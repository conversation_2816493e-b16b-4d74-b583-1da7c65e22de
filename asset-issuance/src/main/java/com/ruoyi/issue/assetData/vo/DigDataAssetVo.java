package com.ruoyi.issue.assetData.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel(value = "数据资产VO对象")
public class DigDataAssetVo {

    /**
     * 数据资产ID
     */
    @ApiModelProperty(value = "数据资产ID")
    private Long dataAssetId;

    /** 资产ID */
    @Excel(name = "资产ID")
    @ApiModelProperty(value = "资产ID")
    private Long assetId;

    /** 资产名称 */
    @Excel(name = "资产名称")
    @ApiModelProperty(value = "资产名称")
    private String assetName;

    /** 资产封面 */
    @Excel(name = "资产封面")
    @ApiModelProperty(value = "资产封面")
    private String assetCover;

    /** 资产缩略图URL */
    @Excel(name = "资产缩略图URL")
    @ApiModelProperty(value = "资产缩略图URL")
    private String assetCoverThumbnail;

    /** 资产唯一编号 */
    @Excel(name = "资产唯一编号")
    @ApiModelProperty(value = "资产唯一编号")
    private String assetCode;

    /** 链上HASH */
    @Excel(name = "链上HASH")
    @ApiModelProperty(value = "链上HASH")
    private String chainHash;

    /** 上链时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "上链时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "上链时间")
    private LocalDateTime chainTime;

    /** 是否已兑换权益(0未兑换 1已兑换) */
    @Excel(name = "是否已兑换权益(0未兑换 1已兑换)")
    @ApiModelProperty(value = "是否已兑换权益(0未兑换 1已兑换)")
    private Integer isRedeemed;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态")
    private String statusCd;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;
}
