package com.ruoyi.issue.assetData.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.issue.assetData.mapper.DigDataAssetMapper;
import com.ruoyi.issue.assetData.domain.DigDataAsset;
import com.ruoyi.issue.assetData.service.IDigDataAssetService;

/**
 * 用户数据资产Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DigDataAssetServiceImpl extends ServiceImpl<DigDataAssetMapper, DigDataAsset> implements IDigDataAssetService
{

    private final DigDataAssetMapper digDataAssetMapper;

    /**
     * 查询用户数据资产
     * 
     * @param dataAssetId 用户数据资产主键
     * @return 用户数据资产
     */
    @Override
    public DigDataAsset selectDigDataAssetByDataAssetId(Long dataAssetId)
    {
        return digDataAssetMapper.selectDigDataAssetByDataAssetId(dataAssetId);
    }

    /**
     * 查询用户数据资产列表
     * 
     * @param digDataAsset 用户数据资产
     * @return 用户数据资产
     */
    @Override
    public List<DigDataAsset> selectDigDataAssetList(DigDataAsset digDataAsset)
    {
        return digDataAssetMapper.selectDigDataAssetList(digDataAsset);
    }

    /**
     * 新增用户数据资产
     * 
     * @param digDataAsset 用户数据资产
     * @return 结果
     */
    @Override
    public int insertDigDataAsset(DigDataAsset digDataAsset)
    {
        return digDataAssetMapper.insertDigDataAsset(digDataAsset);
    }

    /**
     * 修改用户数据资产
     * 
     * @param digDataAsset 用户数据资产
     * @return 结果
     */
    @Override
    public int updateDigDataAsset(DigDataAsset digDataAsset)
    {
        return digDataAssetMapper.updateDigDataAsset(digDataAsset);
    }

    /**
     * 批量删除用户数据资产
     * 
     * @param dataAssetIds 需要删除的用户数据资产主键
     * @return 结果
     */
    @Override
    public int deleteDigDataAssetByDataAssetIds(Long[] dataAssetIds)
    {
        return digDataAssetMapper.deleteDigDataAssetByDataAssetIds(dataAssetIds);
    }

    /**
     * 删除用户数据资产信息
     * 
     * @param dataAssetId 用户数据资产主键
     * @return 结果
     */
    @Override
    public int deleteDigDataAssetByDataAssetId(Long dataAssetId)
    {
        return digDataAssetMapper.deleteDigDataAssetByDataAssetId(dataAssetId);
    }
}
