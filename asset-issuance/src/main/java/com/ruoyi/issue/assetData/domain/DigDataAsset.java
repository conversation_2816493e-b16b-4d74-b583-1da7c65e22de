package com.ruoyi.issue.assetData.domain;

import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.issue.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;

/**
 * 用户数据资产对象 dig_data_asset
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@Data
@ApiModel(value = "用户数据资产对象")
public class DigDataAsset extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 数据资产ID */
    @ApiModelProperty(value = "数据资产ID")
    private Long dataAssetId;

    /** 所属用户ID */
    @Excel(name = "所属用户ID")
    @ApiModelProperty(value = "所属用户ID")
    private Long userId;

    /** 获取方式 */
    @Excel(name = "获取方式")
    @ApiModelProperty(value = "获取方式")
    private String acquireType;

    /** 获取记录ID */
    @Excel(name = "获取记录ID")
    @ApiModelProperty(value = "获取记录ID")
    private Long acquireRecordId;

    /** 资产ID */
    @Excel(name = "资产ID")
    @ApiModelProperty(value = "资产ID")
    private Long assetId;

    /** 资产名称 */
    @Excel(name = "资产名称")
    @ApiModelProperty(value = "资产名称")
    private String assetName;

    /** 资产唯一编号 */
    @Excel(name = "资产唯一编号")
    @ApiModelProperty(value = "资产唯一编号")
    private String assetCode;

    /** 链上ID */
    @Excel(name = "链上ID")
    @ApiModelProperty(value = "链上ID")
    private String chainId;

    /** 链上HASH */
    @Excel(name = "链上HASH")
    @ApiModelProperty(value = "链上HASH")
    private String chainHash;

    /** 上链时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "上链时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "上链时间")
    private LocalDateTime chainTime;

    /** 是否已兑换权益(0未兑换 1已兑换) */
    @Excel(name = "是否已兑换权益(0未兑换 1已兑换)")
    @ApiModelProperty(value = "是否已兑换权益(0未兑换 1已兑换)")
    private Integer isRedeemed;

    /** 乐观锁版本号 */
    @Excel(name = "乐观锁版本号")
    @Version
    @ApiModelProperty(value = "乐观锁版本号")
    private Long VERSION;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态")
    private String statusCd;

    /** 状态时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "状态时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "状态时间")
    private LocalDateTime statusDate;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateDate;

    @TableField(exist = false)
    @ApiModelProperty(value = "错误次数")
    private Integer errorNum;

}
