package com.ruoyi.issue.coupon.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.issue.coupon.domain.DigCoupon;
import com.ruoyi.issue.coupon.domain.DigCouponCode;

/**
 * 权益券Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface DigCouponMapper extends BaseMapper<DigCoupon>
{
    /**
     * 查询权益券
     * 
     * @param couponId 权益券主键
     * @return 权益券
     */
    public DigCoupon selectDigCouponByCouponId(Long couponId);

    /**
     * 查询权益券列表
     * 
     * @param digCoupon 权益券
     * @return 权益券集合
     */
    public List<DigCoupon> selectDigCouponList(DigCoupon digCoupon);

    /**
     * 新增权益券
     * 
     * @param digCoupon 权益券
     * @return 结果
     */
    public int insertDigCoupon(DigCoupon digCoupon);

    /**
     * 修改权益券
     * 
     * @param digCoupon 权益券
     * @return 结果
     */
    public int updateDigCoupon(DigCoupon digCoupon);

    /**
     * 删除权益券
     * 
     * @param couponId 权益券主键
     * @return 结果
     */
    public int deleteDigCouponByCouponId(Long couponId);

    /**
     * 批量删除权益券
     * 
     * @param couponIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDigCouponByCouponIds(Long[] couponIds);

    void batchInsert(List<DigCouponCode> couponCodes);
}
