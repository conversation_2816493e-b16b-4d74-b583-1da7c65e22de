package com.ruoyi.issue.coupon.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.issue.common.constant.Constants;
import com.ruoyi.issue.coupon.domain.DigCouponCode;
import com.ruoyi.issue.coupon.mapper.DigCouponCodeMapper;
import com.ruoyi.issue.coupon.vo.DigCouponCodeVo;
import com.ruoyi.issue.coupon.vo.DigCouponInventoryVo;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import com.ruoyi.scwt.sop.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.issue.coupon.mapper.DigCouponMapper;
import com.ruoyi.issue.coupon.domain.DigCoupon;
import com.ruoyi.issue.coupon.service.IDigCouponService;

/**
 * 权益券Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DigCouponServiceImpl extends ServiceImpl<DigCouponMapper, DigCoupon> implements IDigCouponService {

    private final DigCouponMapper digCouponMapper;

    private final DigCouponCodeMapper digCouponCodeMapper;

    private final AttachmentInfoService attachmentInfoService;

    /**
     * 查询权益券
     *
     * @param couponId 权益券主键
     * @return 权益券
     */
    @Override
    public DigCoupon selectDigCouponByCouponId(Long couponId) {
        DigCoupon digCoupon = digCouponMapper.selectDigCouponByCouponId(couponId);
        if (digCoupon != null) {
            digCoupon.setCouponCover(attachmentInfoService.getObjectUrl(digCoupon.getCouponCover()));
        }
        return digCoupon;
    }

    /**
     * 查询权益券列表
     *
     * @param digCoupon 权益券
     * @return 权益券
     */
    @Override
    public List<DigCoupon> selectDigCouponList(DigCoupon digCoupon) {
        List<DigCoupon> digCoupons = digCouponMapper.selectDigCouponList(digCoupon);
        for (DigCoupon item : digCoupons) {
            item.setCouponCover(attachmentInfoService.getObjectUrl(item.getCouponCover()));
            Long count = digCouponCodeMapper.selectCount(Wrappers.<DigCouponCode>lambdaQuery().eq(DigCouponCode::getCouponId, item.getCouponId()));
            item.setIsGenerated(count > 0);
        }
        return digCoupons;
    }

    /**
     * 新增权益券
     *
     * @param digCoupon 权益券
     * @return 结果
     */
    @Override
    public int insertDigCoupon(DigCoupon digCoupon) {
        return digCouponMapper.insertDigCoupon(digCoupon);
    }

    /**
     * 修改权益券
     *
     * @param digCoupon 权益券
     * @return 结果
     */
    @Override
    public AjaxResult updateDigCoupon(DigCoupon digCoupon) {
        DigCoupon coupon = baseMapper.selectDigCouponByCouponId(digCoupon.getCouponId());
        Long count = digCouponCodeMapper.selectCount(Wrappers.<DigCouponCode>lambdaQuery().eq(DigCouponCode::getCouponId, coupon.getCouponId()));
        if (count > 0) {
            return AjaxResult.error("已经生成了库存，不允许修改！");
        }
        return AjaxResult.success(digCouponMapper.updateDigCoupon(digCoupon));
    }

    /**
     * 批量删除权益券
     *
     * @param couponIds 需要删除的权益券主键
     * @return 结果
     */
    @Override
    public int deleteDigCouponByCouponIds(Long[] couponIds) {
        return digCouponMapper.deleteDigCouponByCouponIds(couponIds);
    }

    /**
     * 删除权益券信息
     *
     * @param couponId 权益券主键
     * @return 结果
     */
    @Override
    public int deleteDigCouponByCouponId(Long couponId) {
        return digCouponMapper.deleteDigCouponByCouponId(couponId);
    }

    @Override
    public AjaxResult consumeCoupon(Long couponId, Long userId) {
//        return digCouponMapper.consumeCoupon(couponId, userId);
        return null;
    }

    @Override
    public AjaxResult generate(DigCoupon digCoupon) {
        DigCoupon coupon = baseMapper.selectDigCouponByCouponId(digCoupon.getCouponId());
        Long count = digCouponCodeMapper.selectCount(Wrappers.<DigCouponCode>lambdaQuery().eq(DigCouponCode::getCouponId, coupon.getCouponId()));
        if (count >= coupon.getQuantity()) {
            return AjaxResult.error("已生成了" + coupon.getQuantity() + "张券,不能重复生成！");
        }
        List<DigCouponCode> couponCodes = new ArrayList<>();
        for (int i = 0; i < coupon.getQuantity() - count; i++) {
            DigCouponCode couponCode = new DigCouponCode();
            couponCode.setCouponId(coupon.getCouponId());
            couponCode.setCouponCode(DigCouponCode.generateCouponCode(coupon.getCouponId()));
            couponCode.setIsUsed(Constants.COUPON_INVENTORY_STATE_ENABLE);
            couponCode.setGenerateTime(LocalDateTime.now());
            couponCodes.add(couponCode);
//            digCouponCodeMapper.insert(couponCode);
        }
        baseMapper.batchInsert(couponCodes);
        return AjaxResult.success("生成成功！");
    }

    @Override
    public List<DigCouponInventoryVo> listInventory(DigCoupon digCoupon) {
        List<DigCoupon> digCouponList = baseMapper.selectList(Wrappers.<DigCoupon>lambdaQuery()
                .eq(StringUtils.isNotBlank(digCoupon.getCouponName()), DigCoupon::getCouponName, digCoupon.getCouponName())
                .eq(DigCoupon::getStatusCd, Constants.GENERAL_STATE_ENABLE));
        List<DigCouponInventoryVo> digCouponInventoryVos = new ArrayList<>();
        digCouponList.forEach(item -> {
            Long count = digCouponCodeMapper.selectCount(Wrappers.<DigCouponCode>lambdaQuery().eq(DigCouponCode::getCouponId, item.getCouponId()).eq(DigCouponCode::getIsUsed, Constants.COUPON_INVENTORY_STATE_ENABLE));
            if (count > 0) {
                DigCouponInventoryVo vo = new DigCouponInventoryVo();
                vo.setCouponId(item.getCouponId());
                vo.setCouponName(item.getCouponName());
                vo.setInventory(count);
                digCouponInventoryVos.add(vo);
            }
        });
        return digCouponInventoryVos;
    }

    @Override
    public void lockInventory(Long couponId, Integer quantity, String lockType, Long lockRelId) {
        Long count = digCouponCodeMapper.selectCount(Wrappers.<DigCouponCode>lambdaQuery().eq(DigCouponCode::getCouponId, couponId).eq(DigCouponCode::getIsUsed, Constants.COUPON_INVENTORY_STATE_ENABLE));
        if (count >= quantity) {
            digCouponCodeMapper.update(null, Wrappers.<DigCouponCode>lambdaUpdate()
                    .set(DigCouponCode::getIsUsed, Constants.COUPON_INVENTORY_STATE_LOCK)
                    .set(DigCouponCode::getLockType, lockType)
                    .set(DigCouponCode::getLockRelId, lockRelId)
                    .eq(DigCouponCode::getCouponId, couponId)
                    .eq(DigCouponCode::getIsUsed, Constants.COUPON_INVENTORY_STATE_ENABLE)
                    .last("limit " + quantity));
        } else {
            throw new RuntimeException("库存不足！");
        }

    }

    @Override
    public void unlockInventory(String lockType, Long lockRelId) {
        digCouponCodeMapper.update(null, Wrappers.<DigCouponCode>lambdaUpdate()
                .set(DigCouponCode::getIsUsed, Constants.COUPON_INVENTORY_STATE_ENABLE)
                .set(DigCouponCode::getLockType, null)
                .set(DigCouponCode::getLockRelId, null)
                .isNull(DigCouponCode::getUserId)
                .eq(DigCouponCode::getIsUsed, Constants.COUPON_INVENTORY_STATE_LOCK)
                .eq(DigCouponCode::getLockType, lockType)
                .eq(DigCouponCode::getLockRelId, lockRelId)
        );
    }

    @Override
    public List<DigCouponCode> selectCouponCodeList(String lockType, Long lockRelId) {
        return digCouponCodeMapper.selectList(Wrappers.<DigCouponCode>lambdaQuery()
                .eq(DigCouponCode::getIsUsed, Constants.COUPON_INVENTORY_STATE_LOCK)
                .isNull(DigCouponCode::getUserId)
                .eq(DigCouponCode::getLockType, lockType)
                .eq(DigCouponCode::getLockRelId, lockRelId));
    }

    @Override
    public int updateCouponCodeById(DigCouponCode couponCode) {
        return digCouponCodeMapper.updateCoponCodeById(couponCode);
    }

    @Override
    public Long getUserCouponCount(Long userId) {
        return digCouponCodeMapper.selectCount(Wrappers.<DigCouponCode>lambdaQuery()
                .eq(DigCouponCode::getUserId, userId)
                .eq(DigCouponCode::getIsUsed, Constants.COUPON_INVENTORY_STATE_LOCK));
    }

    @Override
    public List<DigCouponCodeVo> selectDigCouponListByUserId(DigCoupon digCoupon, Long userId) {
        List<Long> collect = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(digCoupon) && ObjectUtils.isNotEmpty(digCoupon.getCouponName())) {
            collect = digCouponMapper.selectList(Wrappers.<DigCoupon>lambdaQuery()
                            .like(DigCoupon::getCouponName, digCoupon.getCouponName()))
                    .stream().map(DigCoupon::getCouponId).collect(Collectors.toList());
        }
        List<DigCouponCode> digCouponCodes = digCouponCodeMapper.selectList(Wrappers.<DigCouponCode>lambdaQuery()
                .in(collect.size() > 0, DigCouponCode::getCouponId, collect)
                .eq(DigCouponCode::getUserId, userId)
                .eq(DigCouponCode::getIsUsed, Constants.COUPON_INVENTORY_STATE_LOCK));
        List<DigCouponCodeVo> digCouponCodeVos = new ArrayList<>();
        digCouponCodes.forEach(item -> {
            DigCouponCodeVo digCouponCodeVo = new DigCouponCodeVo();
            BeanUtil.copyProperties(item, digCouponCodeVo);
            DigCoupon coupon = digCouponMapper.selectDigCouponByCouponId(item.getCouponId());
            BeanUtil.copyProperties(coupon, digCouponCodeVo);
            digCouponCodeVo.setCouponCover(attachmentInfoService.getObjectUrl(coupon.getCouponCover()));
            digCouponCodeVos.add(digCouponCodeVo);
        });
        return digCouponCodeVos;
    }

    @Override
    public DigCouponCodeVo getCouponInfo(String couponCode) {
        DigCouponCode item = digCouponCodeMapper.selectOne(Wrappers.<DigCouponCode>lambdaQuery()
                .eq(DigCouponCode::getCouponCode, couponCode));
        DigCouponCodeVo digCouponCodeVo = new DigCouponCodeVo();
        BeanUtil.copyProperties(item, digCouponCodeVo);
        DigCoupon coupon = digCouponMapper.selectDigCouponByCouponId(item.getCouponId());
        BeanUtil.copyProperties(coupon, digCouponCodeVo);
        digCouponCodeVo.setCouponCover(attachmentInfoService.getObjectUrl(coupon.getCouponCover()));
        return digCouponCodeVo;
    }
}
