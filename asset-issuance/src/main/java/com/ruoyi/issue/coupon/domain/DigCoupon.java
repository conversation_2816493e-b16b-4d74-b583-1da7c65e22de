package com.ruoyi.issue.coupon.domain;

import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.issue.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;

/**
 * 权益券对象 dig_coupon
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
@ApiModel(value = "权益券对象")
public class DigCoupon extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 权益券ID */
    @ApiModelProperty(value = "权益券ID")
    private Long couponId;

    /** 权益券类型 */
    @Excel(name = "权益券类型")
    @ApiModelProperty(value = "权益券类型")
    private String couponType;

    /** 权益券名称 */
    @Excel(name = "权益券名称")
    @ApiModelProperty(value = "权益券名称")
    private String couponName;

    /** 权益券封面URL */
    @Excel(name = "权益券封面URL")
    @ApiModelProperty(value = "权益券封面URL")
    private String couponCover;

    /** 数量 */
    @Excel(name = "数量")
    @ApiModelProperty(value = "数量")
    private Long quantity;

    /** 固定有效期开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "固定有效期开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "固定有效期开始时间")
    private LocalDateTime startTime;

    /** 固定有效期结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "固定有效期结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "固定有效期结束时间")
    private LocalDateTime endTime;

    /** 说明 */
    @Excel(name = "说明")
    @ApiModelProperty(value = "说明")
    private String description;

    /** 状态代码 */
    @Excel(name = "状态代码")
    @ApiModelProperty(value = "状态代码")
    private String statusCd;

    /** 状态时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "状态时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "状态时间")
    private LocalDateTime statusDate;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty(value = "创建人")
    private String createStaff;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    /** 修改人 */
    @Excel(name = "修改人")
    @ApiModelProperty(value = "修改人")
    private String updateStaff;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateDate;

    @ApiModelProperty(value = "备注")
    private String remark;

    /** 是否已经生成 */
    @TableField(exist = false)
    @ApiModelProperty(value = "是否已经生成")
    private Boolean isGenerated;
}
