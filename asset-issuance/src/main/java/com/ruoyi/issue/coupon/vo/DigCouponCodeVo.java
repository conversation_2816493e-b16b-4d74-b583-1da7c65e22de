package com.ruoyi.issue.coupon.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DigCouponCodeVo {
    /** 权益券编号ID */
    private Long couponCodeId;

    /** 权益券ID */
    private Long couponId;

    /** 权益券名称 */
    @ApiModelProperty(value = "权益券名称")
    private String couponName;

    /** 权益券封面URL */
    @ApiModelProperty(value = "权益券封面")
    private String couponCover;

    @ApiModelProperty(value = "说明")
    private String description;

    /** 权益券唯一编号 */
    private String couponCode;

    /** 绑定用户ID */
    private Long userId;

    /** 是否使用 */
    private String isUsed;

    /** 生成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime generateTime;

    /** 使用时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime useTime;

    /** 固定有效期开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /** 固定有效期结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "状态代码")
    private String statusCd;
}
