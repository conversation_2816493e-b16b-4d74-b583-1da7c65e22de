package com.ruoyi.issue.coupon.domain;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import com.baomidou.mybatisplus.annotation.Version;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.issue.common.entity.BaseEntity;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 用户权益券编号表对象 dig_coupon_code
 */
@Data
public class DigCouponCode extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 权益券编号ID */
    @Excel(name = "权益券编号ID")
    private Long couponCodeId;

    /** 权益券ID */
    private Long couponId;

    /** 权益券唯一编号 */
    @Excel(name = "权益券唯一编号")
    private String couponCode;

    /** 绑定用户ID */
    @Excel(name = "绑定用户ID")
    private Long userId;

    /** 是否使用 */
    @Excel(name = "是否使用")
    private String isUsed;

    /** 锁定类型 */
    @Excel(name = "锁定类型")
    private String lockType;

    /** 锁定关联ID */
    @Excel(name = "锁定关联ID")
    private Long lockRelId;

    /** 生成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "生成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime generateTime;

    /** 使用时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "使用时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime useTime;

    /** 状态代码 */
    @ApiModelProperty(value = "状态代码")
    private String status;

    /** 乐观锁版本号 */
    @Version
    @ApiModelProperty(value = "乐观锁版本号", hidden = true)
    private Integer version;

    public static String generateCouponCode(Long couponId) {
        String time = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String traceId = UUID.randomUUID().toString().substring(0, 10).toUpperCase().replace("-", "");
        return "LY" + couponId + time + traceId;
    }
}
