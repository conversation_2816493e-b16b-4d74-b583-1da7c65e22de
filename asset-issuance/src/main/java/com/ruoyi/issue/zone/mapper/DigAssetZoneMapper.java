package com.ruoyi.issue.zone.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.issue.zone.domain.DigAssetZone;
import org.apache.ibatis.annotations.Mapper;

/**
 * 资产专区Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@Mapper
public interface DigAssetZoneMapper extends BaseMapper<DigAssetZone>
{
    /**
     * 查询资产专区
     * 
     * @param zoneId 资产专区主键
     * @return 资产专区
     */
    public DigAssetZone selectDigAssetZoneByZoneId(Long zoneId);

    /**
     * 查询资产专区列表
     * 
     * @param digAssetZone 资产专区
     * @return 资产专区集合
     */
    public List<DigAssetZone> selectDigAssetZoneList(DigAssetZone digAssetZone);

    /**
     * 新增资产专区
     * 
     * @param digAssetZone 资产专区
     * @return 结果
     */
    public int insertDigAssetZone(DigAssetZone digAssetZone);

    /**
     * 修改资产专区
     * 
     * @param digAssetZone 资产专区
     * @return 结果
     */
    public int updateDigAssetZone(DigAssetZone digAssetZone);

    /**
     * 删除资产专区
     * 
     * @param zoneId 资产专区主键
     * @return 结果
     */
    public int deleteDigAssetZoneByZoneId(Long zoneId);

    /**
     * 批量删除资产专区
     * 
     * @param zoneIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDigAssetZoneByZoneIds(Long[] zoneIds);

    /**
     * 查询资产专区列表
     *
     * @param operatorName 运营人员姓名
     * @return 专区列表
     */
    List<DigAssetZone> digZoneOperateList(String operatorName);

    /**
     * 专区管理列表
     *
     * @param adminName 管理员姓名
     * @return 专区列表
     */
    List<DigAssetZone> digZoneManageList(String adminName);
}
