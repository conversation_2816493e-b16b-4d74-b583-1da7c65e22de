package com.ruoyi.issue.zone.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.issue.common.constant.Constants;
import com.ruoyi.issue.zone.domain.DigAssetZoneRel;
import com.ruoyi.issue.zone.mapper.DigAssetZoneRelMapper;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.ruoyi.issue.zone.mapper.DigAssetZoneMapper;
import com.ruoyi.issue.zone.domain.DigAssetZone;
import com.ruoyi.issue.zone.service.DigAssetZoneService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * 资产专区Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DigAssetZoneServiceImpl extends ServiceImpl<DigAssetZoneMapper, DigAssetZone> implements DigAssetZoneService {

    private final DigAssetZoneMapper digAssetZoneMapper;
    private final DigAssetZoneRelMapper digAssetZoneRelMapper;

    private final AttachmentInfoService attachmentInfoService;

    /**
     * 查询资产专区
     *
     * @param zoneId 资产专区主键
     * @return 资产专区
     */
    @Override
    public DigAssetZone selectDigAssetZoneByZoneId(Long zoneId) {
        return digAssetZoneMapper.selectDigAssetZoneByZoneId(zoneId);
    }

    /**
     * 查询资产专区列表
     *
     * @param digAssetZone 资产专区
     * @return 资产专区
     */
    @Override
    public List<DigAssetZone> selectDigAssetZoneList(DigAssetZone digAssetZone) {
        List<DigAssetZone> list = digAssetZoneMapper.selectDigAssetZoneList(digAssetZone);
        list.forEach(item -> item.setZoneCover(attachmentInfoService.getObjectUrl(item.getZoneCover())));
        return list;
    }

    /**
     * 新增资产专区
     *
     * @param digAssetZone 资产专区
     * @return 结果
     */
    @Override
    public int insertDigAssetZone(DigAssetZone digAssetZone) {
        return digAssetZoneMapper.insertDigAssetZone(digAssetZone);
    }

    /**
     * 修改资产专区
     *
     * @param digAssetZone 资产专区
     * @return 结果
     */
    @Override
    public int updateDigAssetZone(DigAssetZone digAssetZone) {
        return digAssetZoneMapper.updateDigAssetZone(digAssetZone);
    }

    /**
     * 批量删除资产专区
     *
     * @param zoneIds 需要删除的资产专区主键
     * @return 结果
     */
    @Override
    public int deleteDigAssetZoneByZoneIds(Long[] zoneIds) {
        return digAssetZoneMapper.deleteDigAssetZoneByZoneIds(zoneIds);
    }

    /**
     * 删除资产专区信息
     *
     * @param zoneId 资产专区主键
     * @return 结果
     */
    @Override
    public int deleteDigAssetZoneByZoneId(Long zoneId) {
        return digAssetZoneMapper.deleteDigAssetZoneByZoneId(zoneId);
    }

    /**
     * 查询资产专区列表
     *
     * @param operatorName 运营人员姓名
     * @return 专区列表
     */
    @Override
    public List<DigAssetZone> digZoneOperateList(String operatorName) {
        return digAssetZoneMapper.digZoneOperateList(operatorName);
    }

    /**
     * 查询资产专区列表
     *
     * @param zoneName 专区名称
     * @param userId   用户ID
     * @return 专区列表
     */
    @Override
    public List<DigAssetZone> digZoneNameList(String zoneName, Long userId) {
        return digAssetZoneMapper.selectList(Wrappers.<DigAssetZone>lambdaQuery()
                .like(StringUtils.isNotBlank(zoneName), DigAssetZone::getZoneName, zoneName)
                .eq(DigAssetZone::getOperatorId, userId));
    }

    @Override
    public List<DigAssetZone> digZoneManageList(String adminName) {
        return digAssetZoneMapper.digZoneManageList(adminName);
    }

    @Override
    public void insertRel(DigAssetZoneRel zoneRel) {
        digAssetZoneRelMapper.insert(zoneRel);
    }

    @Override
    public String selectZoneNameByAssetId(Long assetId) {
        DigAssetZoneRel digAssetZoneRel = digAssetZoneRelMapper.selectOne(Wrappers.<DigAssetZoneRel>lambdaQuery().eq(DigAssetZoneRel::getAssetId, assetId));
        if (digAssetZoneRel != null) {
            DigAssetZone digAssetZone = baseMapper.selectDigAssetZoneByZoneId(digAssetZoneRel.getZoneId());
            if (digAssetZone != null) {
                return digAssetZone.getZoneName();
            }
        }
        return null;
    }

    @Override
    public List<DigAssetZone> selectNameList() {
        return baseMapper.selectList(Wrappers.<DigAssetZone>lambdaQuery().select(DigAssetZone::getZoneName, DigAssetZone::getZoneId).eq(DigAssetZone::getStatusCd, Constants.GENERAL_STATE_ENABLE));
    }

    @Override
    public List<Long> selectOperatorIdsByRoleIds(List<SysRole> roles, Long userId) {
        if (roles == null || roles.isEmpty()) {
            return null;
        }
        List<String> collect = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        // 判断roles中是否有admin权限
        if (collect.contains(Constants.ROLE_COMMON_ADMIN)) {
            return baseMapper.selectList(Wrappers.<DigAssetZone>lambdaQuery()
                            .eq(DigAssetZone::getStatusCd, Constants.GENERAL_STATE_ENABLE))
                    .stream().map(DigAssetZone::getOperatorId).collect(Collectors.toList());
        } else if (collect.contains(Constants.ROLE_DIG_ZONE_MANAGE)) {
            return baseMapper.selectList(Wrappers.<DigAssetZone>lambdaQuery()
                            .eq(DigAssetZone::getStatusCd, Constants.GENERAL_STATE_ENABLE)
                            .eq(DigAssetZone::getAdminId, userId))
                    .stream().map(DigAssetZone::getOperatorId).collect(Collectors.toList());
        } else if (collect.contains(Constants.ROLE_DIG_ZONE_OPERATE)) {
            return baseMapper.selectList(Wrappers.<DigAssetZone>lambdaQuery()
                            .eq(DigAssetZone::getStatusCd, Constants.GENERAL_STATE_ENABLE)
                            .eq(DigAssetZone::getOperatorId, userId))
                    .stream().map(DigAssetZone::getOperatorId).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public void removeRel(Long assetId) {
        digAssetZoneRelMapper.delete(Wrappers.<DigAssetZoneRel>lambdaQuery().eq(DigAssetZoneRel::getAssetId, assetId));
    }
}
