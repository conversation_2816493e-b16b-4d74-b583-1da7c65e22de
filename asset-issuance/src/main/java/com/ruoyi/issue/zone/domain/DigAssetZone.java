package com.ruoyi.issue.zone.domain;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ruoyi.issue.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;

/**
 * 资产专区对象 dig_asset_zone
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
@Data
@ApiModel(value = "资产专区对象")
public class DigAssetZone  extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 专区ID，主键自增 */
    @ApiModelProperty(value = "专区ID，主键自增")
    private Long zoneId;

    /** 专区封面 URL */
    @Excel(name = "专区封面 URL")
    @ApiModelProperty(value = "专区封面 URL")
    private String zoneCover;

    /** 专区名称 */
    @Excel(name = "专区名称")
    @ApiModelProperty(value = "专区名称")
    private String zoneName;

    /** 专区描述 */
    @Excel(name = "专区描述")
    @ApiModelProperty(value = "专区描述")
    private String zoneDesc;

    /** 管理人员ID */
    @Excel(name = "管理人员ID")
    @ApiModelProperty(value = "管理人员ID")
    private Long adminId;

    /** 管理人员姓名 */
    @Excel(name = "管理人员姓名")
    @ApiModelProperty(value = "管理人员姓名")
    private String adminName;

    /** 管理人员联系电话 */
    @Excel(name = "管理人员联系电话")
    @ApiModelProperty(value = "管理人员联系电话")
    private String adminPhone;

    /** 运营人员ID */
    @Excel(name = "运营人员ID")
    @ApiModelProperty(value = "运营人员ID")
    private Long operatorId;

    /** 运营人员姓名 */
    @Excel(name = "运营人员姓名")
    @ApiModelProperty(value = "运营人员姓名")
    private String operatorName;

    /** 运营人员联系电话 */
    @Excel(name = "运营人员联系电话")
    @ApiModelProperty(value = "运营人员联系电话")
    private String operatorPhone;

    /** 状态代码 */
    @Excel(name = "状态代码")
    @ApiModelProperty(value = "状态代码")
    private String statusCd;

    /** 状态时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "状态时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "状态时间")
    private LocalDateTime statusDate;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty(value = "创建人")
    private String createStaff;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    /** 修改人 */
    @Excel(name = "修改人")
    @ApiModelProperty(value = "修改人")
    private String updateStaff;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateDate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "银联收款商编", hidden = true)
    private String unionPayMerchantCode;

    @ApiModelProperty(value = "收款账号ID", hidden = true)
    private Long payAccountId ;

}
