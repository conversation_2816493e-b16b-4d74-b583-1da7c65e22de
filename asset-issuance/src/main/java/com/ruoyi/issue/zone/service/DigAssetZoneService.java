package com.ruoyi.issue.zone.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.issue.zone.domain.DigAssetZone;
import com.ruoyi.issue.zone.domain.DigAssetZoneRel;

/**
 * 资产专区Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-10
 */
public interface DigAssetZoneService extends IService<DigAssetZone>
{
    /**
     * 查询资产专区
     * 
     * @param zoneId 资产专区主键
     * @return 资产专区
     */
    public DigAssetZone selectDigAssetZoneByZoneId(Long zoneId);

    /**
     * 查询资产专区列表
     * 
     * @param digAssetZone 资产专区
     * @return 资产专区集合
     */
    public List<DigAssetZone> selectDigAssetZoneList(DigAssetZone digAssetZone);

    /**
     * 新增资产专区
     * 
     * @param digAssetZone 资产专区
     * @return 结果
     */
    public int insertDigAssetZone(DigAssetZone digAssetZone);

    /**
     * 修改资产专区
     * 
     * @param digAssetZone 资产专区
     * @return 结果
     */
    public int updateDigAssetZone(DigAssetZone digAssetZone);

    /**
     * 批量删除资产专区
     * 
     * @param zoneIds 需要删除的资产专区主键集合
     * @return 结果
     */
    public int deleteDigAssetZoneByZoneIds(Long[] zoneIds);

    /**
     * 删除资产专区信息
     * 
     * @param zoneId 资产专区主键
     * @return 结果
     */
    public int deleteDigAssetZoneByZoneId(Long zoneId);

    /**
     * 查询资产专区列表
     *
     * @param operatorName 运营人员姓名
     * @return 专区列表
     */
    List<DigAssetZone> digZoneOperateList(String operatorName);


    List<DigAssetZone> digZoneNameList(String zoneName, Long userId);

    List<DigAssetZone> digZoneManageList(String adminName);

    /**
     * 插入专区关系
     *
     * @param zoneRel 专区关系
     */
    void insertRel(DigAssetZoneRel zoneRel);

    /**
     * 根据资产ID查询专区名称
     *
     * @param assetId 资产ID
     * @return 专区名称
     */
    String selectZoneNameByAssetId(Long assetId);

    List<DigAssetZone> selectNameList();

    /**
     * 根据权限列表获取专区运营人员ID集合
     */
    List<Long> selectOperatorIdsByRoleIds(List<SysRole> roles,Long userId);

    void removeRel(Long assetId);
}
