package com.ruoyi.issue.quartz.service;

import com.ruoyi.issue.quartz.domain.NewJob;
import org.quartz.SchedulerException;
import org.springframework.transaction.annotation.Transactional;

/**
 * 定时任务调度信息信息 服务层
 * 
 * <AUTHOR>
 */
public interface JobService
{

    /**
     * 新增任务
     * 
     * @param job 调度信息
     * @return 结果
     */
    public int insertJob(NewJob job) throws Exception;

    @Transactional(rollbackFor = Exception.class)
    int changeStatus(NewJob job) throws SchedulerException;

    @Transactional(rollbackFor = Exception.class)
    int resumeJob(NewJob job) throws SchedulerException;

    @Transactional(rollbackFor = Exception.class)
    int pauseJob(NewJob job) throws SchedulerException;

    /**
     * 删除任务后，所对应的trigger也将被删除
     *
     * @param job 调度信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteJob(NewJob job) throws SchedulerException;

}
