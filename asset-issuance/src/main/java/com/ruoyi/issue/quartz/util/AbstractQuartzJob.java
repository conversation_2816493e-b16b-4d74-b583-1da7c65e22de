package com.ruoyi.issue.quartz.util;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.ScheduleConstants;
import com.ruoyi.common.utils.ExceptionUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.issue.quartz.domain.NewJob;
import com.ruoyi.issue.quartz.domain.NewJobLog;
import com.ruoyi.issue.quartz.service.JobLogService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;

/**
 * 抽象quartz调用
 *
 * <AUTHOR>
 */
public abstract class AbstractQuartzJob implements Job
{
    private static final Logger log = LoggerFactory.getLogger(AbstractQuartzJob.class);

    /**
     * 线程本地变量
     */
    private static ThreadLocal<Date> threadLocal = new ThreadLocal<>();

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException
    {
        NewJob newJob = new NewJob();
        BeanUtils.copyBeanProp(newJob, context.getMergedJobDataMap().get(ScheduleConstants.TASK_PROPERTIES));
        try
        {
            before(context, newJob);
            if (newJob != null)
            {
                doExecute(context, newJob);
            }
            after(context, newJob, null);
        }
        catch (Exception e)
        {
            log.error("任务执行异常  - ：", e);
            after(context, newJob, e);
        }
    }

    /**
     * 执行前
     *
     * @param context 工作执行上下文对象
     * @param newJob 系统计划任务
     */
    protected void before(JobExecutionContext context, NewJob newJob)
    {
        threadLocal.set(new Date());
    }

    /**
     * 执行后
     *
     * @param context 工作执行上下文对象
     * @param newJob 系统计划任务
     */
    protected void after(JobExecutionContext context, NewJob newJob, Exception e)
    {
        Date startTime = threadLocal.get();
        threadLocal.remove();

        final NewJobLog newJobLog = new NewJobLog();
        newJobLog.setJobName(newJob.getJobName());
        newJobLog.setJobGroup(newJob.getJobGroup());
        newJobLog.setInvokeTarget(newJob.getInvokeTarget());
        newJobLog.setStartTime(startTime);
        newJobLog.setStopTime(new Date());
        long runMs = newJobLog.getStopTime().getTime() - newJobLog.getStartTime().getTime();
        newJobLog.setJobMessage(newJobLog.getJobName() + " 总共耗时：" + runMs + "毫秒");
        if (e != null)
        {
            newJobLog.setStatus(Constants.FAIL);
            String errorMsg = StringUtils.substring(ExceptionUtil.getExceptionMessage(e), 0, 2000);
            newJobLog.setExceptionInfo(errorMsg);
        }
        else
        {
            newJobLog.setStatus(Constants.SUCCESS);
        }

        // 写入数据库当中
        SpringUtils.getBean(JobLogService.class).addJobLog(newJobLog);
    }

    /**
     * 执行方法，由子类重载
     *
     * @param context 工作执行上下文对象
     * @param newJob 系统计划任务
     * @throws Exception 执行过程中的异常
     */
    protected abstract void doExecute(JobExecutionContext context, NewJob newJob) throws Exception;
}
