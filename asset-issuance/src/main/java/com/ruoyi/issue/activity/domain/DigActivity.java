package com.ruoyi.issue.activity.domain;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.issue.common.entity.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;

/**
 * 活动对象 dig_activity
 * 
 * <AUTHOR>
 * @date 2025-07-15
 */
@Data
public class DigActivity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 活动ID */
    private Long activityId;

    /** 活动名称 */
    @Excel(name = "活动名称")
    private String activityName;

    /** 活动封面URL */
    @Excel(name = "活动封面URL")
    private String activityCover;

    /** 活动说明图片 */
    @Excel(name = "活动说明图片")
    private String description;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /** 开奖时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开奖时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime drawTime;

    /** 状态代码 */
    @Excel(name = "状态代码")
    private String statusCd;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createStaff;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDate;

    /** 修改人 */
    @Excel(name = "修改人")
    private String updateStaff;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateDate;

    /** 活动奖品 */
    @Excel(name = "活动奖品")
    @TableField(exist = false)
    private List<DigActivityPrize> prizeList;

    /** 是否已经参与 */
    @Excel(name = "是否已经参与")
    @TableField(exist = false)
    private Boolean isParticipated;

    /** 邀请码 */
    @Excel(name = "邀请码")
    @TableField(exist = false)
    private String inviteCode;
}
