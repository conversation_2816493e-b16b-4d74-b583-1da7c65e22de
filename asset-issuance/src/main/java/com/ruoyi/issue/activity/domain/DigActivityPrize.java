package com.ruoyi.issue.activity.domain;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.issue.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;

/**
 * 活动奖品对象 dig_activity_prize
 * 
 * <AUTHOR>
 * @date 2025-01-06
 */
@Data
@ApiModel(value = "活动奖品对象")
public class DigActivityPrize extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 奖品ID */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "奖品ID")
    private Long prizeId;

    /** 关联活动ID */
    @Excel(name = "关联活动ID")
    @ApiModelProperty(value = "关联活动ID")
    private Long activityId;

    /** 奖品类型(COUPON:优惠券/ASSET:数字资产/PHYSICAL:实物) */
    @Excel(name = "奖品类型")
    @ApiModelProperty(value = "奖品类型(COUPON:优惠券/ASSET:数字资产/PHYSICAL:实物)")
    private String prizeType;

    /** 关联奖品ID */
    @Excel(name = "关联奖品ID")
    @ApiModelProperty(value = "关联奖品ID")
    private Long prizeRelId;

    /** 奖品名称 */
    @Excel(name = "奖品名称")
    @ApiModelProperty(value = "奖品名称")
    private String prizeName;

    /** 奖品数量 */
    @Excel(name = "奖品数量")
    @ApiModelProperty(value = "奖品数量")
    private Integer quantity;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateDate;
} 