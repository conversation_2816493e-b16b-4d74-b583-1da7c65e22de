package com.ruoyi.issue.activity.service.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.issue.activity.domain.DigActivityParticipation;
import com.ruoyi.issue.activity.domain.DigActivityPrize;
import com.ruoyi.issue.common.config.ActivityConfig;
import com.ruoyi.issue.coupon.domain.DigCoupon;
import com.ruoyi.issue.coupon.vo.DigCouponCodeVo;
import com.ruoyi.scwt.common.util.DataDesensitizationUtil;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import com.ruoyi.system.domain.vo.ActivityRankVo;
import com.ruoyi.issue.coupon.domain.DigCouponCode;
import com.ruoyi.issue.quartz.domain.NewJob;
import com.ruoyi.issue.quartz.service.JobService;
import com.ruoyi.issue.quartz.util.CronUtils;
import com.ruoyi.system.domain.DigUserInvitation;
import com.ruoyi.issue.activity.mapper.DigActivityParticipationMapper;
import com.ruoyi.issue.activity.mapper.DigActivityPrizeMapper;
import com.ruoyi.system.mapper.DigUserInvitationMapper;
import com.ruoyi.issue.common.constant.Constants;
import com.ruoyi.issue.coupon.service.impl.DigCouponServiceImpl;
import com.ruoyi.system.mapper.SysUserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import com.ruoyi.issue.activity.mapper.DigActivityMapper;
import com.ruoyi.issue.activity.domain.DigActivity;
import com.ruoyi.issue.activity.service.IDigActivityService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 活动Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DigActivityServiceImpl extends ServiceImpl<DigActivityMapper, DigActivity> implements IDigActivityService {

    private final DigActivityMapper digActivityMapper;
    private final DigActivityPrizeMapper digActivityPrizeMapper;
    private final DigActivityParticipationMapper digActivityParticipationMapper;
    private final DigUserInvitationMapper digUserInvitationMapper;
    private final JobService jobService;
    private final DigCouponServiceImpl digCouponService;
    private final SysUserMapper sysUserMapper;
    private final AttachmentInfoService attachmentInfoService;
    private final ActivityConfig activityConfig;

    private final RedisCache redisCache;

    private final RedissonClient redissonClient;

    /**
     * 查询活动
     *
     * @param activityId 活动主键
     * @return 活动
     */
    @Override
    public DigActivity selectDigActivityByActivityId(Long activityId) {
        DigActivity digActivity = digActivityMapper.selectDigActivityByActivityId(activityId);
        List<DigActivityPrize> prizeList = digActivityPrizeMapper.selectList(Wrappers.<DigActivityPrize>lambdaQuery()
                .eq(DigActivityPrize::getActivityId, digActivity.getActivityId()));
        digActivity.setPrizeList(prizeList);
        return digActivity;
    }

    /**
     * 查询活动列表
     *
     * @param digActivity 活动
     * @return 活动
     */
    @Override
    public List<DigActivity> selectDigActivityList(DigActivity digActivity) {
        return digActivityMapper.selectDigActivityList(digActivity);
    }

    /**
     * 新增活动
     *
     * @param digActivity 活动
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertDigActivity(DigActivity digActivity) {
        digActivity.setStatusCd(Constants.GENERAL_STATE_ENABLE);
        digActivityMapper.insertDigActivity(digActivity);
        List<DigActivityPrize> digActivityPrizes = digActivity.getPrizeList();
        for (DigActivityPrize digActivityPrize : digActivityPrizes) {
            digActivityPrize.setActivityId(digActivity.getActivityId());
            digActivityPrizeMapper.insert(digActivityPrize);
            // 锁定奖品库存
            digCouponService.lockInventory(digActivityPrize.getPrizeRelId(), digActivityPrize.getQuantity(), Constants.COUPON_INVENTORY_SALE_TYPE_ACTIVITY, digActivity.getActivityId());
        }
        try {
            NewJob job = new NewJob();
            job.setJobName(digActivity.getActivityName() + "_" + digActivity.getActivityId() + "_开奖定时任务");
            job.setJobGroup("ACTIVITY_LOTTERY");
            job.setInvokeTarget("ryTask.ryActivityLottery(" + digActivity.getActivityId() + "L)");
            //将rule中的startTime时间转化为cron表达式
            job.setCronExpression(CronUtils.getCronExpression(digActivity.getDrawTime(), 1));
            job.setMisfirePolicy("2");
            job.setConcurrent("1");
            job.setStatus("0");
            job.setCreateBy("system");
            jobService.insertJob(job);
            job.setStatus("0");
            jobService.resumeJob(job);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return 1;
    }

    /**
     * 修改活动
     *
     * @param digActivity 活动
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateDigActivity(DigActivity digActivity) {
        try {
            // 删除活动
            digActivityMapper.deleteDigActivityByActivityId(digActivity.getActivityId());
            // 删除奖品
            digActivityPrizeMapper.delete(Wrappers.<DigActivityPrize>lambdaQuery()
                    .eq(DigActivityPrize::getActivityId, digActivity.getActivityId()));
            // 解锁奖品库存
            digCouponService.unlockInventory(Constants.COUPON_INVENTORY_SALE_TYPE_ACTIVITY, digActivity.getActivityId());
            // 去掉定时任务
            NewJob job = new NewJob();
            job.setJobName("_" + digActivity.getActivityId() + "_开奖定时任务");
            jobService.deleteJob(job);
        } catch (SchedulerException e) {
            throw new RuntimeException(e);
        }
        return insertDigActivity(digActivity);
    }

    /**
     * 批量删除活动
     *
     * @param activityIds 需要删除的活动主键
     * @return 结果
     */
    @Override
    public int deleteDigActivityByActivityIds(Long[] activityIds) {
        return digActivityMapper.deleteDigActivityByActivityIds(activityIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void drawActivity(Long activityId) {
        log.info("开始为活动{}进行开奖", activityId);

        // 1. 检查活动状态，防止重复开奖
        DigActivity activity = digActivityMapper.selectDigActivityByActivityId(activityId);
        if (activity == null) {
            log.error("活动{}不存在", activityId);
            return;
        }

        // 2. 获取奖品列表
        List<DigCouponCode> couponCodeList = digCouponService.selectCouponCodeList(Constants.COUPON_INVENTORY_SALE_TYPE_ACTIVITY, activityId);
        int prizeCount = couponCodeList.size();
        if (prizeCount == 0) {
            log.error("活动{}的奖品列表为空！", activityId);
            return;
        }

        // 3. 获取参与人数
        Long participationCount = digActivityParticipationMapper.selectCount(Wrappers.<DigActivityParticipation>lambdaQuery()
                .eq(DigActivityParticipation::getActivityId, activityId));

        if (participationCount == 0) {
            log.warn("活动{}没有参与者", activityId);
            return;
        }

        log.info("活动{}开奖：参与人数{}，奖品数量{}", activityId, participationCount, prizeCount);

        // 4. 使用优化的随机抽奖算法
        List<Long> winnerIds = drawWinnersOptimized(activityId, participationCount, prizeCount);

        // 5. 分配奖品给中奖者
        assignPrizesToWinners(activityId, winnerIds, couponCodeList);

        log.info("活动{}开奖完成，共{}人中奖", activityId, winnerIds.size());
    }

    /**
     * 优化的随机抽奖算法
     * 使用分页查询 + 随机种子，避免ORDER BY RAND()的性能问题
     */
    private List<Long> drawWinnersOptimized(Long activityId, Long totalParticipants, int winnerCount) {
        List<Long> winnerIds = new ArrayList<>();

        // 使用当前时间作为随机种子，确保公平性
        long seed = System.currentTimeMillis();
        Random random = new Random(seed);

        // 如果参与人数小于等于奖品数量，所有人中奖
        if (totalParticipants <= winnerCount) {
            List<DigActivityParticipation> allParticipants = digActivityParticipationMapper.selectList(
                    Wrappers.<DigActivityParticipation>lambdaQuery()
                            .eq(DigActivityParticipation::getActivityId, activityId)
                            .select(DigActivityParticipation::getParticipationId)
            );
            return allParticipants.stream()
                    .map(DigActivityParticipation::getParticipationId)
                    .collect(Collectors.toList());
        }

        // 使用Fisher-Yates洗牌算法的变种，避免重复选择
        Set<Long> selectedIds = new HashSet<>();
        int attempts = 0;
        int maxAttempts = winnerCount * 10; // 防止无限循环

        while (selectedIds.size() < winnerCount && attempts < maxAttempts) {
            // 生成随机偏移量
            long randomOffset = Math.abs(random.nextLong()) % totalParticipants;

            // 分页查询获取随机位置的参与记录
            DigActivityParticipation randomParticipation = digActivityParticipationMapper.selectOne(
                    Wrappers.<DigActivityParticipation>lambdaQuery()
                            .eq(DigActivityParticipation::getActivityId, activityId)
                            .last("LIMIT 1 OFFSET " + randomOffset)
                            .select(DigActivityParticipation::getParticipationId)
            );

            if (randomParticipation != null && !selectedIds.contains(randomParticipation.getParticipationId())) {
                selectedIds.add(randomParticipation.getParticipationId());
                winnerIds.add(randomParticipation.getParticipationId());
            }

            attempts++;
        }

        // 如果随机选择不够，补充选择
        if (selectedIds.size() < winnerCount) {
            List<Long> remainingIds = digActivityParticipationMapper.selectList(
                    Wrappers.<DigActivityParticipation>lambdaQuery()
                            .eq(DigActivityParticipation::getActivityId, activityId)
                            .notIn(DigActivityParticipation::getParticipationId, selectedIds)
                            .last("LIMIT " + (winnerCount - selectedIds.size()))
                            .select(DigActivityParticipation::getParticipationId)
            ).stream().map(DigActivityParticipation::getParticipationId).collect(Collectors.toList());

            winnerIds.addAll(remainingIds);
        }

        return winnerIds;
    }

    /**
     * 分配奖品给中奖者
     */
    private void assignPrizesToWinners(Long activityId, List<Long> winnerIds, List<DigCouponCode> couponCodeList) {
        if (winnerIds.isEmpty() || couponCodeList.isEmpty()) {
            return;
        }

        // 随机打乱奖品顺序，增加随机性
        Collections.shuffle(couponCodeList);

        // 批量更新中奖记录
        List<DigActivityParticipation> updateList = new ArrayList<>();
        List<DigCouponCode> updateCouponCodeList = new ArrayList<>();
        int prizeIndex = 0;

        for (Long winnerId : winnerIds) {
            if (prizeIndex >= couponCodeList.size()) {
                break; // 奖品不够了
            }

            DigCouponCode couponCode = couponCodeList.get(prizeIndex);

            DigActivityParticipation participation = digActivityParticipationMapper.selectById(winnerId);
            participation.setPrizeId(couponCode.getCouponId());
            participation.setPrizeCode(couponCode.getCouponCode());
            participation.setUpdateDate(LocalDateTime.now());

            updateList.add(participation);

            couponCode.setUserId(participation.getUserId());
            couponCode.setUseTime(participation.getUpdateDate());
//            couponCode.setIsUsed(Constants.COUPON_INVENTORY_STATE_DISABLE);
            updateCouponCodeList.add(couponCode);


            prizeIndex++;
        }

        // 批量更新，提高性能
        if (!updateList.isEmpty()) {
            for (DigActivityParticipation participation : updateList) {
                digActivityParticipationMapper.updateById(participation);
            }
            for (DigCouponCode couponCode : updateCouponCodeList) {
                digCouponService.updateCouponCodeById(couponCode);
            }
        }
    }

    /**
     * 删除活动信息
     *
     * @param activityId 活动主键
     * @return 结果
     */
    @Override
    public int deleteDigActivityByActivityId(Long activityId) {
        return digActivityMapper.deleteDigActivityByActivityId(activityId);
    }

    @Override
    public AjaxResult joinActivity(DigActivityParticipation digActivityParticipation) {
        digActivityParticipationMapper.insert(digActivityParticipation);
        return AjaxResult.success("参与成功！");
    }

    @Override
    public Boolean isParticipated(Long activityId, Long userId) {
        Long count = digActivityParticipationMapper.selectCount(Wrappers.<DigActivityParticipation>lambdaQuery()
                .eq(DigActivityParticipation::getActivityId, activityId)
                .eq(DigActivityParticipation::getUserId, userId));
        return count > 0;
    }

    @Override
    public DigUserInvitation getUserInvitation(String inviteCode, Long userId) {
        DigUserInvitation digUserInvitation = digUserInvitationMapper.selectOne(Wrappers.<DigUserInvitation>lambdaQuery()
                .eq(StringUtils.isNotBlank(inviteCode), DigUserInvitation::getInviteCode, inviteCode)
                .eq(ObjectUtil.isNotEmpty(userId), DigUserInvitation::getUserId, userId));
        return digUserInvitation;
    }

    @Override
    public void insertDigUserInvitation(DigUserInvitation digUserInvitation) {
        digUserInvitationMapper.insert(digUserInvitation);
    }

    @Override
    public List<ActivityRankVo> getInviteRank(Long activityId) {
        DigActivity activity = digActivityMapper.selectDigActivityByActivityId(activityId);

        // 从配置文件中获取需要排除的用户名前缀
        List<String> excludePrefixes = activityConfig.getInviteRank().getExcludePrefixes();

        // 从配置文件中获取排行榜显示数量限制
        Integer displayLimit = activityConfig.getInviteRank().getDisplayLimit();
        if (displayLimit != null && displayLimit > 0) {
            log.info("邀请排行榜配置显示前{}名", displayLimit);
        } else {
            log.info("邀请排行榜显示数量限制配置为空，显示全部用户");
        }

        List<ActivityRankVo> inviteRank;

        // 如果配置为空，不进行排除，使用原始查询方法
        if (excludePrefixes == null || excludePrefixes.isEmpty()) {
            log.info("邀请排行榜排除前缀配置为空，不进行用户过滤");
            inviteRank = digUserInvitationMapper.getInviteRank(
                activity.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                activity.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
            );
        } else {
            log.info("邀请排行榜使用配置的排除前缀进行过滤，排除前缀数量: {}", excludePrefixes.size());
            inviteRank = digUserInvitationMapper.getInviteRankWithUserFilter(
                activity.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                activity.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                excludePrefixes
            );
        }

        // 限制排行榜显示数量（仅在配置了有效值时进行限制）
        if (displayLimit != null && displayLimit > 0 && inviteRank.size() > displayLimit) {
            inviteRank = inviteRank.subList(0, displayLimit);
            log.info("邀请排行榜数据已限制为前{}名", displayLimit);
        }

        inviteRank.forEach(item -> {
            SysUser sysUser = sysUserMapper.selectUserById(item.getUserId());
            item.setNickName(DataDesensitizationUtil.replaceLeftRight(sysUser.getUserName(), 3, 4));
            if (StringUtils.isNotBlank(sysUser.getAvatar())) {
                item.setAvatar(attachmentInfoService.getObjectUrl(sysUser.getAvatar()));
            }
        });
        return inviteRank;
    }

    @Override
    public List<DigCouponCodeVo> getDigActivityPrizes(Long activityId, Long userId) {
        List<DigActivityParticipation> digActivityParticipations = digActivityParticipationMapper.selectList(Wrappers.<DigActivityParticipation>lambdaQuery()
                .eq(DigActivityParticipation::getActivityId, activityId)
                .eq(DigActivityParticipation::getUserId, userId)
                .isNotNull(DigActivityParticipation::getPrizeId)
                .isNotNull(DigActivityParticipation::getPrizeCode));
        List<DigCouponCodeVo> digCouponCodeVos = new ArrayList<>();
        digActivityParticipations.forEach(digActivityParticipation -> {
            DigCouponCodeVo digCouponCodeVo = new DigCouponCodeVo();
            digCouponCodeVo.setCouponId(digActivityParticipation.getPrizeId());
            digCouponCodeVo.setCouponCode(digActivityParticipation.getPrizeCode());
            DigCoupon digCoupon = digCouponService.selectDigCouponByCouponId(digActivityParticipation.getPrizeId());
            BeanUtil.copyProperties(digCoupon, digCouponCodeVo);
//            digCouponCodeVo.setCouponCover(attachmentInfoService.getObjectUrl(digCoupon.getCouponCover()));
            digCouponCodeVos.add(digCouponCodeVo);
        });
        return digCouponCodeVos;
    }

    /**
     * 安全开奖方法 - 使用Redis分布式锁防止重复开奖
     * 适用于大规模参与者的开奖场景
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void drawActivityWithLock(Long activityId) {
        String lockKey = "activity_draw_lock:" + activityId;
        String lockValue = String.valueOf(System.currentTimeMillis());

        try {
            // 1. 获取分布式锁，防止重复开奖
            boolean lockAcquired = acquireDistributedLock(lockKey, lockValue, 300); // 5分钟锁
            if (!lockAcquired) {
                log.warn("活动{}正在开奖中，请稍后再试", activityId);
                return;
            }

            // 2. 检查活动状态
            DigActivity activity = digActivityMapper.selectDigActivityByActivityId(activityId);
            if (activity == null) {
                log.error("活动{}不存在", activityId);
                return;
            }

            // 3. 检查是否已经开奖
            if (isActivityAlreadyDrawn(activityId)) {
                log.warn("活动{}已经开奖，跳过重复开奖", activityId);
                return;
            }

            // 4. 执行开奖逻辑
            performLotteryDraw(activityId);

            // 5. 标记活动已开奖
            markActivityAsDrawn(activityId);

            log.info("活动{}开奖完成", activityId);

        } catch (Exception e) {
            log.error("活动{}开奖失败", activityId, e);
            throw new RuntimeException("开奖失败: " + e.getMessage(), e);
        } finally {
            // 释放分布式锁
            releaseDistributedLock(lockKey, lockValue);
        }
    }

    /**
     * 执行开奖逻辑 - 使用分片查询优化大数据量处理
     */
    private void performLotteryDraw(Long activityId) {
        log.info("开始执行开奖逻辑：活动ID={}", activityId);
        
        // 1. 获取奖品信息
        List<DigCouponCode> prizes = digCouponService.selectCouponCodeList(Constants.COUPON_INVENTORY_SALE_TYPE_ACTIVITY, activityId);
        if (prizes.isEmpty()) {
            log.error("活动{}没有可用奖品", activityId);
            return;
        }
        log.info("活动{}获取到{}个奖品", activityId, prizes.size());

        // 2. 获取参与人数
        Long totalParticipants = digActivityParticipationMapper.selectCount(
                Wrappers.<DigActivityParticipation>lambdaQuery()
                        .eq(DigActivityParticipation::getActivityId, activityId)
        );

        if (totalParticipants == 0) {
            log.warn("活动{}没有参与者", activityId);
            return;
        }

        log.info("开始开奖：活动{}，参与人数{}，奖品数量{}", activityId, totalParticipants, prizes.size());

        // 3. 使用分片查询 + 随机算法选择中奖者
        List<Long> winners = selectWinnersBySharding(activityId, totalParticipants, prizes.size());
        log.info("选择中奖者完成：活动{}，中奖者数量{}", activityId, winners.size());

        // 4. 分配奖品
        assignPrizesBatch(activityId, winners, prizes);

        log.info("开奖完成：活动{}，中奖人数{}", activityId, winners.size());
    }

    /**
     * 使用分片查询选择中奖者 - 优化大数据量处理
     */
    private List<Long> selectWinnersBySharding(Long activityId, Long totalParticipants, int winnerCount) {
        List<Long> winners = new ArrayList<>();
        Set<Long> selectedIds = new HashSet<>();

        // 使用时间戳作为随机种子
        Random random = new Random(System.currentTimeMillis());

        // 如果参与人数小于等于奖品数量，所有人中奖
        if (totalParticipants <= winnerCount) {
            log.info("参与人数{}小于等于奖品数量{}，所有人中奖", totalParticipants, winnerCount);
            return getAllParticipantIds(activityId);
        }

        // 分片大小：每次查询1000条记录
        int shardSize = 1000;
        int maxAttempts = winnerCount * 20; // 最大尝试次数
        int attempts = 0;

        while (winners.size() < winnerCount && attempts < maxAttempts) {
            // 随机选择分片
            long randomShard = Math.abs(random.nextLong()) % (totalParticipants / shardSize + 1);
            long offset = randomShard * shardSize;

            // 查询分片数据
            List<DigActivityParticipation> shardParticipants = digActivityParticipationMapper.selectList(
                    Wrappers.<DigActivityParticipation>lambdaQuery()
                            .eq(DigActivityParticipation::getActivityId, activityId)
                            .last("LIMIT " + shardSize + " OFFSET " + offset)
                            .select(DigActivityParticipation::getParticipationId)
            );

            if (!shardParticipants.isEmpty()) {
                // 在分片内随机选择
                Collections.shuffle(shardParticipants);
                for (DigActivityParticipation participant : shardParticipants) {
                    if (winners.size() >= winnerCount) {
                        break;
                    }
                    if (!selectedIds.contains(participant.getParticipationId())) {
                        winners.add(participant.getParticipationId());
                        selectedIds.add(participant.getParticipationId());
                    }
                }
            }

            attempts++;
        }

        // 如果随机选择不够，补充选择
        if (winners.size() < winnerCount) {
            log.info("随机选择的中奖者数量{}不足，需要补充{}个", winners.size(), winnerCount - winners.size());
            List<Long> remaining = getRemainingParticipantIds(activityId, selectedIds, winnerCount - winners.size());
            winners.addAll(remaining);
        }

        log.info("最终选择的中奖者数量: {}", winners.size());
        return winners;
    }

    /**
     * 获取所有参与者ID
     */
    private List<Long> getAllParticipantIds(Long activityId) {
        return digActivityParticipationMapper.selectList(
                        Wrappers.<DigActivityParticipation>lambdaQuery()
                                .eq(DigActivityParticipation::getActivityId, activityId)
                                .select(DigActivityParticipation::getParticipationId)
                ).stream()
                .map(DigActivityParticipation::getParticipationId)
                .collect(Collectors.toList());
    }

    /**
     * 获取剩余参与者ID
     */
    private List<Long> getRemainingParticipantIds(Long activityId, Set<Long> excludeIds, int limit) {
        return digActivityParticipationMapper.selectList(
                        Wrappers.<DigActivityParticipation>lambdaQuery()
                                .eq(DigActivityParticipation::getActivityId, activityId)
                                .notIn(DigActivityParticipation::getParticipationId, excludeIds)
                                .last("LIMIT " + limit)
                                .select(DigActivityParticipation::getParticipationId)
                ).stream()
                .map(DigActivityParticipation::getParticipationId)
                .collect(Collectors.toList());
    }

    /**
     * 批量分配奖品
     */
    private void assignPrizesBatch(Long activityId, List<Long> winners, List<DigCouponCode> prizes) {
        log.info("开始分配奖品：活动{}，中奖者数量{}，奖品数量{}", activityId, winners.size(), prizes.size());
        
        if (winners.isEmpty() || prizes.isEmpty()) {
            log.warn("中奖者或奖品为空，无法分配奖品");
            return;
        }

        // 随机打乱奖品顺序
        Collections.shuffle(prizes);

        // 批量更新中奖记录
        List<DigActivityParticipation> updates = new ArrayList<>();
        List<DigCouponCode> updateCouponCodeList = new ArrayList<>();
        int prizeIndex = 0;

        for (Long winnerId : winners) {
            if (prizeIndex >= prizes.size()) {
                log.warn("奖品数量不足，中奖者{}无法获得奖品", winnerId);
                break;
            }

            DigCouponCode couponCode = prizes.get(prizeIndex);
            DigActivityParticipation participation = digActivityParticipationMapper.selectById(winnerId);
            
            if (participation == null) {
                log.error("未找到参与记录：participationId={}", winnerId);
                continue;
            }
            
            log.info("分配奖品：中奖者{}，奖品{}", participation.getUserId(), couponCode.getCouponCode());
            
            participation.setPrizeId(couponCode.getCouponId());
            participation.setPrizeCode(couponCode.getCouponCode());
            participation.setUpdateDate(LocalDateTime.now());

            updates.add(participation);

            couponCode.setUserId(participation.getUserId());
            couponCode.setUseTime(participation.getUpdateDate());
//            couponCode.setIsUsed(Constants.COUPON_INVENTORY_STATE_DISABLE);
            updateCouponCodeList.add(couponCode);

            prizeIndex++;
        }

        // 批量更新，提高性能
        if (!updates.isEmpty()) {
            log.info("开始批量更新数据库，参与记录更新数量：{}，奖品更新数量：{}", updates.size(), updateCouponCodeList.size());
            
            int participationUpdateCount = 0;
            for (DigActivityParticipation participation : updates) {
                int result = digActivityParticipationMapper.updateById(participation);
                if (result > 0) {
                    participationUpdateCount++;
                } else {
                    log.error("更新参与记录失败：participationId={}", participation.getParticipationId());
                }
            }
            
            int couponUpdateCount = 0;
            for (DigCouponCode couponCode : updateCouponCodeList) {
                int result = digCouponService.updateCouponCodeById(couponCode);
                if (result > 0) {
                    couponUpdateCount++;
                } else {
                    log.error("更新奖品失败：couponId={}", couponCode.getCouponId());
                }
            }
            
            log.info("数据库更新完成：参与记录成功更新{}条，奖品成功更新{}条", participationUpdateCount, couponUpdateCount);
        } else {
            log.warn("没有需要更新的记录");
        }
    }

    /**
     * 获取分布式锁
     */
    private boolean acquireDistributedLock(String lockKey, String lockValue, int expireSeconds) {
        try {
            // 这里需要实现Redis分布式锁逻辑
            // 可以使用Redisson或自定义实现
            // 暂时返回true，实际项目中需要实现
            RLock lock = redissonClient.getLock(lockKey);
            if (lock.tryLock(expireSeconds, TimeUnit.SECONDS)) {
                return true;
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return true;
    }

    /**
     * 释放分布式锁
     */
    private void releaseDistributedLock(String lockKey, String lockValue) {
        // 这里需要实现Redis分布式锁释放逻辑
        // 暂时为空实现，实际项目中需要实现
        RLock lock = redissonClient.getLock(lockKey);
        lock.unlock();
    }

    /**
     * 检查活动是否已经开奖
     */
    private boolean isActivityAlreadyDrawn(Long activityId) {
        // 检查是否有中奖记录
        Long count = digActivityParticipationMapper.selectCount(
                Wrappers.<DigActivityParticipation>lambdaQuery()
                        .eq(DigActivityParticipation::getActivityId, activityId)
                        .isNotNull(DigActivityParticipation::getPrizeId)
        );
        return count > 0;
    }

    /**
     * 标记活动已开奖
     */
    private void markActivityAsDrawn(Long activityId) {
        // 可以更新活动状态或记录开奖时间
        // 这里暂时为空实现
    }

}
