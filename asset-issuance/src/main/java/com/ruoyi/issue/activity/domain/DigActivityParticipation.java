package com.ruoyi.issue.activity.domain;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.issue.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;

/**
 * 活动参与记录对象 dig_activity_participation
 * 
 * <AUTHOR>
 * @date 2025-01-06
 */
@Data
@ApiModel(value = "活动参与记录对象")
public class DigActivityParticipation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 参与记录ID */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "参与记录ID")
    private Long participationId;

    /** 用户ID */
    @Excel(name = "用户ID")
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /** 活动ID */
    @Excel(name = "活动ID")
    @ApiModelProperty(value = "活动ID")
    private Long activityId;

    /** 参与时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "参与时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "参与时间")
    private LocalDateTime participateTime;

    /** 获得奖品ID */
    @Excel(name = "获得奖品ID")
    @ApiModelProperty(value = "获得奖品ID")
    private Long prizeId;

    /** 奖品编号(如兑换码) */
    @Excel(name = "奖品编号")
    @ApiModelProperty(value = "奖品编号(如兑换码)")
    private String prizeCode;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateDate;
} 