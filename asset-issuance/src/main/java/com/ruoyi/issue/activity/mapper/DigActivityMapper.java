package com.ruoyi.issue.activity.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.issue.activity.domain.DigActivity;

/**
 * 活动Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-15
 */
public interface DigActivityMapper extends BaseMapper<DigActivity>
{
    /**
     * 查询活动
     * 
     * @param activityId 活动主键
     * @return 活动
     */
    public DigActivity selectDigActivityByActivityId(Long activityId);

    /**
     * 查询活动列表
     * 
     * @param digActivity 活动
     * @return 活动集合
     */
    public List<DigActivity> selectDigActivityList(DigActivity digActivity);

    /**
     * 新增活动
     * 
     * @param digActivity 活动
     * @return 结果
     */
    public int insertDigActivity(DigActivity digActivity);

    /**
     * 修改活动
     * 
     * @param digActivity 活动
     * @return 结果
     */
    public int updateDigActivity(DigActivity digActivity);

    /**
     * 删除活动
     * 
     * @param activityId 活动主键
     * @return 结果
     */
    public int deleteDigActivityByActivityId(Long activityId);

    /**
     * 批量删除活动
     * 
     * @param activityIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDigActivityByActivityIds(Long[] activityIds);
}
