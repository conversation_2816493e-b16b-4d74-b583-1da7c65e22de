package com.ruoyi.issue.activity.controller;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.RandomUtil;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.domain.model.RegisterBody;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.web.service.SysRegisterService;
import com.ruoyi.issue.activity.domain.DigActivityParticipation;
import com.ruoyi.issue.coupon.vo.DigCouponCodeVo;
import com.ruoyi.scwt.common.util.SmsCodeUtils;
import com.ruoyi.system.domain.vo.ActivityRankVo;
import com.ruoyi.system.domain.DigUserInvitation;
import com.ruoyi.issue.common.constant.Constants;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.issue.activity.domain.DigActivity;
import com.ruoyi.issue.activity.service.IDigActivityService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 活动Controller
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@RestController
@RequestMapping("/activity/activity")
public class DigActivityController extends BaseController {
    @Autowired
    private IDigActivityService digActivityService;
    @Autowired
    private AttachmentInfoService attachmentInfoService;
    @Autowired
    private SysRegisterService registerService;
    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private  SmsCodeUtils smsCodeUtils;

    /**
     * 查询活动列表
     */
    @PreAuthorize("@ss.hasPermi('activity:activity:list')")
    @GetMapping("/list")
    public TableDataInfo list(DigActivity digActivity) {
        startPage();
        List<DigActivity> list = digActivityService.selectDigActivityList(digActivity);
        for (DigActivity activity : list) {
            activity.setActivityCover(attachmentInfoService.getObjectUrl(activity.getActivityCover()));
            activity.setDescription(attachmentInfoService.getObjectUrl(activity.getDescription()));
        }
        return getDataTable(list);
    }

    /**
     * 导出活动列表
     */
    @PreAuthorize("@ss.hasPermi('activity:activity:export')")
    @Log(title = "活动", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DigActivity digActivity) {
        List<DigActivity> list = digActivityService.selectDigActivityList(digActivity);
        ExcelUtil<DigActivity> util = new ExcelUtil<DigActivity>(DigActivity.class);
        util.exportExcel(response, list, "活动数据");
    }

    /**
     * 获取活动详细信息
     */
    @PreAuthorize("@ss.hasPermi('activity:activity:query')")
    @GetMapping(value = "/{activityId}")
    public AjaxResult getInfo(@PathVariable("activityId") Long activityId) {
        return success(digActivityService.selectDigActivityByActivityId(activityId));
    }

    /**
     * 新增活动
     */
    @PreAuthorize("@ss.hasPermi('activity:activity:add')")
    @Log(title = "活动", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DigActivity digActivity) {
        // 校验时间开始时间<结束时间<抽奖时间
        if (digActivity.getStartTime().isAfter(digActivity.getEndTime())) return error("开始时间不能大于结束时间");
        if (digActivity.getEndTime().isAfter(digActivity.getDrawTime())) return error("结束时间不能大于抽奖时间");
        return toAjax(digActivityService.insertDigActivity(digActivity));
    }

    /**
     * 修改活动
     */
    @PreAuthorize("@ss.hasPermi('activity:activity:edit')")
    @Log(title = "活动", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DigActivity digActivity) {
        if (digActivity.getStartTime().isAfter(digActivity.getEndTime())) return error("开始时间不能大于结束时间");
        if (digActivity.getEndTime().isAfter(digActivity.getDrawTime())) return error("结束时间不能大于抽奖时间");
        return toAjax(digActivityService.updateDigActivity(digActivity));
    }

    /**
     * 删除活动
     */
    @PreAuthorize("@ss.hasPermi('activity:activity:remove')")
    @Log(title = "活动", businessType = BusinessType.DELETE)
    @DeleteMapping("/{activityIds}")
    public AjaxResult remove(@PathVariable Long[] activityIds) {
        return toAjax(digActivityService.deleteDigActivityByActivityIds(activityIds));
    }

    /**----------------------------------------客户端----------------------------------------*/

    /**
     * 查询活动列表
     */
    @Anonymous
    @GetMapping("/client/list")
    public TableDataInfo clientList(DigActivity digActivity) {
        startPage();
        digActivity.setStatusCd(Constants.GENERAL_STATE_ENABLE);
        List<DigActivity> list = digActivityService.selectDigActivityList(digActivity);

        // 检查是否获取到数据
        if (list == null || list.isEmpty()) {
            TableDataInfo tableDataInfo = new TableDataInfo();
            tableDataInfo.setCode(HttpStatus.SUCCESS);
            tableDataInfo.setMsg("暂无活动数据");
            tableDataInfo.setRows(new ArrayList<>());
            tableDataInfo.setTotal(0);
            return tableDataInfo;
        }

        for (DigActivity activity : list) {
            activity.setActivityCover(attachmentInfoService.getObjectUrl(activity.getActivityCover()));
            activity.setDescription(attachmentInfoService.getObjectUrl(activity.getDescription()));
        }
        return getDataTable(list);
    }

    @GetMapping(value = "/client/{activityId}")
    public AjaxResult clientGetInfo(@PathVariable("activityId") Long activityId) {
        DigActivity digActivity = digActivityService.selectDigActivityByActivityId(activityId);
        digActivity.setActivityCover(attachmentInfoService.getObjectUrl(digActivity.getActivityCover()));
        digActivity.setDescription(attachmentInfoService.getObjectUrl(digActivity.getDescription()));
        digActivity.setIsParticipated(digActivityService.isParticipated(activityId, getUserId()));
        digActivity.setInviteCode(digActivityService.getUserInvitation(null,getUserId()).getInviteCode());
        return success(digActivity);
    }

    /**
     * 用户参与活动
     */
    @PostMapping("/client/join")
    public AjaxResult clientJoin(@RequestBody Long activityId) {
        DigActivity digActivity = digActivityService.selectDigActivityByActivityId(activityId);
        if (digActivity.getStartTime().isAfter(LocalDateTime.now())) return warn("活动尚未开始");
        if (digActivity.getEndTime().isBefore(LocalDateTime.now())) return warn("活动已结束");
        if (digActivityService.isParticipated(activityId, getUserId())) return warn("您已参与过该活动");
        DigActivityParticipation digActivityParticipation = new DigActivityParticipation();
        digActivityParticipation.setActivityId(activityId);
        digActivityParticipation.setUserId(getUserId());
        digActivityParticipation.setParticipateTime(LocalDateTime.now());
        digActivityParticipation.setCreateDate(LocalDateTime.now());
        return digActivityService.joinActivity(digActivityParticipation);
    }

    /**
     * 根据活动ID获取活动邀新排行榜
     */
    @Anonymous
    @GetMapping("/client/inviteRank/{activityId}")
    public TableDataInfo inviteRank(@PathVariable("activityId") Long activityId) {
        startPage();
        List<ActivityRankVo> inviteRank = digActivityService.getInviteRank(activityId);
        return getDataTable(inviteRank);
    }

    /**
     * 查看中奖结果
     */
    @Anonymous
    @GetMapping("/client/prizeResult/{activityId}")
    public AjaxResult prizeResult(@PathVariable("activityId") Long activityId) {
        DigActivity digActivity = digActivityService.selectDigActivityByActivityId(activityId);
        if (digActivity.getDrawTime().isAfter(LocalDateTime.now())) return warn("活动未开奖");
        List<DigCouponCodeVo> digActivityPrizes = digActivityService.getDigActivityPrizes(activityId,getUserId());
        return success(digActivityPrizes);
    }

    /**
     * 邀新活动专用注册
     */
    @Anonymous
    @PostMapping("/client/register")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult clientRegister(@RequestBody RegisterBody user) {
        if (!("true".equals(configService.selectConfigByKey("sys.account.registerUser")))) {
            return error("当前系统没有开启注册功能！");
        }
        String msg = registerService.register(user);
        if (StringUtils.isEmpty(msg)) {
            DigUserInvitation digUserInvitation = new DigUserInvitation();
            SysUser sysUser = sysUserService.selectUserByUserName(user.getUsername());
            digUserInvitation.setUserId(sysUser.getUserId());
            digUserInvitation.setInviteCode(sysUser.getUserId()+ RandomUtil.randomString(12));
            Long inviterId = digActivityService.getUserInvitation(user.getInviteCode(),null).getUserId();
            digUserInvitation.setInviterId(inviterId);
            digUserInvitation.setCreateDate(LocalDateTime.now());
            digActivityService.insertDigUserInvitation(digUserInvitation);
            return success();
        }
        return error(msg);
    }

    /**
     * 用户重置密码
     */
    @PutMapping("/client/updatePwd")
    public AjaxResult updatePwd(@RequestBody RegisterBody registerBody) {
        if (!registerBody.getPhone().equals(getLoginUser().getUser().getPhonenumber())) return error("手机号码不一致");
        smsCodeUtils.validateSmsCaptcha(registerBody.getPhone(), registerBody.getSmsCode());
        SysUser user = new SysUser();
        user.setPassword(SecurityUtils.encryptPassword(registerBody.getPassword()));
        user.setUpdateBy(getUsername());
        user.setUserId(getUserId());
        return toAjax(sysUserService.resetPwd(user));
    }


}
