package com.ruoyi.issue.issuer.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.issue.common.constant.Constants;
import com.ruoyi.issue.issuer.domain.DigAssetIssuerRel;
import com.ruoyi.issue.issuer.mapper.DigAssetIssuerRelMapper;
import com.ruoyi.issue.zone.service.DigAssetZoneService;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.ruoyi.issue.issuer.mapper.DigAssetIssuerMapper;
import com.ruoyi.issue.issuer.domain.DigAssetIssuer;
import com.ruoyi.issue.issuer.service.DigAssetIssuerService;

/**
 * 资产发行方Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DigAssetIssuerServiceImpl extends ServiceImpl<DigAssetIssuerMapper, DigAssetIssuer> implements DigAssetIssuerService {

    private final DigAssetIssuerMapper digAssetIssuerMapper;
    private final DigAssetIssuerRelMapper digAssetIssuerRelMapper;
    private final DigAssetZoneService digAssetZoneService;
    private final AttachmentInfoService attachmentInfoService;

    /**
     * 查询资产发行方
     *
     * @param issuerId 资产发行方主键
     * @return 资产发行方
     */
    @Override
    public DigAssetIssuer selectDigAssetIssuerByIssuerId(Long issuerId) {
        return digAssetIssuerMapper.selectDigAssetIssuerByIssuerId(issuerId);
    }

    /**
     * 查询资产发行方列表
     *
     * @param digAssetIssuer 资产发行方
     * @return 资产发行方
     */
    @Override
    public List<DigAssetIssuer> selectDigAssetIssuerList(DigAssetIssuer digAssetIssuer, List<SysRole> roles, Long userId) {
        List<Long> ids = digAssetZoneService.selectOperatorIdsByRoleIds(roles, userId);

        return digAssetIssuerMapper.selectList(Wrappers.<DigAssetIssuer>lambdaQuery()
                .in(ObjectUtils.isNotEmpty(ids), DigAssetIssuer::getCreateStaff, ids)
                .eq(StringUtils.isNotBlank(digAssetIssuer.getStatusCd()), DigAssetIssuer::getStatusCd, Constants.GENERAL_STATE_ENABLE)
                .like(StringUtils.isNotBlank(digAssetIssuer.getIssuerName()), DigAssetIssuer::getIssuerName, digAssetIssuer.getIssuerName()));

//        return digAssetIssuerMapper.selectDigAssetIssuerList(digAssetIssuer);
    }

    /**
     * 新增资产发行方
     *
     * @param digAssetIssuer 资产发行方
     * @return 结果
     */
    @Override
    public int insertDigAssetIssuer(DigAssetIssuer digAssetIssuer) {
        return digAssetIssuerMapper.insertDigAssetIssuer(digAssetIssuer);
    }

    /**
     * 修改资产发行方
     *
     * @param digAssetIssuer 资产发行方
     * @return 结果
     */
    @Override
    public int updateDigAssetIssuer(DigAssetIssuer digAssetIssuer) {
        return digAssetIssuerMapper.updateDigAssetIssuer(digAssetIssuer);
    }

    /**
     * 批量删除资产发行方
     *
     * @param issuerIds 需要删除的资产发行方主键
     * @return 结果
     */
    @Override
    public int deleteDigAssetIssuerByIssuerIds(Long[] issuerIds) {
        return digAssetIssuerMapper.deleteDigAssetIssuerByIssuerIds(issuerIds);
    }

    /**
     * 删除资产发行方信息
     *
     * @param issuerId 资产发行方主键
     * @return 结果
     */
    @Override
    public int deleteDigAssetIssuerByIssuerId(Long issuerId) {
        return digAssetIssuerMapper.deleteDigAssetIssuerByIssuerId(issuerId);
    }

    /**
     * 获取发行方名称列表
     *
     * @param issuerName
     * @return
     */
    @Override
    public List<DigAssetIssuer> getIssuerNameList(String issuerName, List<SysRole> roles, Long userId) {
        List<Long> ids = digAssetZoneService.selectOperatorIdsByRoleIds(roles, userId);
        return digAssetIssuerMapper.selectList(Wrappers.<DigAssetIssuer>lambdaQuery()
                .in(ObjectUtils.isNotEmpty(ids), DigAssetIssuer::getCreateStaff, ids)
                .like(StringUtils.isNotBlank(issuerName), DigAssetIssuer::getIssuerName, issuerName)
                .eq(DigAssetIssuer::getCreateStaff, userId));
    }

    /**
     * 插入资产发行方关系
     *
     * @param issuerRel
     */
    @Override
    public void insertRel(DigAssetIssuerRel issuerRel) {
        digAssetIssuerRelMapper.insert(issuerRel);
    }

    @Override
    public List<String> selectIssuerNamesByAssetId(Long assetId) {
        List<DigAssetIssuerRel> digAssetIssuerRels = digAssetIssuerRelMapper.selectList(Wrappers.<DigAssetIssuerRel>lambdaQuery().eq(DigAssetIssuerRel::getAssetId, assetId));
        if (!digAssetIssuerRels.isEmpty()) {
            List<Long> idList = digAssetIssuerRels.stream().map(DigAssetIssuerRel::getIssuerId).collect(Collectors.toList());
            List<DigAssetIssuer> digAssetIssuers = baseMapper.selectList(Wrappers.<DigAssetIssuer>lambdaQuery().in(DigAssetIssuer::getIssuerId, idList));
            return digAssetIssuers.stream().map(DigAssetIssuer::getIssuerName).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<DigAssetIssuer> selectIssuerListByAssetId(Long assetId) {
        List<DigAssetIssuerRel> digAssetIssuerRels = digAssetIssuerRelMapper.selectList(Wrappers.<DigAssetIssuerRel>lambdaQuery().eq(DigAssetIssuerRel::getAssetId, assetId));
        List<DigAssetIssuer> digAssetIssuers = baseMapper.selectList(Wrappers.<DigAssetIssuer>lambdaQuery()
                .select(DigAssetIssuer::getIssuerId, DigAssetIssuer::getIssuerName, DigAssetIssuer::getIssuerLogo)
                .in(DigAssetIssuer::getIssuerId, digAssetIssuerRels.stream().map(DigAssetIssuerRel::getIssuerId).collect(Collectors.toList()))
                .eq(DigAssetIssuer::getStatusCd, Constants.GENERAL_STATE_ENABLE));
        digAssetIssuers.forEach(issuer -> issuer.setIssuerLogo(attachmentInfoService.getObjectUrl(issuer.getIssuerLogo())));
        return digAssetIssuers;
    }

    @Override
    public void removeRel(Long assetId) {
        digAssetIssuerRelMapper.delete(Wrappers.<DigAssetIssuerRel>lambdaQuery().eq(DigAssetIssuerRel::getAssetId, assetId));
    }
}
