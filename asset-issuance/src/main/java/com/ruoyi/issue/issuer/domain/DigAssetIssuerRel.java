package com.ruoyi.issue.issuer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 资产-发行方关联表 实体类
 */
@Data
@TableName("dig_asset_issuer_rel")
@EqualsAndHashCode()
public class DigAssetIssuerRel extends Model<DigAssetIssuerRel> {

    /**
     * 关联ID
     */
    @TableId(type= IdType.AUTO)
    private Long id;

    /**
     * 资产ID
     */
    @ApiModelProperty(value="资产ID")
    private Long assetId;

    /**
     * 发行方ID
     */
    @ApiModelProperty(value="发行方ID")
    private Long issuerId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createDate;

}
