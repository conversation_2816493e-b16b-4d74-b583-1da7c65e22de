package com.ruoyi.issue.issuer.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.issue.common.constant.Constants;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.issue.issuer.domain.DigAssetIssuer;
import com.ruoyi.issue.issuer.service.DigAssetIssuerService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 资产发行方Controller
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@RestController
@Api(value = "资产发行方管理", tags = "资产发行方管理")
@RequestMapping("/issuer/issuer")
public class DigAssetIssuerController extends BaseController
{
    @Autowired
    private DigAssetIssuerService digAssetIssuerService;

    @Autowired
    private AttachmentInfoService attachmentInfoService;

    /**
     * 查询资产发行方列表
     */
    @PreAuthorize("@ss.hasPermi('issuer:issuer:list')")
    @GetMapping("/list")
    @ApiOperation("查询资产发行方列表")
    public TableDataInfo list(DigAssetIssuer digAssetIssuer)
    {
        startPage();
        digAssetIssuer.setCreateStaff(String.valueOf(getUserId()));
        List<DigAssetIssuer> list = digAssetIssuerService.selectDigAssetIssuerList(digAssetIssuer,getLoginUser().getUser().getRoles(),getUserId());
        list.forEach(item -> item.setIssuerLogo(attachmentInfoService.getObjectUrl(item.getIssuerLogo())));
        return getDataTable(list);
    }

    /**
     * 导出资产发行方列表
     */
    @PreAuthorize("@ss.hasPermi('issuer:issuer:export')")
    @Log(title = "资产发行方", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出资产发行方列表")
    public void export(HttpServletResponse response, DigAssetIssuer digAssetIssuer)
    {
        List<DigAssetIssuer> list = digAssetIssuerService.selectDigAssetIssuerList(digAssetIssuer,getLoginUser().getUser().getRoles(),getUserId());
        ExcelUtil<DigAssetIssuer> util = new ExcelUtil<DigAssetIssuer>(DigAssetIssuer.class);
        util.exportExcel(response, list, "资产发行方数据");
    }

    /**
     * 获取资产发行方详细信息
     */
    @PreAuthorize("@ss.hasPermi('issuer:issuer:query')")
    @GetMapping(value = "/{issuerId}")
    @ApiOperation("获取资产发行方详细信息")
    public AjaxResult getInfo(@PathVariable("issuerId") Long issuerId)
    {
        return success(digAssetIssuerService.selectDigAssetIssuerByIssuerId(issuerId));
    }

    /**
     * 新增资产发行方
     */
    @PreAuthorize("@ss.hasPermi('issuer:issuer:add')")
    @Log(title = "资产发行方", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增资产发行方")
    public AjaxResult add(@RequestBody DigAssetIssuer digAssetIssuer)
    {
        digAssetIssuer.setCreateStaff(String.valueOf(getUserId()));
        digAssetIssuer.setStatusCd(Constants.GENERAL_STATE_ENABLE);
        return toAjax(digAssetIssuerService.insertDigAssetIssuer(digAssetIssuer));
    }

    /**
     * 修改资产发行方
     */
    @PreAuthorize("@ss.hasPermi('issuer:issuer:edit')")
    @Log(title = "资产发行方", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改资产发行方")
    public AjaxResult edit(@RequestBody DigAssetIssuer digAssetIssuer)
    {
        digAssetIssuer.setCreateStaff(String.valueOf(getUserId()));
        digAssetIssuer.setStatusCd(Constants.GENERAL_STATE_ENABLE);
        return toAjax(digAssetIssuerService.updateDigAssetIssuer(digAssetIssuer));
    }

    /**
     * 删除资产发行方
     */
    @PreAuthorize("@ss.hasPermi('issuer:issuer:remove')")
    @Log(title = "资产发行方", businessType = BusinessType.DELETE)
	@DeleteMapping("/{issuerIds}")
    @ApiOperation("删除资产发行方")
    public AjaxResult remove(@PathVariable Long[] issuerIds)
    {
        return toAjax(digAssetIssuerService.deleteDigAssetIssuerByIssuerIds(issuerIds));
    }

    /***
     * 获取资产发行方名称列表，可根据名称模糊查询
     */
    @GetMapping("/getIssuerNameList")
    @ApiOperation("获取资产发行方名称列表")
    public AjaxResult getIssuerNameList(String issuerName) {
        return success(digAssetIssuerService.getIssuerNameList(issuerName,getLoginUser().getUser().getRoles(),getUserId()));
    }
}
