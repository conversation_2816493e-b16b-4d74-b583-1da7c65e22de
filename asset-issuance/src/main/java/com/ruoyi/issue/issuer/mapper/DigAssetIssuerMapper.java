package com.ruoyi.issue.issuer.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.issue.issuer.domain.DigAssetIssuer;
import com.ruoyi.scwt.works.entity.EvidenceOwner;

/**
 * 资产发行方Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface DigAssetIssuerMapper  extends BaseMapper<DigAssetIssuer>
{
    /**
     * 查询资产发行方
     * 
     * @param issuerId 资产发行方主键
     * @return 资产发行方
     */
    public DigAssetIssuer selectDigAssetIssuerByIssuerId(Long issuerId);

    /**
     * 查询资产发行方列表
     * 
     * @param digAssetIssuer 资产发行方
     * @return 资产发行方集合
     */
    public List<DigAssetIssuer> selectDigAssetIssuerList(DigAssetIssuer digAssetIssuer);

    /**
     * 新增资产发行方
     * 
     * @param digAssetIssuer 资产发行方
     * @return 结果
     */
    public int insertDigAssetIssuer(DigAssetIssuer digAssetIssuer);

    /**
     * 修改资产发行方
     * 
     * @param digAssetIssuer 资产发行方
     * @return 结果
     */
    public int updateDigAssetIssuer(DigAssetIssuer digAssetIssuer);

    /**
     * 删除资产发行方
     * 
     * @param issuerId 资产发行方主键
     * @return 结果
     */
    public int deleteDigAssetIssuerByIssuerId(Long issuerId);

    /**
     * 批量删除资产发行方
     * 
     * @param issuerIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDigAssetIssuerByIssuerIds(Long[] issuerIds);
}
