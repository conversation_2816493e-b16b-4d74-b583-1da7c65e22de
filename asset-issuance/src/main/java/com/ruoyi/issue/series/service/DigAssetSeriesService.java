package com.ruoyi.issue.series.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.issue.series.domain.DigAssetSeries;
import com.ruoyi.issue.series.domain.DigAssetSeriesRel;

/**
 * 资产系列Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface DigAssetSeriesService extends IService<DigAssetSeries>
{
    /**
     * 查询资产系列
     * 
     * @param seriesId 资产系列主键
     * @return 资产系列
     */
    public DigAssetSeries selectDigAssetSeriesBySeriesId(Long seriesId);

    /**
     * 查询资产系列列表
     * 
     * @param digAssetSeries 资产系列
     * @return 资产系列集合
     */
    public List<DigAssetSeries> selectDigAssetSeriesList(DigAssetSeries digAssetSeries, List<SysRole>  roles, Long userId);

    /**
     * 新增资产系列
     * 
     * @param digAssetSeries 资产系列
     * @return 结果
     */
    public int insertDigAssetSeries(DigAssetSeries digAssetSeries);

    /**
     * 修改资产系列
     * 
     * @param digAssetSeries 资产系列
     * @return 结果
     */
    public int updateDigAssetSeries(DigAssetSeries digAssetSeries);

    /**
     * 批量删除资产系列
     * 
     * @param seriesIds 需要删除的资产系列主键集合
     * @return 结果
     */
    public int deleteDigAssetSeriesBySeriesIds(Long[] seriesIds);

    /**
     * 删除资产系列信息
     * 
     * @param seriesId 资产系列主键
     * @return 结果
     */
    public int deleteDigAssetSeriesBySeriesId(Long seriesId);

    /**
     * 根据名称查询资产系列列表
     *
     * @param seriesName
     * @return
     */
    List<DigAssetSeries> selectDigAssetSeriesListByName(String seriesName,  Long userId);

    /**
     * 插入资产系列关系
     *
     * @param seriesRel
     */
    void insertRel(DigAssetSeriesRel seriesRel);

    /**
     * 根据资产ID查询资产系列名称
     *
     * @param assetId
     * @return
     */
    String selectSeriesNameByAssetId(Long assetId);

    List<DigAssetSeries> selectNameList();

    void removeRel(Long assetId);
}
