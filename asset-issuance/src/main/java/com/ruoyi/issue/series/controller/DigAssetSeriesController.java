package com.ruoyi.issue.series.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.issue.common.constant.Constants;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.issue.series.domain.DigAssetSeries;
import com.ruoyi.issue.series.service.DigAssetSeriesService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 资产系列Controller
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@RestController
@Api(value = "资产系列管理", tags = "资产系列管理")
@RequestMapping("/series/series")
public class DigAssetSeriesController extends BaseController {
    @Autowired
    private DigAssetSeriesService digAssetSeriesService;

    @Autowired
    private AttachmentInfoService attachmentInfoService;

    /**
     * 查询资产系列列表
     */
    @PreAuthorize("@ss.hasPermi('series:series:list')")
    @GetMapping("/list")
    @ApiOperation("查询资产系列列表")
    public TableDataInfo list(DigAssetSeries digAssetSeries) {
        startPage();
        digAssetSeries.setCreateStaff(String.valueOf(getUserId()));
        List<DigAssetSeries> list = digAssetSeriesService.selectDigAssetSeriesList(digAssetSeries,getLoginUser().getUser().getRoles(),getUserId());
        return getDataTable(list);
    }

    /**
     * 导出资产系列列表
     */
    @PreAuthorize("@ss.hasPermi('series:series:export')")
    @Log(title = "资产系列", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出资产系列列表")
    public void export(HttpServletResponse response, DigAssetSeries digAssetSeries) {
        List<DigAssetSeries> list = digAssetSeriesService.selectDigAssetSeriesList(digAssetSeries,getLoginUser().getUser().getRoles(),getUserId());
        ExcelUtil<DigAssetSeries> util = new ExcelUtil<DigAssetSeries>(DigAssetSeries.class);
        util.exportExcel(response, list, "资产系列数据");
    }

    /**
     * 获取资产系列详细信息
     */
    @PreAuthorize("@ss.hasPermi('series:series:query')")
    @GetMapping(value = "/{seriesId}")
    @ApiOperation("获取资产系列详细信息")
    public AjaxResult getInfo(@PathVariable("seriesId") Long seriesId) {
        return success(digAssetSeriesService.selectDigAssetSeriesBySeriesId(seriesId));
    }

    /**
     * 新增资产系列
     */
    @PreAuthorize("@ss.hasPermi('series:series:add')")
    @Log(title = "资产系列", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增资产系列")
    public AjaxResult add(@RequestBody DigAssetSeries digAssetSeries) {
        digAssetSeries.setCreateStaff(String.valueOf(getUserId()));
        digAssetSeries.setStatusCd(Constants.GENERAL_STATE_ENABLE);
        return toAjax(digAssetSeriesService.insertDigAssetSeries(digAssetSeries));
    }

    /**
     * 修改资产系列
     */
    @PreAuthorize("@ss.hasPermi('series:series:edit')")
    @Log(title = "资产系列", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改资产系列")
    public AjaxResult edit(@RequestBody DigAssetSeries digAssetSeries) {
        digAssetSeries.setCreateStaff(String.valueOf(getUserId()));
        digAssetSeries.setStatusCd(Constants.GENERAL_STATE_ENABLE);
        return toAjax(digAssetSeriesService.updateDigAssetSeries(digAssetSeries));
    }

    /**
     * 删除资产系列
     */
    @PreAuthorize("@ss.hasPermi('series:series:remove')")
    @Log(title = "资产系列", businessType = BusinessType.DELETE)
    @DeleteMapping("/{seriesIds}")
    @ApiOperation("删除资产系列")
    public AjaxResult remove(@PathVariable Long[] seriesIds) {
        return toAjax(digAssetSeriesService.deleteDigAssetSeriesBySeriesIds(seriesIds));
    }

    /**
     * 查询资产系列列表，系列名称模糊查询
     */
    @GetMapping("/listByName")
    @ApiOperation("根据名称模糊查询资产系列列表")
    public AjaxResult listByName(String seriesName) {
        return success(digAssetSeriesService.selectDigAssetSeriesListByName(seriesName, getUserId()));
    }

    /**-------------------------------客户端，不需要token的接口----------------------------------*/


    /**
     * 获取所有资产系列名称列表，不需要token
     */
    @GetMapping("/listName")
    @Anonymous
    @ApiOperation("获取所有资产系列名称列表")
    public AjaxResult listName() {
        return success(digAssetSeriesService.selectNameList());
    }



    /**
     * 获取资产系列详细信息
     */
    @GetMapping(value = "/client/{seriesId}")
    @Anonymous
    @ApiOperation("客户端获取资产系列详细信息")
    public AjaxResult getSeriesInfo(@PathVariable("seriesId") Long seriesId) {
        DigAssetSeries digAssetSeries = digAssetSeriesService.selectDigAssetSeriesBySeriesId(seriesId);
        digAssetSeries.setSeriesCover(attachmentInfoService.getObjectUrl(digAssetSeries.getSeriesCover()));
        return success(digAssetSeries);
    }

}
