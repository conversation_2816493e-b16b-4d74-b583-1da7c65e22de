package com.ruoyi.issue.series.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.issue.common.constant.Constants;
import com.ruoyi.issue.series.domain.DigAssetSeriesRel;
import com.ruoyi.issue.series.mapper.DigAssetSeriesRelMapper;
import com.ruoyi.issue.zone.service.DigAssetZoneService;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.ruoyi.issue.series.mapper.DigAssetSeriesMapper;
import com.ruoyi.issue.series.domain.DigAssetSeries;
import com.ruoyi.issue.series.service.DigAssetSeriesService;

/**
 * 资产系列Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DigAssetSeriesServiceImpl extends ServiceImpl<DigAssetSeriesMapper, DigAssetSeries> implements DigAssetSeriesService {

    private final DigAssetSeriesMapper digAssetSeriesMapper;
    private final DigAssetSeriesRelMapper digAssetSeriesRelMapper;

    private final AttachmentInfoService attachmentInfoService;
    private final DigAssetZoneService digAssetZoneService;

    /**
     * 查询资产系列
     *
     * @param seriesId 资产系列主键
     * @return 资产系列
     */
    @Override
    public DigAssetSeries selectDigAssetSeriesBySeriesId(Long seriesId) {
        return digAssetSeriesMapper.selectDigAssetSeriesBySeriesId(seriesId);
    }

    /**
     * 查询资产系列列表
     *
     * @param digAssetSeries 资产系列
     * @return 资产系列
     */
    @Override
    public List<DigAssetSeries> selectDigAssetSeriesList(DigAssetSeries digAssetSeries, List<SysRole>  roles, Long userId) {
//        List<DigAssetSeries> list = digAssetSeriesMapper.selectDigAssetSeriesList(digAssetSeries);
        List<Long> ids = null;
        if (ObjectUtils.isNotEmpty(roles) && roles.size() > 0 && userId != null) {
            ids = digAssetZoneService.selectOperatorIdsByRoleIds(roles, userId);
        }
        List<DigAssetSeries> list = digAssetSeriesMapper.selectList(Wrappers.<DigAssetSeries>lambdaQuery()
                .in(ObjectUtils.isNotEmpty(ids),DigAssetSeries::getCreateStaff, ids)
                .eq(StringUtils.isNotBlank(digAssetSeries.getStatusCd()),DigAssetSeries::getStatusCd, Constants.GENERAL_STATE_ENABLE)
                .like(StringUtils.isNotBlank(digAssetSeries.getSeriesName()), DigAssetSeries::getSeriesName, digAssetSeries.getSeriesName()));
        list.forEach(item -> item.setSeriesCover(attachmentInfoService.getObjectUrl(item.getSeriesCover())));
        return list;
    }

    /**
     * 新增资产系列
     *
     * @param digAssetSeries 资产系列
     * @return 结果
     */
    @Override
    public int insertDigAssetSeries(DigAssetSeries digAssetSeries) {
        return digAssetSeriesMapper.insertDigAssetSeries(digAssetSeries);
    }

    /**
     * 修改资产系列
     *
     * @param digAssetSeries 资产系列
     * @return 结果
     */
    @Override
    public int updateDigAssetSeries(DigAssetSeries digAssetSeries) {
        return digAssetSeriesMapper.updateDigAssetSeries(digAssetSeries);
    }

    /**
     * 批量删除资产系列
     *
     * @param seriesIds 需要删除的资产系列主键
     * @return 结果
     */
    @Override
    public int deleteDigAssetSeriesBySeriesIds(Long[] seriesIds) {
        return digAssetSeriesMapper.deleteDigAssetSeriesBySeriesIds(seriesIds);
    }

    /**
     * 删除资产系列信息
     *
     * @param seriesId 资产系列主键
     * @return 结果
     */
    @Override
    public int deleteDigAssetSeriesBySeriesId(Long seriesId) {
        return digAssetSeriesMapper.deleteDigAssetSeriesBySeriesId(seriesId);
    }

    @Override
    public List<DigAssetSeries> selectDigAssetSeriesListByName(String seriesName, Long userId) {
        return digAssetSeriesMapper.selectList(Wrappers.<DigAssetSeries>lambdaQuery()
                .like(StringUtils.isNotBlank(seriesName), DigAssetSeries::getSeriesName, seriesName)
                .eq(DigAssetSeries::getCreateStaff, userId));
    }

    @Override
    public void insertRel(DigAssetSeriesRel seriesRel) {
        digAssetSeriesRelMapper.insert(seriesRel);
    }

    @Override
    public String selectSeriesNameByAssetId(Long assetId) {
        DigAssetSeriesRel digAssetSeriesRel = digAssetSeriesRelMapper.selectOne(Wrappers.<DigAssetSeriesRel>lambdaQuery().eq(DigAssetSeriesRel::getAssetId, assetId));
        if (digAssetSeriesRel != null) {
            DigAssetSeries digAssetSeries = digAssetSeriesMapper.selectDigAssetSeriesBySeriesId(digAssetSeriesRel.getSeriesId());
            if (digAssetSeries != null) {
                return digAssetSeries.getSeriesName();
            }
        }
        return null;
    }

    @Override
    public List<DigAssetSeries> selectNameList() {
        return baseMapper.selectList(Wrappers.<DigAssetSeries>lambdaQuery().select(DigAssetSeries::getSeriesName, DigAssetSeries::getSeriesId).eq(DigAssetSeries::getStatusCd, Constants.GENERAL_STATE_ENABLE));
    }

    @Override
    public void removeRel(Long assetId) {
        digAssetSeriesRelMapper.delete(Wrappers.<DigAssetSeriesRel>lambdaQuery().eq(DigAssetSeriesRel::getAssetId, assetId));
    }
}
