package com.ruoyi.issue.series.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
@Data
@TableName("dig_asset_series_rel")
public class DigAssetSeriesRel extends Model<DigAssetSeriesRel> {
    /**
     * 关联ID
     */
    @TableId(type= IdType.AUTO)
    private Long id;

    /**
     * 资产ID
     */
    @ApiModelProperty(value="资产ID")
    private Long assetId;

    /**
     * 系列ID
     */
    @ApiModelProperty(value="系列ID")
    private Long seriesId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createDate;
}
