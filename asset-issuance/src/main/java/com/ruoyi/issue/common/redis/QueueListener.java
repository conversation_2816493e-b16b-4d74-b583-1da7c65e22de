package com.ruoyi.issue.common.redis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.QueryTimeoutException;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.concurrent.TimeUnit;

/**
 * @ClassName QueueListener
 * <AUTHOR> 102306
 * @Date 2022/2/15 10:44
 */
@Slf4j
public class QueueListener implements Runnable{

    private final StringRedisTemplate redisTemplate;
    private final String queue;
    private final String tempQueue;
    private final MsgConsumer consumer;

    public QueueListener(StringRedisTemplate redisTemplate, String queue, MsgConsumer consumer, String tempQueue) {
        this.redisTemplate = redisTemplate;
        this.queue = queue;
        this.consumer = consumer;
        this.tempQueue = tempQueue;
    }

    @Override
    public void run() {
        log.info("QueueListener start...queue:{}", queue);
        while (RedisMqConsumerContainer.run) {
            try {
                Object msg = redisTemplate.opsForList().rightPopAndLeftPush(queue, tempQueue,30, TimeUnit.SECONDS);
                if (msg != null) {
                    try {
                        consumer.onMessage(msg);
                    } catch (Exception e) {
                        consumer.onError(msg, e);
                    }
                }
            } catch (QueryTimeoutException ignored) {
                log.error("超时");
            } catch (Exception e) {
                if (RedisMqConsumerContainer.run) {
                    log.error("Queue:{}", queue, e);
                } else {
                    log.info("QueueListener exits...queue:{}", queue);
                }
            }
        }
    }
}
