package com.ruoyi.issue.common.redis;

import cn.hutool.json.JSONUtil;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * @ClassName QueueSender
 * <AUTHOR> 102306
 * @Date 2022/2/15 10:41
 */
public class QueueSender {

    private final StringRedisTemplate redisTemplate;


    public QueueSender(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public void sendMsg(String queue,Object msg){
        redisTemplate.opsForList().leftPush(queue, JSONUtil.toJsonStr(msg));
    }

}
