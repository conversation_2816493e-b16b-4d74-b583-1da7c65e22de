package com.ruoyi.issue.common.redis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @ClassName RedisMqConsumerContainer
 * <AUTHOR> 102306
 * @Date 2022/2/15 10:50
 */

@Slf4j
public class RedisMqConsumerContainer {
    private Map<String, QueueConfiguration> consumerMap = new HashMap<>();
    private final StringRedisTemplate redisTemplate;
    static boolean run;
    private ExecutorService exec;
    private final String tempQueue;

    public RedisMqConsumerContainer(StringRedisTemplate redisTemplate, String tempQueue) {
        this.redisTemplate = redisTemplate;
        this.tempQueue = tempQueue;
    }

    public void addConsumer(QueueConfiguration configuration) {
        if (consumerMap.containsKey(configuration.getQueue())) {
            log.warn("Key:{} 队列已经存在", configuration.getQueue());
        }
        if (configuration.getConsumer() == null) {
            log.warn("队列:{} 消费者不能为空", configuration.getQueue());
        }
        consumerMap.put(configuration.getQueue(), configuration);
    }

    public void destroy() {
        run = false;
        this.exec.shutdown();
        log.info("QueueListener exiting.");
        while (!this.exec.isTerminated()) {

        }
        log.info("QueueListener exited.");
    }

    public void init() {
        run = true;
        this.exec = Executors.newCachedThreadPool(r -> {
            final AtomicInteger threadNumber = new AtomicInteger(1);
            return new Thread(r, "RedisMQListener-" + threadNumber.getAndIncrement());
        });
        consumerMap = Collections.unmodifiableMap(consumerMap);
        consumerMap.forEach((k, v) -> exec.submit(new QueueListener(redisTemplate, v.getQueue(), v.getConsumer(),tempQueue)));
    }

}
