package com.ruoyi.issue.common.config;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * 使用guava接口限流
 * <AUTHOR> 102306
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequestRateLimiter {

    /**
     * 这里指吞吐率每秒多少许可数（通常是指QPS，每秒多少查询）
     */
    double QPS() default 10D;

    /**
     * 获取令牌超时时间
     */
    long acquireTokenTimeout() default 100;

    /**
     * 获取令牌超时时间单位：默认为 毫秒
     */
    TimeUnit timeunit() default TimeUnit.MILLISECONDS;

    /**
     * 获取令牌失败提示
     */
    String resMsg() default "请求过于频繁，请稍后再试！";
}
