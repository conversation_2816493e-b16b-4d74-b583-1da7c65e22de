package com.ruoyi.issue.common.redis;


import com.ruoyi.common.constant.CacheConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * @ClassName ConsumerConfiguration
 * <AUTHOR> 102306
 * @Date 2022/2/15 11:39
 */
@Configuration
@Order(110)
public class ConsumerConfiguration {

    /**
     * 测试支付消费者 测试代码
     *
     * @param redisTemplate redis
     * @return 消费者容器
     */
    @Bean(initMethod = "init", destroyMethod = "destroy")
    public RedisMqConsumerContainer payRedisMqConsumerContainer(@Autowired StringRedisTemplate redisTemplate) {
        RedisMqConsumerContainer config = new RedisMqConsumerContainer(redisTemplate, CacheConstants.ORDER_TEMP_QUEUE);
        config.addConsumer(QueueConfiguration.builder()
                .queue(CacheConstants.ORDER_QUEUE)
                .tempQueue(CacheConstants.ORDER_TEMP_QUEUE)
                .consumer(new CreateOrderConsumer())
                .build());
        return config;
    }

    /**
     * 上链消息消费者配置
     *
     * @param redisTemplate redis
     * @return 消费者容器
     */
    @Bean(initMethod = "init", destroyMethod = "destroy")
    public RedisMqConsumerContainer chainRedisMqConsumerContainer(@Autowired StringRedisTemplate redisTemplate) {
        RedisMqConsumerContainer config = new RedisMqConsumerContainer(
                redisTemplate,
                CacheConstants.CHAIN_TEMP_QUEUE
        );

        config.addConsumer(QueueConfiguration.builder()
                .queue(CacheConstants.CHAIN_QUEUE)
                .tempQueue(CacheConstants.CHAIN_TEMP_QUEUE)
                .consumer(new ChainConsumer()) // 使用新建的ChainConsumer
                .build());

        return config;
    }

    /**
     * 配置redis消息队列生产者
     *
     * @param redisTemplate redis
     * @return 生产者
     */
    @Bean
    public QueueSender queueSender(@Autowired StringRedisTemplate redisTemplate) {
        return new QueueSender(redisTemplate);
    }
}
