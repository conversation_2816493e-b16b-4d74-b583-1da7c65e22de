package com.ruoyi.issue.common.redis;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.issue.asset.domain.DigDigitalAssetDetailVo;
import com.ruoyi.issue.assetData.domain.DigDataAsset;
import com.ruoyi.issue.assetData.service.impl.DigDataAssetServiceImpl;
import com.ruoyi.issue.common.constant.Constants;
import com.ruoyi.issue.common.util.SpringContextHolder;
import com.ruoyi.scwt.identify.entity.Identify;
import com.ruoyi.scwt.identify.entity.WcUser;
import com.ruoyi.scwt.identify.mapper.WcUserMapper;
import com.ruoyi.scwt.identify.service.impl.IdentifyServiceImpl;
import com.ruoyi.scwt.sop.controller.BlockChainController;
import com.ruoyi.scwt.sop.dto.WCDACDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
public class ChainConsumer implements MsgConsumer {

    @Override
    public void onMessage(Object message) {
        log.info("接收到上链消息：{}", message);
        DigDataAsset chainTask = JSONUtil.toBean(JSONUtil.parseObj(message), DigDataAsset.class);
        BlockChainController blockChainController = SpringContextHolder.getBean(BlockChainController.class);
        WcUserMapper wcUserMapper = SpringContextHolder.getBean(WcUserMapper.class);
        DigDataAssetServiceImpl digDataAssetService = SpringContextHolder.getBean(DigDataAssetServiceImpl.class);
        // 开启事务
        TransactionTemplate transactionTemplate = SpringContextHolder.getBean(TransactionTemplate.class);
        WcUser wcUser = wcUserMapper.selectOne(Wrappers.<WcUser>lambdaQuery()
                .eq(WcUser::getUserId, chainTask.getUserId()));
        transactionTemplate.execute(status -> {
            WCDACDto wcdacDto = new WCDACDto();
            wcdacDto.setAmount(1L);
            wcdacDto.setCategoryId(33L);
            wcdacDto.setMetadata(chainTask.toString());
            wcdacDto.setTo(wcUser.getAddress());
            JSONObject jsonObject = blockChainController.issueNft(wcdacDto);
            chainTask.setChainId(jsonObject.getJSONArray("dacIdList").get(0).toString());
            chainTask.setChainHash(jsonObject.getStr("transactionHash"));
            chainTask.setChainTime(LocalDateTime.now());
            chainTask.setStatusCd(Constants.DIG_ASSET_DATA_STATE_ENABLE);
            chainTask.setStatusDate(LocalDateTime.now());
            digDataAssetService.updateDigDataAsset(chainTask);
            digDataAssetService.update(Wrappers.<DigDataAsset>lambdaUpdate()
                    .set(DigDataAsset::getChainId, jsonObject.getJSONArray("dacIdList").get(0).toString())
                    .set(DigDataAsset::getChainHash, jsonObject.getStr("transactionHash"))
                    .set(DigDataAsset::getChainTime, LocalDateTime.now())
                    .set(DigDataAsset::getStatusCd, Constants.DIG_ASSET_DATA_STATE_ENABLE)
                    .set(DigDataAsset::getStatusDate, LocalDateTime.now())
                    .set(DigDataAsset::getUpdateDate, LocalDateTime.now())
                    .eq(DigDataAsset::getUserId, chainTask.getUserId())
                    .eq(DigDataAsset::getAssetCode, chainTask.getAssetCode()));
            return true;
        });
    }

    @Override
    public void onError(Object msg, Exception e) {
        log.error("上链消息队列发生错误，消息：{}", msg, e);
        DigDataAsset chainTask = JSONUtil.toBean(JSONUtil.parseObj(msg), DigDataAsset.class);
        chainTask.setErrorNum(chainTask.getErrorNum() == null ? 1 : chainTask.getErrorNum() + 1);
        
        QueueSender sender = SpringContextHolder.getBean(QueueSender.class);
        if (chainTask.getErrorNum() > 5) {
            log.error("上链消息错误次数超过5次，资产ID：{}", chainTask.getDataAssetId());
            // 处理严重错误，如记录到数据库或发送告警
            sender.sendMsg(CacheConstants.CHAIN_QUEUE_ERROR, chainTask);
        } else {
            // 重试
            sender.sendMsg(CacheConstants.CHAIN_QUEUE, chainTask);
        }
    }
}
