package com.ruoyi.issue.common.constant;

import com.ruoyi.common.core.domain.entity.SysRole;
import org.quartz.SimpleTrigger;

/**
 * <AUTHOR>
 * 通用常量
 */
public interface Constants {


    /***-----------------------------------------通用-------------------------------------------------*/

    /**
     * 启用
     */
    String GENERAL_STATE_ENABLE = "1000";
    /**
     * 禁用
     */
    String GENERAL_STATE_DISABLE = "1100";
    /**
     * 删除
     */
    String GENERAL_STATE_DELETE = "1900";


    /***-----------------------------------------资产库存-------------------------------------------------*/

    /**
     * 未售
     */
    String INVENTORY_STATE_ENABLE = "0";
    /**
     * 已售
     */
    String INVENTORY_STATE_DISABLE = "1";
    /**
     * 锁定
     */
    String INVENTORY_STATE_LOCK = "2";


    /**
     * 发行
     */
    String INVENTORY_SALE_TYPE_ISSUE = "1";
    /**
     * 空投
     */
    String INVENTORY_SALE_TYPE_AIRDROP = "2";
    /**
     * 活动
     */
    String INVENTORY_SALE_TYPE_ACTIVITY = "3";


    /**
     * 资产
     */
    String INVENTORY_LOCK_TYPE_ISSUE = "1";
    /**
     * 空投
     */
    String INVENTORY_LOCK_TYPE_AIRDROP = "2";
    /**
     * 活动
     */
    String INVENTORY_LOCK_TYPE_ACTIVITY = "3";
    /**
     * 盲盒
     */
    String INVENTORY_LOCK_TYPE_BLIND_BOX = "4";

    /***-----------------------------------------数字资产状态-------------------------------------------------*/

    /**
     * 已上架
     */
    String DIG_ASSET_STATE_ENABLE = "1000";
    /**
     * 资产配置
     */
    String DIG_ASSET_STATE_CONFIG = "1100";
    /**
     * 审核中
     */
    String DIG_ASSET_STATE_AUDITING = "1200";
    /**
     * 审核驳回
     */
    String DIG_ASSET_STATE_REJECT = "1300";
    /**
     * 强制下架
     */
    String DIG_ASSET_STATE_DOWN = "1400";
    /**
     * 库存生成中
     */
    String DIG_ASSET_STATE_STOCKING = "1500";
    /**
     * 审核通过
     */
    String DIG_ASSET_STATE_PASS = "1600";


    /***-----------------------------------------数字资产订单交易类型-------------------------------------------------*/

    /**
     * 发行购买
     */
    String DIG_ORDER_TRADETYPE_ISSUEBUY = "issueBuy";

    /***-----------------------------------------数字资产订单交易渠道-------------------------------------------------*/

    /**
     * 支付宝H5
     */
    String DIG_ORDER_CHANNEL_ALIPAYH5 = "alipayH5";
    /**
     * 微信H5
     */
    String DIG_ORDER_CHANNEL_WECHATH5 = "wechatH5";
    /**
     * 银联网关支付
     */
    String DIG_ORDER_CHANNEL_UNION_GATEWAY_PAY = "unionGatewayPay";
    /***-----------------------------------------数字资产订单状态-------------------------------------------------*/

    /**
     * 待支付
     */
    String DIG_ORDER_STATE_UNPAID = "UNPAID";
    /**
     * 已支付
     */
    String DIG_ORDER_STATE_PAID = "PAID";
    /**
     * 交易取消
     */
    String DIG_ORDER_STATE_CLOSED = "CLOSED";
    /**
     * 超时关闭
     */
    String DIG_ORDER_STATE_TIME_CLOSED = "TIME_CLOSED";
    /**
     * 退款中
     */
    String DIG_ORDER_STATE_REFUNDING = "UNREFUND";
    /**
     * 退款完成
     */
    String DIG_ORDER_STATE_REFUNDED = "REFUND";


    /**
     * 订单号前缀
     */
    String DIG_ORDER_CODE_PREFIX = "DIG_";


    /***-----------------------------------------数字资产获取方式-------------------------------------------------*/

    /**
     * 发售购买
     */
    String DIG_ACQUIRE_TYPE_SALE_BUY = "sale_buy";
    /**
     * 活动获得
     */
    String DIG_ACQUIRE_TYPE_ACTIVITY = "activity";
    /**
     * 空投
     */
    String DIG_ACQUIRE_TYPE_AIRDROP = "airdrop";
    /**
     * 合成
     */
    String DIG_ACQUIRE_TYPE_SYNTHESIS = "synthesis";
    /**
     * 盲盒
     */
    String DIG_ACQUIRE_TYPE_BLIND_BOX = "blind_box";


    /***-----------------------------------------数字资产权益是否兑换------------------------------------------------*/

    /**
     * 未兑换
     */
    Integer DIG_ASSET_IS_REDEEMED_NO = 0;
    /**
     * 已兑换
     */
    Integer DIG_ASSET_IS_REDEEMED_YES = 1;


    /***-----------------------------------------数字资产是否是盲盒------------------------------------------------*/

    /**
     * 不是盲盒
     */
    String DIG_ASSET_IS_BLIND_BOX_NO = "0";
    /**
     * 是盲盒
     */
    String DIG_ASSET_IS_BLIND_BOX_YES = "1";

    /***-----------------------------------------数字资产data状态-------------------------------------------------*/

    /**
     * 已完成
     */
    String DIG_ASSET_DATA_STATE_ENABLE = "1000";
    /**
     * 上链中
     */
    String DIG_ASSET_DATA_STATE_ING = "1100";

    /***-----------------------------------------权益券库存-------------------------------------------------*/

    /**
     * 未使用
     */
    String COUPON_INVENTORY_STATE_ENABLE = "0";
    /**
     * 已使用
     */
    String COUPON_INVENTORY_STATE_DISABLE = "1";
    /**
     * 锁定
     */
    String COUPON_INVENTORY_STATE_LOCK = "2";

    /**
     * 资产
     */
    String COUPON_INVENTORY_SALE_TYPE_ISSUE = "1";
    /**
     * 空投
     */
    String COUPON_INVENTORY_SALE_TYPE_AIRDROP = "2";
    /**
     * 活动
     */
    String COUPON_INVENTORY_SALE_TYPE_ACTIVITY = "3";
    /**
     * 盲盒
     */
    String COUPON_INVENTORY_SALE_TYPE_BLIND_BOX = "4";

    /***-----------------------------------------权限-------------------------------------------------*/

    /**
     * 管理员
     */
    String ROLE_COMMON_ADMIN = "admin";
    /**
     * 发行平台专区运营
     */
    String ROLE_DIG_ZONE_OPERATE = "digZoneOperate";
    /**
     * 发行平台专区管理
     */
    String ROLE_DIG_ZONE_MANAGE = "digZoneAdmin";


    /***-----------------------------------------盲盒奖品类型-------------------------------------------------*/

    /**
     * 权益券
     */
    String DIG_BLIND_BOX_PRIZE_TYPE_COUPON = "COUPON";
    /**
     * 数字资产
     */
    String DIG_BLIND_BOX_PRIZE_TYPE_DIG_ASSET = "ASSET";

    /***-----------------------------------------盲盒开启状态-------------------------------------------------*/
    /**
     * 未开启
     */
    String DIG_BLIND_BOX_PRIZE_STATE_ENABLE = "0";
    /**
     * 已开启
     */
    String DIG_BLIND_BOX_PRIZE_STATE_DISABLE = "1";

    /***-----------------------------------------用户积分账号状态-------------------------------------------------*/

    /**
     * 正常
     */
    String USER_POINTS_STATE_NORMAL = "NORMAL";
    /**
     * 冻结
     */
    String USER_POINTS_STATE_FROZEN = "FROZEN";
    /**------------------------------------------------订单超时时间（分钟）-----------------------------------*/
    long DIG_ORDER_OVERTIME = 10;
}
