package com.ruoyi.issue.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 活动相关配置属性
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@Component
@ConfigurationProperties(prefix = "ruoyi.activity")
public class ActivityConfig {

    /**
     * 邀请排行榜配置
     */
    private InviteRank inviteRank = new InviteRank();

    /**
     * 邀请排行榜配置类
     */
    @Data
    public static class InviteRank {
        /**
         * 需要排除的用户名前缀列表
         */
        private List<String> excludePrefixes;

        /**
         * 排行榜显示前多少名（不设置则显示全部）
         */
        private Integer displayLimit;
    }
}
