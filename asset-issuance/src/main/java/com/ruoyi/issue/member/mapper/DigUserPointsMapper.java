package com.ruoyi.issue.member.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.issue.member.domain.DigUserPoints;

/**
 * 用户积分账户Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
public interface DigUserPointsMapper extends BaseMapper<DigUserPoints>
{
    /**
     * 查询用户积分账户
     * 
     * @param ID 用户积分账户主键
     * @return 用户积分账户
     */
    public DigUserPoints selectDigUserPointsByID(Long ID);

    /**
     * 查询用户积分账户列表
     * 
     * @param digUserPoints 用户积分账户
     * @return 用户积分账户集合
     */
    public List<DigUserPoints> selectDigUserPointsList(DigUserPoints digUserPoints);

    /**
     * 新增用户积分账户
     * 
     * @param digUserPoints 用户积分账户
     * @return 结果
     */
    public int insertDigUserPoints(DigUserPoints digUserPoints);

    /**
     * 修改用户积分账户
     * 
     * @param digUserPoints 用户积分账户
     * @return 结果
     */
    public int updateDigUserPoints(DigUserPoints digUserPoints);

    /**
     * 删除用户积分账户
     * 
     * @param ID 用户积分账户主键
     * @return 结果
     */
    public int deleteDigUserPointsByID(Long ID);

    /**
     * 批量删除用户积分账户
     * 
     * @param IDs 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDigUserPointsByIDs(Long[] IDs);
}
