package com.ruoyi.issue.member.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.issue.member.mapper.DigUserPointsMapper;
import com.ruoyi.issue.member.domain.DigUserPoints;
import com.ruoyi.issue.member.service.IDigUserPointsService;

/**
 * 用户积分账户Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DigUserPointsServiceImpl extends ServiceImpl<DigUserPointsMapper, DigUserPoints> implements IDigUserPointsService
{

    private final DigUserPointsMapper digUserPointsMapper;

    /**
     * 查询用户积分账户
     * 
     * @param ID 用户积分账户主键
     * @return 用户积分账户
     */
    @Override
    public DigUserPoints selectDigUserPointsByID(Long ID)
    {
        return digUserPointsMapper.selectDigUserPointsByID(ID);
    }

    /**
     * 查询用户积分账户列表
     * 
     * @param digUserPoints 用户积分账户
     * @return 用户积分账户
     */
    @Override
    public List<DigUserPoints> selectDigUserPointsList(DigUserPoints digUserPoints)
    {
        return digUserPointsMapper.selectDigUserPointsList(digUserPoints);
    }

    /**
     * 新增用户积分账户
     * 
     * @param digUserPoints 用户积分账户
     * @return 结果
     */
    @Override
    public int insertDigUserPoints(DigUserPoints digUserPoints)
    {
        return digUserPointsMapper.insertDigUserPoints(digUserPoints);
    }

    /**
     * 修改用户积分账户
     * 
     * @param digUserPoints 用户积分账户
     * @return 结果
     */
    @Override
    public int updateDigUserPoints(DigUserPoints digUserPoints)
    {
        return digUserPointsMapper.updateDigUserPoints(digUserPoints);
    }

    /**
     * 批量删除用户积分账户
     * 
     * @param IDs 需要删除的用户积分账户主键
     * @return 结果
     */
    @Override
    public int deleteDigUserPointsByIDs(Long[] IDs)
    {
        return digUserPointsMapper.deleteDigUserPointsByIDs(IDs);
    }

    /**
     * 删除用户积分账户信息
     * 
     * @param ID 用户积分账户主键
     * @return 结果
     */
    @Override
    public int deleteDigUserPointsByID(Long ID)
    {
        return digUserPointsMapper.deleteDigUserPointsByID(ID);
    }
}
