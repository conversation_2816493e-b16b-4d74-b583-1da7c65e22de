package com.ruoyi.issue.member.controller;

import java.sql.Wrapper;
import java.time.LocalDateTime;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.issue.common.constant.Constants;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.issue.member.domain.DigUserPoints;
import com.ruoyi.issue.member.service.IDigUserPointsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 用户积分账户Controller
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@RestController
@RequestMapping("/member/userPonints")
public class DigUserPointsController extends BaseController
{
    @Autowired
    private IDigUserPointsService digUserPointsService;

    /**
     * 查询用户积分账户列表
     */
    @PreAuthorize("@ss.hasPermi('member:userPonints:list')")
    @GetMapping("/list")
    public TableDataInfo list(DigUserPoints digUserPoints)
    {
        startPage();
        List<DigUserPoints> list = digUserPointsService.selectDigUserPointsList(digUserPoints);
        return getDataTable(list);
    }

    /**
     * 导出用户积分账户列表
     */
    @PreAuthorize("@ss.hasPermi('member:userPonints:export')")
    @Log(title = "用户积分账户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DigUserPoints digUserPoints)
    {
        List<DigUserPoints> list = digUserPointsService.selectDigUserPointsList(digUserPoints);
        ExcelUtil<DigUserPoints> util = new ExcelUtil<DigUserPoints>(DigUserPoints.class);
        util.exportExcel(response, list, "用户积分账户数据");
    }

    /**
     * 获取用户积分账户详细信息
     */
    @PreAuthorize("@ss.hasPermi('member:userPonints:query')")
    @GetMapping(value = "/{ID}")
    public AjaxResult getInfo(@PathVariable("ID") Long ID)
    {
        return success(digUserPointsService.selectDigUserPointsByID(ID));
    }

    /**
     * 新增用户积分账户
     */
    @PreAuthorize("@ss.hasPermi('member:userPonints:add')")
    @Log(title = "用户积分账户", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DigUserPoints digUserPoints)
    {
        return toAjax(digUserPointsService.insertDigUserPoints(digUserPoints));
    }

    /**
     * 修改用户积分账户
     */
    @PreAuthorize("@ss.hasPermi('member:userPonints:edit')")
    @Log(title = "用户积分账户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DigUserPoints digUserPoints)
    {
        return toAjax(digUserPointsService.updateDigUserPoints(digUserPoints));
    }

    /**
     * 删除用户积分账户
     */
    @PreAuthorize("@ss.hasPermi('member:userPonints:remove')")
    @Log(title = "用户积分账户", businessType = BusinessType.DELETE)
	@DeleteMapping("/{IDs}")
    public AjaxResult remove(@PathVariable Long[] IDs)
    {
        return toAjax(digUserPointsService.deleteDigUserPointsByIDs(IDs));
    }

    /**-----------------------------------------客户端----------------------------------------------*/

    @GetMapping("/getUserPoints")
    public AjaxResult getUserPoints()
    {
    	Long userId = getUserId();
    	DigUserPoints digUserPoints = digUserPointsService.getOne(Wrappers.<DigUserPoints>lambdaQuery()
                .eq(DigUserPoints::getUserId, userId));
    	if(digUserPoints == null) {
    		digUserPoints = new DigUserPoints();
    		digUserPoints.setUserId(userId);
    		digUserPoints.setPoints(0L);
            digUserPoints.setStatus(Constants.USER_POINTS_STATE_NORMAL);
            digUserPoints.setCreateDate(LocalDateTime.now());
    		digUserPointsService.insertDigUserPoints(digUserPoints);
    	}
    	return success(digUserPoints);
    }
}
