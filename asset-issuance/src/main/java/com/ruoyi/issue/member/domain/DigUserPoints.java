package com.ruoyi.issue.member.domain;

import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.issue.common.entity.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;

/**
 * 用户积分账户对象 dig_user_points
 * 
 * <AUTHOR>
 * @date 2025-07-27
 */
@Data
public class DigUserPoints extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "ID", type = IdType.AUTO)
    private Long ID;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 当前积分 */
    @Excel(name = "当前积分")
    private Long points;

    /** 状态(NORMAL:正常/FROZEN:冻结) */
    @Excel(name = "状态(NORMAL:正常/FROZEN:冻结)")
    private String status;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDate;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateDate;


}
