package com.ruoyi.issue.assetRec.domain;

import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.issue.common.entity.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;

/**
 * 资产热门推荐对象 dig_asset_hot_recommend
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
public class DigAssetHotRecommend extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 推荐记录ID */
    private Long recId;

    /** 资产ID */
    @Excel(name = "资产ID")
    private Long assetId;

    /** 资产名称 */
    @Excel(name = "资产名称")
    private String assetName;

    /** 推荐类型 */
    @Excel(name = "推荐类型")
    private String recType;

    /** 推荐位序号 */
    @Excel(name = "推荐位序号")
    private Long recPosition;

    /** 展示开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "展示开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss ")
    private LocalDateTime startTime;

    /** 展示结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "展示结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /** 点击次数 */
    @Excel(name = "点击次数")
    private Long clickCount;

    /** 状态代码 */
    @Excel(name = "状态代码")
    private String statusCd;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createStaff;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDate;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateDate;

    private String remark;
}
