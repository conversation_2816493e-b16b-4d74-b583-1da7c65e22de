package com.ruoyi.issue.assetRec.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.issue.asset.domain.DigDigitalAsset;
import com.ruoyi.issue.asset.mapper.DigDigitalAssetMapper;
import com.ruoyi.issue.common.constant.Constants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.ruoyi.issue.assetRec.mapper.DigAssetHotRecommendMapper;
import com.ruoyi.issue.assetRec.domain.DigAssetHotRecommend;
import com.ruoyi.issue.assetRec.service.DigAssetHotRecommendService;

/**
 * 资产热门推荐Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DigAssetHotRecommendServiceImpl extends ServiceImpl<DigAssetHotRecommendMapper, DigAssetHotRecommend> implements DigAssetHotRecommendService {

    private final DigAssetHotRecommendMapper digAssetHotRecommendMapper;

    private final DigDigitalAssetMapper digDigitalAssetMapper;


    private final RedisCache redisCache;

    /**
     * 查询资产热门推荐
     *
     * @param recId 资产热门推荐主键
     * @return 资产热门推荐
     */
    @Override
    public DigAssetHotRecommend selectDigAssetHotRecommendByRecId(Long recId) {
        return digAssetHotRecommendMapper.selectDigAssetHotRecommendByRecId(recId);
    }

    /**
     * 查询资产热门推荐列表
     *
     * @param digAssetHotRecommend 资产热门推荐
     * @return 资产热门推荐
     */
    @Override
    public List<DigAssetHotRecommend> selectDigAssetHotRecommendList(DigAssetHotRecommend digAssetHotRecommend) {
        return digAssetHotRecommendMapper.selectDigAssetHotRecommendList(digAssetHotRecommend);
    }

    /**
     * 新增资产热门推荐
     *
     * @param digAssetHotRecommend 资产热门推荐
     * @return 结果
     */
    @Override
    public int insertDigAssetHotRecommend(DigAssetHotRecommend digAssetHotRecommend) {
        return digAssetHotRecommendMapper.insertDigAssetHotRecommend(digAssetHotRecommend);
    }

    /**
     * 修改资产热门推荐
     *
     * @param digAssetHotRecommend 资产热门推荐
     * @return 结果
     */
    @Override
    public int updateDigAssetHotRecommend(DigAssetHotRecommend digAssetHotRecommend) {
        return digAssetHotRecommendMapper.updateDigAssetHotRecommend(digAssetHotRecommend);
    }

    /**
     * 批量删除资产热门推荐
     *
     * @param recIds 需要删除的资产热门推荐主键
     * @return 结果
     */
    @Override
    public int deleteDigAssetHotRecommendByRecIds(Long[] recIds) {
        return digAssetHotRecommendMapper.deleteDigAssetHotRecommendByRecIds(recIds);
    }

    /**
     * 删除资产热门推荐信息
     *
     * @param recId 资产热门推荐主键
     * @return 结果
     */
    @Override
    public int deleteDigAssetHotRecommendByRecId(Long recId) {
        return digAssetHotRecommendMapper.deleteDigAssetHotRecommendByRecId(recId);
    }


    @Override
    public AjaxResult listUp(DigAssetHotRecommend digAssetHotRecommend) {
        List<Long> assetIds = baseMapper.selectList(Wrappers.query(digAssetHotRecommend)).stream().map(DigAssetHotRecommend::getAssetId).collect(Collectors.toList());
        List<DigDigitalAsset> list = digDigitalAssetMapper.selectList(Wrappers.<DigDigitalAsset>lambdaQuery()
                .select(DigDigitalAsset::getAssetId, DigDigitalAsset::getAssetName)
                .like(StringUtils.isNotEmpty(digAssetHotRecommend.getAssetName()), DigDigitalAsset::getAssetName, digAssetHotRecommend.getAssetName())
                .notIn(ObjectUtil.isNotEmpty(assetIds), DigDigitalAsset::getAssetId, assetIds)
                .eq(DigDigitalAsset::getStatusCd, Constants.DIG_ASSET_STATE_ENABLE));
        return AjaxResult.success(list);
    }

    @Override
    public List<DigDigitalAsset> clientList(DigAssetHotRecommend digAssetHotRecommend) {
//        redisCache.setCacheObject(CacheConstants.DIG_ASSET_BROWSE_COUNT_KEY, digAssetHotRecommend);
        List<DigAssetHotRecommend> digAssetHotRecommends = baseMapper.selectList(Wrappers.<DigAssetHotRecommend>lambdaQuery()
                .eq(StringUtils.isNotEmpty(digAssetHotRecommend.getRecType()), DigAssetHotRecommend::getRecType, digAssetHotRecommend.getRecType())
                .eq(DigAssetHotRecommend::getStatusCd, Constants.DIG_ASSET_STATE_ENABLE)
                .le(DigAssetHotRecommend::getStartTime, LocalDateTime.now())
                .ge(DigAssetHotRecommend::getEndTime, LocalDateTime.now())
                .orderByDesc(DigAssetHotRecommend::getRecPosition));
        if (digAssetHotRecommends.size() > 0) {
            List<Long> assetIds = digAssetHotRecommends.stream().map(DigAssetHotRecommend::getAssetId).collect(Collectors.toList());
            return digDigitalAssetMapper.selectList(Wrappers.<DigDigitalAsset>lambdaQuery()
                    .in(DigDigitalAsset::getAssetId, assetIds)
                    .eq(DigDigitalAsset::getStatusCd, Constants.DIG_ASSET_STATE_ENABLE)
            );
        }
        return null;
    }
}
