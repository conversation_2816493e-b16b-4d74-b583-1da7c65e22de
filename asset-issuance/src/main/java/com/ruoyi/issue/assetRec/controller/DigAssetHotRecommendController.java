package com.ruoyi.issue.assetRec.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.ObjectUtil;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.issue.asset.domain.DigDigitalAsset;
import com.ruoyi.issue.asset.service.DigDigitalAssetService;
import com.ruoyi.issue.common.constant.Constants;
import com.ruoyi.issue.issuer.service.DigAssetIssuerService;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.issue.assetRec.domain.DigAssetHotRecommend;
import com.ruoyi.issue.assetRec.service.DigAssetHotRecommendService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 资产热门推荐Controller
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@RestController
@Api(value = "资产热门推荐管理", tags = "资产热门推荐管理")
@RequestMapping("/assetRec/assetRec")
public class DigAssetHotRecommendController extends BaseController {
    @Autowired
    private DigAssetHotRecommendService digAssetHotRecommendService;
    @Autowired
    private AttachmentInfoService attachmentInfoService;
    @Autowired
    private DigDigitalAssetService digDigitalAssetService;

    @Autowired
    private DigAssetIssuerService digAssetIssuerService;

    /**
     * 查询资产热门推荐列表
     */
    @PreAuthorize("@ss.hasPermi('assetRec:assetRec:list')")
    @GetMapping("/list")
    @ApiOperation("查询资产热门推荐列表")
    public TableDataInfo list(DigAssetHotRecommend digAssetHotRecommend) {
        startPage();
        List<DigAssetHotRecommend> list = digAssetHotRecommendService.selectDigAssetHotRecommendList(digAssetHotRecommend);
        return getDataTable(list);
    }

    /**
     * 导出资产热门推荐列表
     */
    @PreAuthorize("@ss.hasPermi('assetRec:assetRec:export')")
    @Log(title = "资产热门推荐", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出资产热门推荐列表")
    public void export(HttpServletResponse response, DigAssetHotRecommend digAssetHotRecommend) {
        List<DigAssetHotRecommend> list = digAssetHotRecommendService.selectDigAssetHotRecommendList(digAssetHotRecommend);
        ExcelUtil<DigAssetHotRecommend> util = new ExcelUtil<DigAssetHotRecommend>(DigAssetHotRecommend.class);
        util.exportExcel(response, list, "资产热门推荐数据");
    }

    /**
     * 获取资产热门推荐详细信息
     */
    @PreAuthorize("@ss.hasPermi('assetRec:assetRec:query')")
    @GetMapping(value = "/{recId}")
    @ApiOperation("获取资产热门推荐详细信息")
    public AjaxResult getInfo(@PathVariable("recId") Long recId) {
        return success(digAssetHotRecommendService.selectDigAssetHotRecommendByRecId(recId));
    }

    /**
     * 新增资产热门推荐
     */
    @PreAuthorize("@ss.hasPermi('assetRec:assetRec:add')")
    @Log(title = "资产热门推荐", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增资产热门推荐")
    public AjaxResult add(@RequestBody DigAssetHotRecommend digAssetHotRecommend) {
        digAssetHotRecommend.setStatusCd(Constants.GENERAL_STATE_ENABLE);
        return toAjax(digAssetHotRecommendService.insertDigAssetHotRecommend(digAssetHotRecommend));
    }

    /**
     * 修改资产热门推荐
     */
    @PreAuthorize("@ss.hasPermi('assetRec:assetRec:edit')")
    @Log(title = "资产热门推荐", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改资产热门推荐")
    public AjaxResult edit(@RequestBody DigAssetHotRecommend digAssetHotRecommend) {
        return toAjax(digAssetHotRecommendService.updateDigAssetHotRecommend(digAssetHotRecommend));
    }

    /**
     * 删除资产热门推荐
     */
    @PreAuthorize("@ss.hasPermi('assetRec:assetRec:remove')")
    @Log(title = "资产热门推荐", businessType = BusinessType.DELETE)
    @DeleteMapping("/{recIds}")
    @ApiOperation("删除资产热门推荐")
    public AjaxResult remove(@PathVariable Long[] recIds) {
        return toAjax(digAssetHotRecommendService.deleteDigAssetHotRecommendByRecIds(recIds));
    }

    /**
     * 上架资产列表
     */
    @PreAuthorize("@ss.hasPermi('assetRec:assetRec:list')")
    @GetMapping("/list/up")
    @ApiOperation("查询上架资产列表")
    public AjaxResult listUp(DigAssetHotRecommend digAssetHotRecommend) {
        return digAssetHotRecommendService.listUp(digAssetHotRecommend);
    }

    /**
     * -----------------------------------------客户端，不需要登录--------------------------------------------------
     */

    @GetMapping("/client/list")
    @Anonymous
    @ApiOperation("客户端获取热门推荐资产列表")
    public AjaxResult clientList(DigAssetHotRecommend digAssetHotRecommend) {
        List<DigDigitalAsset> digDigitalAssets = digAssetHotRecommendService.clientList(digAssetHotRecommend);
        if (ObjectUtil.isNotEmpty(digDigitalAssets)){
            digDigitalAssets.forEach(asset -> {
                asset.setAssetCover(attachmentInfoService.getObjectUrl(asset.getAssetCover()));
                asset.setIssuers(digAssetIssuerService.selectIssuerListByAssetId(asset.getAssetId()));
                asset.setAssetCoverThumbnail(attachmentInfoService.getObjectUrl(asset.getAssetCoverThumbnail()));
                asset.setInventoryNum(digDigitalAssetService.isSoldOut(asset.getAssetId()));
            });
        }
        return success(digDigitalAssets);
    }
}
