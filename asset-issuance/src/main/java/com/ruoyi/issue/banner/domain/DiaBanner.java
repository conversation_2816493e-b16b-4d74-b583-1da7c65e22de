package com.ruoyi.issue.banner.domain;

import java.time.LocalDateTime;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.issue.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;

/**
 * Banner图对象 dia_banner
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
@ApiModel(value = "Banner图对象")
public class DiaBanner extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** banner图片 */
    @Excel(name = "banner图片")
    @ApiModelProperty(value = "banner图片")
    private String imageUrl;

    /** 跳转类型 */
    @Excel(name = "跳转类型")
    @ApiModelProperty(value = "跳转类型")
    private String jumpType;

    /** 内容ID */
    @Excel(name = "内容ID")
    @ApiModelProperty(value = "内容ID")
    private String contentId;

    /** banner标题 */
    @Excel(name = "banner标题")
    @ApiModelProperty(value = "banner标题")
    private String title;

    /** 排序权重 */
    @Excel(name = "排序权重")
    @ApiModelProperty(value = "排序权重")
    private Long sortOrder;

    /** 状态：0-禁用 1-启用 */
    @Excel(name = "状态：0-禁用 1-启用")
    @ApiModelProperty(value = "状态：0-禁用 1-启用")
    private String statusCd;

    /** 展示开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "展示开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "展示开始时间")
    private LocalDateTime startTime;

    /** 展示结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "展示结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "展示结束时间")
    private LocalDateTime endTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateDate;

}
