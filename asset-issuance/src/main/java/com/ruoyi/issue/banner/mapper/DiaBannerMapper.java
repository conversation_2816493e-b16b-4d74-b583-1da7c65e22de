package com.ruoyi.issue.banner.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.issue.banner.domain.DiaBanner;

/**
 * Banner图Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
public interface DiaBannerMapper extends BaseMapper<DiaBanner>
{
    /**
     * 查询Banner图
     * 
     * @param id Banner图主键
     * @return Banner图
     */
    public DiaBanner selectDiaBannerById(Long id);

    /**
     * 查询Banner图列表
     * 
     * @param diaBanner Banner图
     * @return Banner图集合
     */
    public List<DiaBanner> selectDiaBannerList(DiaBanner diaBanner);

    /**
     * 新增Banner图
     * 
     * @param diaBanner Banner图
     * @return 结果
     */
    public int insertDiaBanner(DiaBanner diaBanner);

    /**
     * 修改Banner图
     * 
     * @param diaBanner Banner图
     * @return 结果
     */
    public int updateDiaBanner(DiaBanner diaBanner);

    /**
     * 删除Banner图
     * 
     * @param id Banner图主键
     * @return 结果
     */
    public int deleteDiaBannerById(Long id);

    /**
     * 批量删除Banner图
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDiaBannerByIds(Long[] ids);
}
