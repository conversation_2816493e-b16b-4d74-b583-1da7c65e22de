package com.ruoyi.issue.asset.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.issue.asset.domain.DigAssetInventory;
import com.ruoyi.issue.asset.domain.DigAssetInventoryVo;
import com.ruoyi.issue.asset.domain.DigDigitalAsset;
import com.ruoyi.issue.asset.domain.DigDigitalAssetAddVo;
import com.ruoyi.issue.pay.domain.DigOrderCreateVo;

import java.util.List;

/**
 * 数字资产Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface DigAssetInventoryService extends IService<DigAssetInventory>
{

    /***
     * 生成库存
     */
    public void generateInventory(Long assetId);


    void setInventoryToRedis(Long assetId, String saleType, List<DigAssetInventory> digAssetInventoryList);

    void refreshInventory(Long assetId, String saleType);

    DigAssetInventory getInventory(DigOrderCreateVo digOrderCreateVo);

    /***
     * 放回库存
     */
    void returnInventory(DigOrderCreateVo digOrderCreateVo);

    List<DigAssetInventoryVo> getInventoryList(String saleType);
}
