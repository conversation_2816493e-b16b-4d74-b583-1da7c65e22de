package com.ruoyi.issue.asset.domain;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.issue.common.entity.BaseEntity;
import com.ruoyi.issue.issuer.domain.DigAssetIssuer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;

/**
 * 数字资产对象 dig_digital_asset
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
@ApiModel(value = "数字资产对象")
public class DigDigitalAsset extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 资产ID，主键 */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "资产ID，主键")
    private Long assetId;

    /** 资产名称 */
    @Excel(name = "资产名称")
    @ApiModelProperty(value = "资产名称")
    private String assetName;

    /** 资产封面图URL */
    @Excel(name = "资产封面图URL")
    @ApiModelProperty(value = "资产封面图URL")
    private String assetCover;

    /** 资产缩略图URL */
    @Excel(name = "资产缩略图URL")
    @ApiModelProperty(value = "资产缩略图URL")
    private String assetCoverThumbnail;

    /** 资产文件URL */
    @Excel(name = "资产文件URL")
    @ApiModelProperty(value = "资产文件URL")
    private String assetFile;

    /** 发行方ID列表(多个以逗号隔开) */
    @Excel(name = "发行方ID列表(多个以逗号隔开)")
    @ApiModelProperty(value = "发行方ID列表(多个以逗号隔开)")
    private String issuerIds;

    /** 资产类型 */
    @Excel(name = "资产类型")
    @ApiModelProperty(value = "资产类型")
    private String assetType;

    /** 资产等级 */
    @Excel(name = "资产等级")
    @ApiModelProperty(value = "资产等级")
    private Long assetLevel;

    /** 开售时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开售时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开售时间")
    private LocalDateTime saleStartTime;

    /** 是否是盲盒（0：不是，1：是） */
    @Excel(name = "是否是盲盒")
    @ApiModelProperty(value = "是否是盲盒")
    private String isBlindBox;

    /** 发行数量 */
    @Excel(name = "发行数量")
    @ApiModelProperty(value = "发行数量")
    private Long issueQuantity;

    /** 个人限购数量 */
    @Excel(name = "个人限购数量")
    @ApiModelProperty(value = "个人限购数量")
    private Long individualLimit;

    /** 企业限购数量 */
    @Excel(name = "企业限购数量")
    @ApiModelProperty(value = "企业限购数量")
    private Long enterpriseLimit;

    /** 空投数量 */
    @Excel(name = "空投数量")
    @ApiModelProperty(value = "空投数量")
    private Long airdropQuantity;

    /** 活动数量 */
    @Excel(name = "活动数量")
    @ApiModelProperty(value = "活动数量")
    private Long activityQuantity;

    /** 发行价格 */
    @Excel(name = "发行价格")
    @ApiModelProperty(value = "发行价格")
    private BigDecimal issuePrice;

    /** 所属系列ID */
    @Excel(name = "所属系列ID")
    @ApiModelProperty(value = "所属系列ID")
    private Long seriesId;

    /** 资产关键字(多个以逗号隔开) */
    @Excel(name = "资产关键字(多个以逗号隔开)")
    @ApiModelProperty(value = "资产关键字(多个以逗号隔开)")
    private String assetKeywords;

    /** 资产简介 */
    @Excel(name = "资产简介")
    @ApiModelProperty(value = "资产简介")
    private String assetDesc;

    /** 资产介绍图片URL(多个以逗号隔开) */
    @Excel(name = "资产介绍图片URL(多个以逗号隔开)")
    @ApiModelProperty(value = "资产介绍图片URL(多个以逗号隔开)")
    private String introImages;

    /** 驳回原因 */
    @Excel(name = "驳回原因")
    @ApiModelProperty(value = "驳回原因")
    private String rejectionReason;

    /** 状态代码 */
    @Excel(name = "状态代码")
    @ApiModelProperty(value = "状态代码")
    private String statusCd;

    /** 状态时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "状态时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "状态时间")
    private LocalDateTime statusDate;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty(value = "创建人")
    private String createStaff;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    /** 修改人 */
    @Excel(name = "修改人")
    @ApiModelProperty(value = "修改人")
    private String updateStaff;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateDate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @TableField(exist = false)
    @ApiModelProperty(value = "发行方列表")
    private List<DigAssetIssuer> issuers;

    @TableField(exist = false)
    @ApiModelProperty(value = "库存数量")
    private int inventoryNum;

    @TableField(exist = false)
    @ApiModelProperty(value = "系列名称")
    private String seriesName;

    @TableField(exist = false)
    @ApiModelProperty(value = "专区名称")
    private String zoneName;


    @TableField(exist = false)
    @ApiModelProperty(value = "总数")
    private Long totalQuantity;
}
