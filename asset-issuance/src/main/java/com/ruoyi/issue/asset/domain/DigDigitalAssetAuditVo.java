package com.ruoyi.issue.asset.domain;

import com.ruoyi.issue.rule.domain.DigSaleRule;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 数字资产对象 dig_digital_asset
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
@ApiModel(value = "数字资产对象")
public class DigDigitalAssetAuditVo extends DigDigitalAsset
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "系列ID")
    private Long  seriesId;

    @ApiModelProperty(value = "专区ID")
    private Long zoneId;

    @ApiModelProperty(value = "系列名称")
    private String seriesName;
    
    @ApiModelProperty(value = "专区名称")
    private String zoneName;
    
    @ApiModelProperty(value = "发行方名称列表")
    private List<String> issuerNames;
    
    @ApiModelProperty(value = "销售规则列表")
    private List<DigSaleRule> digSaleRuleList;

}
