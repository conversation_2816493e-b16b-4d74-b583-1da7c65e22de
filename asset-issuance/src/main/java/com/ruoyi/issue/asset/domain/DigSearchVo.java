package com.ruoyi.issue.asset.domain;

import com.ruoyi.issue.series.domain.DigAssetSeries;
import com.ruoyi.issue.zone.domain.DigAssetZone;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "数字资产检索返回对象")
public class DigSearchVo {

    @ApiModelProperty(value = "搜索关键字")
    private String searchValue;
    
    @ApiModelProperty(value = "资产列表")
    private List<DigDigitalAsset>  assets;
    
    @ApiModelProperty(value = "系列列表")
    private List<DigAssetSeries> series;
    
    @ApiModelProperty(value = "专区列表")
    private List<DigAssetZone> zones;
}
