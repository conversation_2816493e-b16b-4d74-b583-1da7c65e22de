package com.ruoyi.issue.asset.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 数字资产对象 dig_digital_asset
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
@ApiModel(value = "数字资产对象")
public class DigDigitalAssetQueryVo
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "系列ID")
    private Long  seriesId;

    @ApiModelProperty(value = "专区ID")
    private Long zoneId;

    @ApiModelProperty(value = "资产名称")
    private String assetName;

    @ApiModelProperty(value = "资产类型")
    private String assetType;

    @ApiModelProperty(value = "资产等级")
    private Long assetLevel;

    @ApiModelProperty(value = "资产关键字")
    private String assetKeywords;

    @ApiModelProperty(value = "状态代码")
    private String statusCd;

    @ApiModelProperty(value = "发行方ID")
    private Long issuerId;

    @ApiModelProperty(value = "开售时间")
    private LocalDateTime saleStartTime;

}
