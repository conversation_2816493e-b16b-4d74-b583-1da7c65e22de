package com.ruoyi.issue.asset.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.issue.asset.domain.*;
import com.ruoyi.issue.asset.mapper.DigAssetInventoryMapper;
import com.ruoyi.issue.asset.service.DigAssetInventoryService;
import com.ruoyi.issue.common.constant.Constants;
import com.ruoyi.issue.issuer.domain.DigAssetIssuerRel;
import com.ruoyi.issue.issuer.mapper.DigAssetIssuerRelMapper;
import com.ruoyi.issue.series.domain.DigAssetSeriesRel;
import com.ruoyi.issue.series.mapper.DigAssetSeriesRelMapper;
import com.ruoyi.issue.zone.domain.DigAssetZoneRel;
import com.ruoyi.issue.zone.mapper.DigAssetZoneRelMapper;
import com.ruoyi.issue.zone.service.DigAssetZoneService;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.ruoyi.issue.asset.mapper.DigDigitalAssetMapper;
import com.ruoyi.issue.asset.service.DigDigitalAssetService;

/**
 * 数字资产Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DigDigitalAssetServiceImpl extends ServiceImpl<DigDigitalAssetMapper, DigDigitalAsset> implements DigDigitalAssetService {

    private final DigDigitalAssetMapper digDigitalAssetMapper;

    private final DigAssetIssuerRelMapper digAssetIssuerRelMapper;

    private final DigAssetSeriesRelMapper digAssetSeriesRelMapper;

    private final DigAssetZoneRelMapper digAssetZoneRelMapper;

    private final DigAssetZoneService digAssetZoneService;

    private final DigAssetInventoryService digAssetInventoryService;

    private final RedisCache redisCache;

    private final AttachmentInfoService attachmentInfoService;

    /**
     * 查询数字资产
     *
     * @param assetId 数字资产主键
     * @return 数字资产
     */
    @Override
    public DigDigitalAssetAddVo selectDigDigitalAssetByAssetId(Long assetId) {
        DigDigitalAsset digDigitalAsset = baseMapper.selectById(assetId);
        DigDigitalAssetAddVo addVo = new DigDigitalAssetAddVo();
        BeanUtil.copyProperties(digDigitalAsset, addVo);
        addVo.setIssuerIds(getIssuerIds(assetId));
        addVo.setSeriesId(getSeriesId(assetId));
        if (ObjectUtil.isNotEmpty(addVo.getZoneId())) {
            addVo.setZoneId(getZoneId(assetId));
        }
        return addVo;
    }

    private Long getZoneId(Long assetId) {
        return digAssetZoneRelMapper.selectOne(Wrappers.<DigAssetZoneRel>lambdaQuery().eq(DigAssetZoneRel::getAssetId, assetId)).getZoneId();
    }

    private Long getSeriesId(Long assetId) {
        return digAssetSeriesRelMapper.selectOne(Wrappers.<DigAssetSeriesRel>lambdaQuery().eq(DigAssetSeriesRel::getAssetId, assetId)).getSeriesId();
    }

    private String getIssuerIds(Long assetId) {
        List<DigAssetIssuerRel> digAssetIssuerRels = digAssetIssuerRelMapper.selectList(Wrappers.<DigAssetIssuerRel>lambdaQuery().eq(DigAssetIssuerRel::getAssetId, assetId));
        return digAssetIssuerRels.stream().map(DigAssetIssuerRel::getIssuerId).map(String::valueOf).collect(Collectors.joining(","));
    }

    /**
     * 查询数字资产列表
     *
     * @param digDigitalAsset 数字资产
     * @return 数字资产
     */
    @Override
    public List<DigDigitalAsset> selectDigDigitalAssetList(DigDigitalAsset digDigitalAsset, List<SysRole> roles, Long userId) {
        List<Long> ids = digAssetZoneService.selectOperatorIdsByRoleIds(roles, userId);
//        List<DigDigitalAsset> list = digDigitalAssetMapper.selectDigDigitalAssetList(digDigitalAsset);
        List<DigDigitalAsset> list = digDigitalAssetMapper.selectList(Wrappers.<DigDigitalAsset>lambdaQuery()
                .in(ObjectUtil.isNotEmpty(ids), DigDigitalAsset::getCreateStaff, ids)
                .eq(StringUtils.isNotEmpty(digDigitalAsset.getStatusCd()), DigDigitalAsset::getStatusCd, digDigitalAsset.getStatusCd())
                .like(StringUtils.isNotEmpty(digDigitalAsset.getAssetName()), DigDigitalAsset::getAssetName, digDigitalAsset.getAssetName())
                .eq(ObjectUtil.isNotEmpty(digDigitalAsset.getAssetType()), DigDigitalAsset::getAssetType, digDigitalAsset.getAssetType())
                .eq(ObjectUtil.isNotEmpty(digDigitalAsset.getAssetLevel()), DigDigitalAsset::getAssetLevel, digDigitalAsset.getAssetLevel())
                .like(StringUtils.isNotEmpty(digDigitalAsset.getAssetKeywords()), DigDigitalAsset::getAssetKeywords, digDigitalAsset.getAssetKeywords())
                .orderByDesc(DigDigitalAsset::getSaleStartTime));

        list.forEach(asset -> {
            asset.setAssetCover(attachmentInfoService.getObjectUrl(asset.getAssetCover()));
            asset.setAssetCoverThumbnail(attachmentInfoService.getObjectUrl(asset.getAssetCoverThumbnail()));
        });
        return list;
    }

    /**
     * 新增数字资产
     *
     * @param digDigitalAsset 数字资产
     * @return 结果
     */
    @Override
    public int insertDigDigitalAsset(DigDigitalAsset digDigitalAsset) {
        return digDigitalAssetMapper.insert(digDigitalAsset);
    }

    /**
     * 修改数字资产
     *
     * @param digDigitalAsset 数字资产
     * @return 结果
     */
    @Override
    public int updateDigDigitalAsset(DigDigitalAsset digDigitalAsset) {
        return digDigitalAssetMapper.updateDigDigitalAsset(digDigitalAsset);
    }

    /**
     * 批量删除数字资产
     *
     * @param assetIds 需要删除的数字资产主键
     * @return 结果
     */
    @Override
    public int deleteDigDigitalAssetByAssetIds(Long[] assetIds) {
        return digDigitalAssetMapper.deleteDigDigitalAssetByAssetIds(assetIds);
    }

    /**
     * 删除数字资产信息
     *
     * @param assetId 数字资产主键
     * @return 结果
     */
    @Override
    public int deleteDigDigitalAssetByAssetId(Long assetId) {
        return digDigitalAssetMapper.deleteDigDigitalAssetByAssetId(assetId);
    }

    @Override
    public List<DigDigitalAsset> queryAssetList(DigDigitalAssetQueryVo digitalAssetQueryVo) {
        List<Long> assetIds = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(digitalAssetQueryVo.getIssuerId())) {
            List<Long> issueAssetIds = digAssetIssuerRelMapper.selectList(Wrappers.<DigAssetIssuerRel>lambdaQuery().eq(DigAssetIssuerRel::getIssuerId, digitalAssetQueryVo.getIssuerId())).stream().map(DigAssetIssuerRel::getAssetId).collect(Collectors.toList());
            if (issueAssetIds.isEmpty()) return new ArrayList<>();
            assetIds.addAll(issueAssetIds);
        }
        if (ObjectUtil.isNotEmpty(digitalAssetQueryVo.getSeriesId())) {
            List<Long> seriesAssetIds = digAssetSeriesRelMapper.selectList(Wrappers.<DigAssetSeriesRel>lambdaQuery().eq(DigAssetSeriesRel::getSeriesId, digitalAssetQueryVo.getSeriesId())).stream().map(DigAssetSeriesRel::getAssetId).collect(Collectors.toList());
            if (seriesAssetIds.isEmpty()) return new ArrayList<>();
            assetIds.addAll(seriesAssetIds);
        }
        if (ObjectUtil.isNotEmpty(digitalAssetQueryVo.getZoneId())) {
            List<Long> zoneAssetIds = digAssetZoneRelMapper.selectList(Wrappers.<DigAssetZoneRel>lambdaQuery().eq(DigAssetZoneRel::getZoneId, digitalAssetQueryVo.getZoneId())).stream().map(DigAssetZoneRel::getAssetId).collect(Collectors.toList());
            if (zoneAssetIds.isEmpty()) return new ArrayList<>();
            assetIds.addAll(zoneAssetIds);
        }

        List<DigDigitalAsset> digDigitalAssets = baseMapper.selectList(Wrappers.<DigDigitalAsset>lambdaQuery()
                .select(DigDigitalAsset::getAssetId, DigDigitalAsset::getAssetName, DigDigitalAsset::getAssetType, DigDigitalAsset::getAssetLevel, DigDigitalAsset::getAssetKeywords, DigDigitalAsset::getAssetCover, DigDigitalAsset::getAssetCoverThumbnail, DigDigitalAsset::getIssuePrice, DigDigitalAsset::getSaleStartTime, DigDigitalAsset::getIssueQuantity)
                .in(ObjectUtil.isNotEmpty(assetIds), DigDigitalAsset::getAssetId, assetIds)
                .like(StringUtils.isNotEmpty(digitalAssetQueryVo.getAssetName()), DigDigitalAsset::getAssetName, digitalAssetQueryVo.getAssetName())
                .eq(StringUtils.isNotEmpty(digitalAssetQueryVo.getAssetType()), DigDigitalAsset::getAssetType, digitalAssetQueryVo.getAssetType())
                .eq(ObjectUtil.isNotEmpty(digitalAssetQueryVo.getAssetLevel()), DigDigitalAsset::getAssetLevel, digitalAssetQueryVo.getAssetLevel())
                .like(StringUtils.isNotEmpty(digitalAssetQueryVo.getAssetKeywords()), DigDigitalAsset::getAssetKeywords, digitalAssetQueryVo.getAssetKeywords())
                .eq(StringUtils.isNotEmpty(digitalAssetQueryVo.getStatusCd()), DigDigitalAsset::getStatusCd, digitalAssetQueryVo.getStatusCd())
                .ge(ObjectUtil.isNotEmpty(digitalAssetQueryVo.getSaleStartTime()), DigDigitalAsset::getSaleStartTime, digitalAssetQueryVo.getSaleStartTime())
                .orderByDesc(DigDigitalAsset::getSaleStartTime)
        );
        return digDigitalAssets;
    }

    @Override
    public int isSoldOut(Long assetId) {
        String countKey = CacheConstants.INVENTORY_COUNT_KEY + assetId + "_" + Constants.INVENTORY_SALE_TYPE_ISSUE;
        if (!redisCache.hasKey(countKey)) {
            digAssetInventoryService.refreshInventory(assetId, Constants.INVENTORY_SALE_TYPE_ISSUE);
        }
        return redisCache.getCacheObject(countKey);
    }

    @Override
    public String getCoverById(Long assetId) {
        DigDigitalAsset digDigitalAsset = baseMapper.selectById(assetId);
        if (digDigitalAsset != null) {
            return attachmentInfoService.getObjectUrl(digDigitalAsset.getAssetCoverThumbnail());
        }
        return null;
    }

    @Override
    public List<DigDigitalAsset> getUnconfiguredBoxList(List<Long> ids) {
        return baseMapper.selectList(Wrappers.<DigDigitalAsset>lambdaQuery()
                .select(DigDigitalAsset::getAssetId, DigDigitalAsset::getAssetName, DigDigitalAsset::getAssetCover, DigDigitalAsset::getIssueQuantity, DigDigitalAsset::getActivityQuantity, DigDigitalAsset::getAirdropQuantity)
                .notIn(ObjectUtil.isNotEmpty(ids), DigDigitalAsset::getAssetId, ids)
                .eq(DigDigitalAsset::getStatusCd, Constants.DIG_ASSET_STATE_ENABLE)
                .eq(DigDigitalAsset::getIsBlindBox, "1"));
    }

    @Override
    public List<DigAssetInventoryVo> getInventoryList(String saleType) {
        List<DigAssetInventoryVo> list = digAssetInventoryService.getInventoryList(saleType);
        list.forEach(item -> item.setAssetName(baseMapper.selectDigDigitalAssetByAssetId(item.getAssetId()).getAssetName()));
        return list;
    }

    @Override
    public void lockInventory(Long assetId, int quantity, String lockType, Long lockRelId) {
        Long count = digAssetInventoryService.count(Wrappers.<DigAssetInventory>lambdaQuery()
                .eq(DigAssetInventory::getAssetId, assetId)
                .eq(DigAssetInventory::getStatusCd, Constants.INVENTORY_STATE_ENABLE)
                .eq(DigAssetInventory::getSaleType, Constants.INVENTORY_SALE_TYPE_AIRDROP));
        if (count < quantity) {
            throw new RuntimeException("库存不足");
        } else {
            digAssetInventoryService.update(null, Wrappers.<DigAssetInventory>lambdaUpdate()
                    .set(DigAssetInventory::getStatusCd, Constants.INVENTORY_STATE_LOCK)
                    .set(DigAssetInventory::getLockType, lockType)
                    .set(DigAssetInventory::getLockRelId, lockRelId)
                    .eq(DigAssetInventory::getAssetId, assetId)
                    .eq(DigAssetInventory::getStatusCd, Constants.INVENTORY_STATE_ENABLE)
                    .eq(DigAssetInventory::getSaleType, Constants.INVENTORY_SALE_TYPE_AIRDROP)
                    .last("limit " + quantity));
        }
    }

    @Override
    public void unlockInventory(String lockType, Long lockRelId) {
        digAssetInventoryService.update(null, Wrappers.<DigAssetInventory>lambdaUpdate()
                .set(DigAssetInventory::getStatusCd, Constants.INVENTORY_STATE_ENABLE)
                .set(DigAssetInventory::getLockType, null)
                .set(DigAssetInventory::getLockRelId, null)
                .eq(DigAssetInventory::getStatusCd, Constants.INVENTORY_STATE_LOCK)
                .eq(DigAssetInventory::getSaleType, Constants.INVENTORY_SALE_TYPE_AIRDROP)
                .eq(DigAssetInventory::getLockType, lockType)
                .eq(DigAssetInventory::getLockRelId, lockRelId));
    }


}
