package com.ruoyi.issue.asset.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.issue.asset.domain.DigAssetInventory;
import com.ruoyi.issue.asset.domain.DigAssetInventoryVo;
import com.ruoyi.issue.asset.domain.DigAssetPavilionRel;

import java.util.List;

public interface DigAssetInventoryMapper extends BaseMapper<DigAssetInventory> {
    void insertDigAssetInventoryBatch(List<DigAssetInventory> digAssetInventoryIssueList);

    void updateStatusByCode(String assetCode, String statusCd);

    List<DigAssetInventoryVo> getInventoryList(String saleType);
}
