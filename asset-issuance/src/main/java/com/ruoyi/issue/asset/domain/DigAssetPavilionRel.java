package com.ruoyi.issue.asset.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("dig_asset_pavilion_rel")
public class DigAssetPavilionRel extends Model<DigAssetPavilionRel> {
    /**
     * 资产ID
     */
    @TableId(type= IdType.AUTO)
    @ApiModelProperty(value = "资产ID")
    private Long assetId;


    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createDate;
}
