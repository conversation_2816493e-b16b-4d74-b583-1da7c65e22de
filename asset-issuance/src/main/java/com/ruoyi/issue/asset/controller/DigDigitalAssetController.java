package com.ruoyi.issue.asset.controller;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.issue.asset.domain.*;
import com.ruoyi.issue.asset.service.DigAssetInventoryService;
import com.ruoyi.issue.asset.vo.MyViewNumVo;
import com.ruoyi.issue.assetData.domain.DigDataAsset;
import com.ruoyi.issue.assetData.service.IDigDataAssetService;
import com.ruoyi.issue.common.constant.Constants;
import com.ruoyi.issue.issuer.domain.DigAssetIssuerRel;
import com.ruoyi.issue.issuer.service.DigAssetIssuerService;
import com.ruoyi.issue.quartz.domain.NewJob;
import com.ruoyi.issue.quartz.service.JobService;
import com.ruoyi.issue.rule.service.DigSaleRuleService;
import com.ruoyi.issue.series.domain.DigAssetSeries;
import com.ruoyi.issue.series.domain.DigAssetSeriesRel;
import com.ruoyi.issue.series.service.DigAssetSeriesService;
import com.ruoyi.issue.zone.domain.DigAssetZone;
import com.ruoyi.issue.zone.domain.DigAssetZoneRel;
import com.ruoyi.issue.zone.service.DigAssetZoneService;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import com.ruoyi.scwt.identify.entity.Identify;
import com.ruoyi.scwt.identify.entity.WcUser;
import com.ruoyi.scwt.identify.mapper.WcUserMapper;
import com.ruoyi.scwt.identify.service.IdentifyService;
import com.ruoyi.scwt.sop.dto.WCDACDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.issue.asset.service.DigDigitalAssetService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 数字资产Controller
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@RestController
@Api(value = "dig_asset", tags = "数字资产信息")
@RequestMapping("/asset/asset")
public class DigDigitalAssetController extends BaseController {
    @Autowired
    private DigDigitalAssetService digDigitalAssetService;

    @Autowired
    private DigAssetIssuerService digAssetIssuerService;

    @Autowired
    private DigAssetSeriesService digAssetSeriesService;

    @Autowired
    private DigAssetZoneService digAssetZoneService;

    @Autowired
    private DigSaleRuleService digSaleRuleService;

    @Autowired
    private DigAssetInventoryService digAssetInventoryService;

    @Autowired
    private AttachmentInfoService attachmentInfoService;

    @Autowired
    private WcUserMapper wcUserMapper;

    @Autowired
    private IDigDataAssetService digDataAssetService;

    @Autowired
    private RedisCache redisCache;


    /**
     * 查询数字资产列表
     */
    @PreAuthorize("@ss.hasPermi('asset:asset:list')")
    @GetMapping("/list")
    @ApiOperation("查询数字资产列表")
    public TableDataInfo list(DigDigitalAsset digDigitalAsset) {
        startPage();
        List<DigDigitalAsset> list = digDigitalAssetService.selectDigDigitalAssetList(digDigitalAsset, getLoginUser().getUser().getRoles(), getUserId());
        return getDataTable(list);
    }

    /**
     * 导出数字资产列表
     */
    @PreAuthorize("@ss.hasPermi('asset:asset:export')")
    @Log(title = "数字资产", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出数字资产列表")
    public void export(HttpServletResponse response, DigDigitalAsset digDigitalAsset) {
        List<DigDigitalAsset> list = digDigitalAssetService.selectDigDigitalAssetList(digDigitalAsset, getLoginUser().getUser().getRoles(), getUserId());
        ExcelUtil<DigDigitalAsset> util = new ExcelUtil<DigDigitalAsset>(DigDigitalAsset.class);
        util.exportExcel(response, list, "数字资产数据");
    }

    /**
     * 获取数字资产详细信息
     */
    @PreAuthorize("@ss.hasPermi('asset:asset:query')")
    @GetMapping(value = "/{assetId}")
    @ApiOperation("获取数字资产详细信息")
    public AjaxResult getInfo(@PathVariable("assetId") Long assetId) {
        DigDigitalAssetAddVo addVo = digDigitalAssetService.selectDigDigitalAssetByAssetId(assetId);
        addVo.setAssetCover(attachmentInfoService.getObjectUrl(addVo.getAssetCover()));
        addVo.setAssetFile(attachmentInfoService.getObjectUrl(addVo.getAssetFile()));
        addVo.setIntroImages(attachmentInfoService.getObjectUrl(addVo.getIntroImages()));
        return success(addVo);
    }

    /**
     * 新增数字资产
     */
    @PreAuthorize("@ss.hasPermi('asset:asset:add')")
    @Log(title = "数字资产", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增数字资产")
    @Transactional
    public AjaxResult add(@RequestBody DigDigitalAssetAddVo addVo) {
        DigDigitalAsset digDigitalAsset = new DigDigitalAsset();
        BeanUtil.copyProperties(addVo, digDigitalAsset);
        digDigitalAsset.setCreateStaff(String.valueOf(getUserId()));
        digDigitalAsset.setStatusCd(Constants.DIG_ASSET_STATE_CONFIG);
        digDigitalAssetService.save(digDigitalAsset);
//        AjaxResult ajaxResult = toAjax(digDigitalAssetService.insertDigDigitalAsset(digDigitalAsset));
        if (StringUtils.isNotEmpty(addVo.getIssuerIds())) {
            for (String issuerId : addVo.getIssuerIds().split(",")) {
                DigAssetIssuerRel issuerRel = new DigAssetIssuerRel();
                issuerRel.setAssetId(digDigitalAsset.getAssetId());
                issuerRel.setIssuerId(Long.valueOf(issuerId));
                digAssetIssuerService.insertRel(issuerRel);
            }
        }
        if (ObjectUtil.isNotEmpty(addVo.getSeriesId())) {
            DigAssetSeriesRel seriesRel = new DigAssetSeriesRel();
            seriesRel.setAssetId(digDigitalAsset.getAssetId());
            seriesRel.setSeriesId(addVo.getSeriesId());
            digAssetSeriesService.insertRel(seriesRel);
        }
        if (ObjectUtil.isNotEmpty(addVo.getZoneId())) {
            DigAssetZoneRel zoneRel = new DigAssetZoneRel();
            zoneRel.setAssetId(digDigitalAsset.getAssetId());
            zoneRel.setZoneId(addVo.getZoneId());
            digAssetZoneService.insertRel(zoneRel);
        }
        return success();
    }

    /**
     * 修改数字资产
     */
    @PreAuthorize("@ss.hasPermi('asset:asset:edit')")
    @Log(title = "数字资产", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改数字资产")
    public AjaxResult edit(@RequestBody DigDigitalAssetAddVo addVo) {
        DigDigitalAsset digDigitalAsset = new DigDigitalAsset();
        BeanUtil.copyProperties(addVo, digDigitalAsset);
        digDigitalAsset.setUpdateStaff(String.valueOf(getUserId()));
        digDigitalAsset.setUpdateDate(LocalDateTime.now());
        digDigitalAssetService.updateDigDigitalAsset(digDigitalAsset);

        digAssetIssuerService.removeRel(digDigitalAsset.getAssetId());
        digAssetSeriesService.removeRel(digDigitalAsset.getAssetId());
        digAssetZoneService.removeRel(digDigitalAsset.getAssetId());

        if (StringUtils.isNotEmpty(addVo.getIssuerIds())) {
            for (String issuerId : addVo.getIssuerIds().split(",")) {
                DigAssetIssuerRel issuerRel = new DigAssetIssuerRel();
                issuerRel.setAssetId(digDigitalAsset.getAssetId());
                issuerRel.setIssuerId(Long.valueOf(issuerId));
                digAssetIssuerService.insertRel(issuerRel);
            }
        }
        if (ObjectUtil.isNotEmpty(addVo.getSeriesId())) {
            DigAssetSeriesRel seriesRel = new DigAssetSeriesRel();
            seriesRel.setAssetId(digDigitalAsset.getAssetId());
            seriesRel.setSeriesId(addVo.getSeriesId());
            digAssetSeriesService.insertRel(seriesRel);
        }
        if (ObjectUtil.isNotEmpty(addVo.getZoneId())) {
            DigAssetZoneRel zoneRel = new DigAssetZoneRel();
            zoneRel.setAssetId(digDigitalAsset.getAssetId());
            zoneRel.setZoneId(addVo.getZoneId());
            digAssetZoneService.insertRel(zoneRel);
        }
//        digDigitalAsset.setStatusCd(Constants.DIG_ASSET_STATE_CONFIG);
        return toAjax(digDigitalAssetService.updateDigDigitalAsset(digDigitalAsset));
    }

    @PreAuthorize("@ss.hasPermi('asset:asset:edit')")
    @Log(title = "数字资产", businessType = BusinessType.UPDATE)
    @PutMapping("/statusCd")
    @ApiOperation("修改数字资产状态")
    public AjaxResult editStatusCd(@RequestBody DigDigitalAssetAddVo addVo) {
        DigDigitalAsset digDigitalAsset = new DigDigitalAsset();
        BeanUtil.copyProperties(addVo, digDigitalAsset);
        return toAjax(digDigitalAssetService.updateDigDigitalAsset(digDigitalAsset));
    }

    /**
     * 删除数字资产
     */
    @PreAuthorize("@ss.hasPermi('asset:asset:remove')")
    @Log(title = "数字资产", businessType = BusinessType.DELETE)
    @DeleteMapping("/{assetIds}")
    public AjaxResult remove(@PathVariable Long[] assetIds) {
        return toAjax(digDigitalAssetService.deleteDigDigitalAssetByAssetIds(assetIds));
    }


    /**
     * 获取数字资产审核详细信息
     */
    @PreAuthorize("@ss.hasPermi('asset:asset:query')")
    @GetMapping(value = "/audit/{assetId}")
    @ApiOperation("获取数字资产审核详细信息")
    public AjaxResult getAuditInfo(@PathVariable("assetId") Long assetId) {
        DigDigitalAssetAddVo addVo = digDigitalAssetService.selectDigDigitalAssetByAssetId(assetId);
        addVo.setAssetCover(attachmentInfoService.getObjectUrl(addVo.getAssetCover()));
        addVo.setAssetFile(attachmentInfoService.getObjectUrl(addVo.getAssetFile()));
        addVo.setIntroImages(attachmentInfoService.getObjectUrl(addVo.getIntroImages()));
        DigDigitalAssetAuditVo auditVo = new DigDigitalAssetAuditVo();
        BeanUtil.copyProperties(addVo, auditVo);
        auditVo.setIssuerNames(digAssetIssuerService.selectIssuerNamesByAssetId(assetId));
        auditVo.setZoneName(digAssetZoneService.selectZoneNameByAssetId(assetId));
        auditVo.setSeriesName(digAssetSeriesService.selectSeriesNameByAssetId(assetId));
        auditVo.setDigSaleRuleList(digSaleRuleService.selectDigSaleRuleListByAssetId(assetId));
        return success(auditVo);
    }

    /**
     * 审核数字资产
     */
    @PreAuthorize("@ss.hasPermi('asset:asset:audit')")
    @Log(title = "数字资产", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    @ApiOperation("审核数字资产")
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult auditAssets(@RequestBody DigDigitalAsset digDigitalAsset) {
        int i = digDigitalAssetService.updateDigDigitalAsset(digDigitalAsset);
        if (digDigitalAsset.getStatusCd().equals(Constants.DIG_ASSET_STATE_STOCKING)) {
            digAssetInventoryService.generateInventory(digDigitalAsset.getAssetId());
            if (digDigitalAsset.getIssueQuantity()>0) {
                digSaleRuleService.setRuleToRedis(digDigitalAsset.getAssetId());
                digDigitalAsset.setStatusCd(Constants.DIG_ASSET_STATE_ENABLE);
            }else {
                digDigitalAsset.setStatusCd(Constants.DIG_ASSET_STATE_PASS);
            }
            digDigitalAsset.setStatusDate(LocalDateTime.now());
            digDigitalAssetService.updateDigDigitalAsset(digDigitalAsset);
        }
        return toAjax(i);
    }

    /**
     * 获取有空投库存的资产列表
     * @param saleType
     */
    @GetMapping("/InventoryList")
    @ApiOperation("获取有库存的资产列表")
    @PreAuthorize("@ss.hasPermi('box:box:add')")
    public AjaxResult getInventoryList(String saleType) {
        List<DigAssetInventoryVo> list = digDigitalAssetService.getInventoryList(saleType);
        return success(list);
    }


    /**-------------------------------客户端，不需要token的接口----------------------------------*/

    /**
     * 获取数字资产列表
     */
    @Anonymous
    @GetMapping("/client/list")
    @ApiOperation("获取数字资产列表")
    public TableDataInfo H5List(DigDigitalAssetQueryVo digitalAssetQueryVo) {
        digitalAssetQueryVo.setStatusCd(Constants.DIG_ASSET_STATE_ENABLE);
        startPage();
        List<DigDigitalAsset> list = digDigitalAssetService.queryAssetList(digitalAssetQueryVo);
        for (DigDigitalAsset asset : list) {
            asset.setAssetCover(attachmentInfoService.getObjectUrl(asset.getAssetCover()));
            asset.setAssetCoverThumbnail(attachmentInfoService.getObjectUrl(asset.getAssetCoverThumbnail()));
            asset.setIssuers(digAssetIssuerService.selectIssuerListByAssetId(asset.getAssetId()));
            asset.setInventoryNum(digDigitalAssetService.isSoldOut(asset.getAssetId()));
        }

        return getDataTable(list);
    }

    /**
     * 获取首页预售数资资产列表
     */
    @Anonymous
    @GetMapping("/client/presale/list")
    @ApiOperation("获取首页预售数资资产列表")
    public TableDataInfo H5PresaleList(DigDigitalAssetQueryVo digitalAssetQueryVo) {
        digitalAssetQueryVo.setStatusCd(Constants.DIG_ASSET_STATE_ENABLE);
        digitalAssetQueryVo.setSaleStartTime(LocalDateTime.now());
        startPage();
        List<DigDigitalAsset> list = digDigitalAssetService.queryAssetList(digitalAssetQueryVo);
        for (DigDigitalAsset asset : list) {
            asset.setAssetCover(attachmentInfoService.getObjectUrl(asset.getAssetCover()));
            asset.setAssetCoverThumbnail(attachmentInfoService.getObjectUrl(asset.getAssetCoverThumbnail()));
            asset.setIssuers(digAssetIssuerService.selectIssuerListByAssetId(asset.getAssetId()));
            asset.setInventoryNum(digDigitalAssetService.isSoldOut(asset.getAssetId()));
        }
        return getDataTable(list);
    }

    /**
     * 获取数字资产详情
     */
    @Anonymous
    @GetMapping("/client/detail/{assetId}")
    @ApiOperation("获取数字资产详情")
    public AjaxResult H5Detail(@PathVariable("assetId") Long assetId) {
        String key = CacheConstants.DIG_ASSET_DETAIL_KEY + assetId;
        if (redisCache.hasKey(key)) {
            DigDigitalAsset digDigitalAsset = redisCache.getCacheObject(key);
            return success(digDigitalAsset);
        } else {
            DigDigitalAsset digDigitalAsset = digDigitalAssetService.selectDigDigitalAssetByAssetId(assetId);
            digDigitalAsset.setAssetCover(attachmentInfoService.getObjectUrl(digDigitalAsset.getAssetCover()));
            digDigitalAsset.setAssetCoverThumbnail(attachmentInfoService.getObjectUrl(digDigitalAsset.getAssetCoverThumbnail()));
            digDigitalAsset.setIntroImages(attachmentInfoService.getObjectUrl(digDigitalAsset.getIntroImages()));
            digDigitalAsset.setIssuers(digAssetIssuerService.selectIssuerListByAssetId(assetId));
            digDigitalAsset.setInventoryNum(digDigitalAssetService.isSoldOut(assetId));
            digDigitalAsset.setSeriesName(digAssetSeriesService.selectSeriesNameByAssetId(assetId));
            digDigitalAsset.setZoneName(digAssetZoneService.selectZoneNameByAssetId(assetId));
            redisCache.setCacheObject(CacheConstants.DIG_ASSET_DETAIL_KEY + assetId, digDigitalAsset, 1, TimeUnit.DAYS);
            return success(digDigitalAsset);
        }
    }

    /***
     * 检索接口
     */
    @Anonymous
    @GetMapping("/client/search")
    @ApiOperation("检索接口")
    public AjaxResult H5Search(DigSearchVo searchVo) {
        DigDigitalAsset digDigitalAsset = new DigDigitalAsset();
        digDigitalAsset.setStatusCd(Constants.DIG_ASSET_STATE_ENABLE);
        digDigitalAsset.setAssetName(searchVo.getSearchValue());
        List<DigDigitalAsset> list = digDigitalAssetService.selectDigDigitalAssetList(digDigitalAsset, null, null);
        DigAssetZone digAssetZone = new DigAssetZone();
        digAssetZone.setStatusCd(Constants.GENERAL_STATE_ENABLE);
        digAssetZone.setZoneName(searchVo.getSearchValue());
        List<DigAssetZone> zones = digAssetZoneService.selectDigAssetZoneList(digAssetZone);
        DigAssetSeries digAssetSeries = new DigAssetSeries();
        digAssetSeries.setStatusCd(Constants.GENERAL_STATE_ENABLE);
        digAssetSeries.setSeriesName(searchVo.getSearchValue());
        List<DigAssetSeries> series = digAssetSeriesService.selectDigAssetSeriesList(digAssetSeries, null, null);
        searchVo.setAssets(list);
        searchVo.setZones(zones);
        searchVo.setSeries(series);
        return AjaxResult.success(searchVo);

    }

    /**
     * 我的页面数量返回
     */

    @GetMapping("/client/my/num")
    @ApiOperation("我的页面数量返回")
    public AjaxResult H5MyNum() {
        WcUser wcUser = wcUserMapper.selectOne(Wrappers.<WcUser>lambdaQuery()
                .eq(WcUser::getUserId, getUserId()));
        long count = digDataAssetService.count(Wrappers.<DigDataAsset>lambdaQuery()
                .eq(DigDataAsset::getUserId, getUserId()));
        MyViewNumVo myViewNum = new MyViewNumVo();
        myViewNum.setUserAddress(ObjectUtil.isNotEmpty(wcUser) && ObjectUtil.isNotEmpty(wcUser.getAddress()) ? wcUser.getAddress() : "暂无");
        myViewNum.setAssetQuantity(count);
        return AjaxResult.success(myViewNum);
    }

    @GetMapping("/client/my/assetsFile/{assetId}")
    @ApiOperation("我的页面资产文件返回")
    public AjaxResult H5MyAssetsFile(@PathVariable("assetId") Long assetId) {
        DigDigitalAsset digitalAsset = digDigitalAssetService.getById(assetId);
        if (ObjectUtil.isEmpty(digitalAsset)) {
            return AjaxResult.warn("暂无数据");
        } else {
            return AjaxResult.success(attachmentInfoService.getObjectUrl(digitalAsset.getAssetFile()));
        }
    }

}
