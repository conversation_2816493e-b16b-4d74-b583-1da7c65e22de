package com.ruoyi.issue.asset.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "我的页面数量统计VO对象")
public class MyViewNumVo {

    /***
     * 用户链上地址
     */
    @ApiModelProperty(value = "用户链上地址")
    private String userAddress;
    
    /***
     * 资产数量
     */
    @ApiModelProperty(value = "资产数量")
    private Long assetQuantity;
}
