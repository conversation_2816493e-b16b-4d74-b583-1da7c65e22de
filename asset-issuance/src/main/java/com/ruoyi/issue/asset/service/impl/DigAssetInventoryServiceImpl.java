package com.ruoyi.issue.asset.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.issue.asset.domain.DigAssetInventory;
import com.ruoyi.issue.asset.domain.DigAssetInventoryVo;
import com.ruoyi.issue.asset.domain.DigDigitalAsset;
import com.ruoyi.issue.asset.mapper.DigAssetInventoryMapper;
import com.ruoyi.issue.asset.mapper.DigDigitalAssetMapper;
import com.ruoyi.issue.asset.service.DigAssetInventoryService;
import com.ruoyi.issue.common.constant.Constants;
import com.ruoyi.issue.pay.domain.DigAssetOrder;
import com.ruoyi.issue.pay.domain.DigOrderCreateVo;
import com.ruoyi.issue.pay.mapper.DigAssetOrderMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import java.util.concurrent.TimeUnit;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Queue;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class DigAssetInventoryServiceImpl extends ServiceImpl<DigAssetInventoryMapper, DigAssetInventory> implements DigAssetInventoryService {

    private final DigDigitalAssetMapper digDigitalAssetMapper;
    private final DigAssetInventoryMapper digAssetInventoryMapper;

    private final DigAssetOrderMapper digAssetOrderMapper;

    private final RedissonClient redissonClient;

    private final RedisCache redisCache;



    @Override
    @Async("threadPoolTaskExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void generateInventory(Long assetId) {
        Long count = baseMapper.selectCount(Wrappers.<DigAssetInventory>lambdaQuery().eq(DigAssetInventory::getAssetId, assetId));
        if (count > 0) {
            log.info("资产ID为{}的资产已生成过库存，请勿重复生成", assetId);
            return;
        }
        DigDigitalAsset digDigitalAsset = digDigitalAssetMapper.selectDigDigitalAssetByAssetId(assetId);
        Long totalQuantity = digDigitalAsset.getIssueQuantity() + digDigitalAsset.getAirdropQuantity() + digDigitalAsset.getActivityQuantity();
        Queue<String> assetCodeList = generateAssetCode(assetId, totalQuantity);
        DigAssetInventory digAssetInventory = new DigAssetInventory();
        digAssetInventory.setAssetId(assetId);
        digAssetInventory.setStorageTime(LocalDateTime.now());
        digAssetInventory.setStatusCd(Constants.INVENTORY_STATE_ENABLE);
        //根据digDigitalAsset的issueQuantity、airdropQuantity、activityQuantity三个数量，只要不为0就分别从assetCodeList中获取编号
        if (digDigitalAsset.getIssueQuantity() > 0) {
            List<DigAssetInventory> digAssetInventoryIssueList = new ArrayList<>();
            for (long i = 0L; i < digDigitalAsset.getIssueQuantity(); i++) {
                DigAssetInventory digAssetInventoryIssue = new DigAssetInventory();
                BeanUtil.copyProperties(digAssetInventory, digAssetInventoryIssue);
                digAssetInventoryIssue.setAssetCode(assetCodeList.poll());
                digAssetInventoryIssue.setSaleType(Constants.INVENTORY_SALE_TYPE_ISSUE);
                digAssetInventoryIssueList.add(digAssetInventoryIssue);
            }
            digAssetInventoryMapper.insertDigAssetInventoryBatch(digAssetInventoryIssueList);
            setInventoryToRedis(assetId, Constants.INVENTORY_SALE_TYPE_ISSUE, digAssetInventoryIssueList);
        }
        if (digDigitalAsset.getAirdropQuantity() > 0) {
            List<DigAssetInventory> digAssetInventoryAirdropList = new ArrayList<>();
            for (long i = 0L; i < digDigitalAsset.getAirdropQuantity(); i++) {
                DigAssetInventory digAssetInventoryAir = new DigAssetInventory();
                BeanUtil.copyProperties(digAssetInventory, digAssetInventoryAir);
                digAssetInventoryAir.setAssetCode(assetCodeList.poll());
                digAssetInventoryAir.setSaleType(Constants.INVENTORY_SALE_TYPE_AIRDROP);
                digAssetInventoryAirdropList.add(digAssetInventoryAir);
            }
            digAssetInventoryMapper.insertDigAssetInventoryBatch(digAssetInventoryAirdropList);
//            setInventoryToRedis(assetId, Constants.INVENTORY_SALE_TYPE_AIRDROP, digAssetInventoryAirdropList);
        }
        if (digDigitalAsset.getActivityQuantity() > 0) {
            List<DigAssetInventory> digAssetInventoryActivityList = new ArrayList<>();
            for (long i = 0L; i < digDigitalAsset.getActivityQuantity(); i++) {
                DigAssetInventory digAssetInventoryActivity = new DigAssetInventory();
                BeanUtil.copyProperties(digAssetInventory, digAssetInventoryActivity);
                digAssetInventoryActivity.setAssetCode(assetCodeList.poll());
                digAssetInventoryActivity.setSaleType(Constants.INVENTORY_SALE_TYPE_ACTIVITY);
                digAssetInventoryActivityList.add(digAssetInventoryActivity);
            }
            digAssetInventoryMapper.insertDigAssetInventoryBatch(digAssetInventoryActivityList);
//            setInventoryToRedis(assetId, Constants.INVENTORY_SALE_TYPE_ACTIVITY, digAssetInventoryActivityList);
        }
    }

    /***
     * 生成资产编号
     */
    private Queue<String> generateAssetCode(Long assetId, Long totalQuantity) {
        //根据要生成的数量，根据“LY#assetId#totalQuantity”totalQuantity由1到totalQuantity,totalQuantity生成资产编号
//        List<String> assetCodeList = new ArrayList<>();
        Queue<String> assetCodeList = new LinkedList<>();
        for (int i = 1; i <= totalQuantity; i++) {
            //i根据最大数位，不够的前面填充0
            String iStr = i + "";
            while (iStr.length() < totalQuantity.toString().length()) {
                iStr = "0" + iStr;
            }
            String assetCode = "LY#" + assetId + "#" + iStr;
            assetCodeList.add(assetCode);
        }
        return assetCodeList;
    }

    /***
     * 将库存存到redis
     */
    @Override
    public void setInventoryToRedis(Long assetId, String saleType, List<DigAssetInventory> digAssetInventoryList) {
        String countKey = CacheConstants.INVENTORY_COUNT_KEY + assetId + "_" + saleType;
        redisCache.setCacheObject(countKey, digAssetInventoryList.size());
        String key = CacheConstants.INVENTORY_QUEUE_KEY + assetId + "_" + saleType;
        digAssetInventoryList.forEach(inventory -> {
            redisCache.leftPushToQueue(key, inventory);
        });
    }

    @Override
    public void refreshInventory(Long assetId, String saleType) {
        List<DigAssetOrder> digAssetOrders = digAssetOrderMapper.selectList(Wrappers.<DigAssetOrder>lambdaQuery()
                .eq(DigAssetOrder::getAssetId, assetId)
                .eq(DigAssetOrder::getTradeType, Constants.DIG_ORDER_TRADETYPE_ISSUEBUY)
                .eq(DigAssetOrder::getStatusCd, Constants.DIG_ORDER_STATE_UNPAID));
        List<String> assetCodes = new ArrayList<>();
        if (digAssetOrders.size() > 0) {
            assetCodes = digAssetOrders.stream().map(DigAssetOrder::getAssetCode).collect(Collectors.toList());
        }
        String listKey = CacheConstants.INVENTORY_QUEUE_KEY + assetId + "_" + saleType;
        List<DigAssetInventory> digAssetInventories = baseMapper.selectList(Wrappers.<DigAssetInventory>lambdaQuery()
                .eq(DigAssetInventory::getAssetId, assetId)
                .eq(DigAssetInventory::getSaleType, saleType)
                .notIn(ObjectUtils.isNotEmpty(assetCodes),DigAssetInventory::getAssetCode, assetCodes)
                .eq(DigAssetInventory::getStatusCd, Constants.INVENTORY_STATE_ENABLE));
        redisCache.deleteQueue(listKey);
        setInventoryToRedis(assetId, saleType, digAssetInventories);
    }

//    @Override
//    public DigAssetInventory getInventory(DigOrderCreateVo digOrderCreateVo) {
//        String countKey = CacheConstants.INVENTORY_COUNT_KEY + digOrderCreateVo.getAssetId() + "_" + Constants.INVENTORY_SALE_TYPE_ISSUE;
//        String listKey = CacheConstants.INVENTORY_QUEUE_KEY + digOrderCreateVo.getAssetId() + "_" + Constants.INVENTORY_SALE_TYPE_ISSUE;
//        if (!redisCache.hasKey(countKey) || !redisCache.hasKey(listKey)) {
//            refreshInventory(digOrderCreateVo.getAssetId(), Constants.INVENTORY_SALE_TYPE_ISSUE);
//        }
//        if ((int) redisCache.getCacheObject(countKey) > 0) {
//            DigAssetInventory digAssetInventory = redisCache.rightPopFromQueue(listKey);
//            if (digAssetInventory != null) {
//                redisCache.decrement(countKey);
//                return digAssetInventory;
//            }
//        }
//        return null;
//    }

    @Override
    public DigAssetInventory getInventory(DigOrderCreateVo digOrderCreateVo) {
        String lockKey = CacheConstants.INVENTORY_LOCK_KEY + digOrderCreateVo.getAssetId() + "_" + Constants.INVENTORY_SALE_TYPE_ISSUE;
        String countKey = CacheConstants.INVENTORY_COUNT_KEY + digOrderCreateVo.getAssetId() + "_" + Constants.INVENTORY_SALE_TYPE_ISSUE;
        String listKey = CacheConstants.INVENTORY_QUEUE_KEY + digOrderCreateVo.getAssetId() + "_" + Constants.INVENTORY_SALE_TYPE_ISSUE;

        // 获取分布式锁
        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 尝试获取锁，最多等待3秒，锁自动释放时间10秒
            if (lock.tryLock(3, 10, TimeUnit.SECONDS)) {
                if (!redisCache.hasKey(countKey) || !redisCache.hasKey(listKey)) {
                    refreshInventory(digOrderCreateVo.getAssetId(), Constants.INVENTORY_SALE_TYPE_ISSUE);
                }
                if ((int) redisCache.getCacheObject(countKey) > 0) {
                    DigAssetInventory digAssetInventory = redisCache.rightPopFromQueue(listKey);
                    if (digAssetInventory != null) {
                        redisCache.decrement(countKey);
                        return digAssetInventory;
                    }
                }
            } else {
                log.warn("获取库存锁失败: {}", lockKey);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取库存锁被中断: {}", e.getMessage());
        } finally {
            // 确保释放锁
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
        return null;
    }

    @Override
    public void returnInventory(DigOrderCreateVo digOrderCreateVo) {
        String lockKey = CacheConstants.INVENTORY_LOCK_KEY + digOrderCreateVo.getAssetId() + "_" + Constants.INVENTORY_SALE_TYPE_ISSUE;
        String countKey = CacheConstants.INVENTORY_COUNT_KEY + digOrderCreateVo.getAssetId() + "_" + Constants.INVENTORY_SALE_TYPE_ISSUE;
        String listKey = CacheConstants.INVENTORY_QUEUE_KEY + digOrderCreateVo.getAssetId() + "_" + Constants.INVENTORY_SALE_TYPE_ISSUE;

        // 获取分布式锁
        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 尝试获取锁，最多等待3秒，锁自动释放时间10秒
            if (lock.tryLock(3, 10, TimeUnit.SECONDS)) {
                if (!redisCache.hasKey(countKey) || !redisCache.hasKey(listKey)) {
                    refreshInventory(digOrderCreateVo.getAssetId(), Constants.INVENTORY_SALE_TYPE_ISSUE);
                }
                DigAssetInventory digAssetInventory=baseMapper.selectOne(Wrappers.<DigAssetInventory>lambdaQuery().eq(DigAssetInventory::getAssetCode, digOrderCreateVo.getAssetCode()));
                redisCache.leftPushToQueue(listKey, digAssetInventory);
                redisCache.increment(countKey);

            } else {
                log.warn("获取库存锁失败: {}", lockKey);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取库存锁被中断: {}", e.getMessage());
        } finally {
            // 确保释放锁
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    @Override
    public List<DigAssetInventoryVo> getInventoryList(String saleType) {
        List<DigAssetInventoryVo> digAssetInventories = baseMapper.getInventoryList(saleType);
        return digAssetInventories;
    }


}
