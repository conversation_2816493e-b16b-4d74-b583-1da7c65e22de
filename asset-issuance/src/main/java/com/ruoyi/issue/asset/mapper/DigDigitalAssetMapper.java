package com.ruoyi.issue.asset.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.issue.asset.domain.DigDigitalAsset;

/**
 * 数字资产Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface DigDigitalAssetMapper extends BaseMapper<DigDigitalAsset>
{
    /**
     * 查询数字资产
     * 
     * @param assetId 数字资产主键
     * @return 数字资产
     */
    public DigDigitalAsset selectDigDigitalAssetByAssetId(Long assetId);

    /**
     * 查询数字资产列表
     * 
     * @param digDigitalAsset 数字资产
     * @return 数字资产集合
     */
    public List<DigDigitalAsset> selectDigDigitalAssetList(DigDigitalAsset digDigitalAsset);

    /**
     * 新增数字资产
     * 
     * @param digDigitalAsset 数字资产
     * @return 结果
     */
    public int insertDigDigitalAsset(DigDigitalAsset digDigitalAsset);

    /**
     * 修改数字资产
     * 
     * @param digDigitalAsset 数字资产
     * @return 结果
     */
    public int updateDigDigitalAsset(DigDigitalAsset digDigitalAsset);

    /**
     * 删除数字资产
     * 
     * @param assetId 数字资产主键
     * @return 结果
     */
    public int deleteDigDigitalAssetByAssetId(Long assetId);

    /**
     * 批量删除数字资产
     * 
     * @param assetIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDigDigitalAssetByAssetIds(Long[] assetIds);
}
