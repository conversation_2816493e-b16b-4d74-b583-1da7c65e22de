package com.ruoyi.issue.box.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.issue.asset.domain.DigDigitalAsset;
import com.ruoyi.issue.asset.service.DigDigitalAssetService;
import com.ruoyi.issue.box.domain.DigUserBlindBox;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.issue.box.domain.DigBlindBox;
import com.ruoyi.issue.box.service.IDigBlindBoxService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 盲盒Controller
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@RestController
@RequestMapping("/box/box")
public class DigBlindBoxController extends BaseController {
    @Autowired
    private IDigBlindBoxService digBlindBoxService;
    @Autowired
    private DigDigitalAssetService digDigitalAssetService;
    @Autowired
    private AttachmentInfoService attachmentInfoService;

    /**
     * 查询盲盒列表
     */
    @PreAuthorize("@ss.hasPermi('box:box:list')")
    @GetMapping("/list")
    public TableDataInfo list(DigBlindBox digBlindBox) {
        startPage();
        List<DigBlindBox> list = digBlindBoxService.selectDigBlindBoxList(digBlindBox);
        for (DigBlindBox box : list) {
            box.setBoxCover(attachmentInfoService.getObjectUrl(box.getBoxCover()));
        }
        return getDataTable(list);
    }

    /**
     * 导出盲盒列表
     */
    @PreAuthorize("@ss.hasPermi('box:box:export')")
    @Log(title = "盲盒", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DigBlindBox digBlindBox) {
        List<DigBlindBox> list = digBlindBoxService.selectDigBlindBoxList(digBlindBox);
        ExcelUtil<DigBlindBox> util = new ExcelUtil<DigBlindBox>(DigBlindBox.class);
        util.exportExcel(response, list, "盲盒数据");
    }

    /**
     * 获取盲盒详细信息
     */
    @PreAuthorize("@ss.hasPermi('box:box:query')")
    @GetMapping(value = "/{boxId}")
    public AjaxResult getInfo(@PathVariable("boxId") Long boxId) {
        return success(digBlindBoxService.selectDigBlindBoxByBoxId(boxId));
    }

    /**
     * 新增盲盒
     */
    @PreAuthorize("@ss.hasPermi('box:box:add')")
    @Log(title = "盲盒", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DigBlindBox digBlindBox) {
        return toAjax(digBlindBoxService.insertDigBlindBox(digBlindBox));
    }

    /**
     * 修改盲盒
     */
    @PreAuthorize("@ss.hasPermi('box:box:edit')")
    @Log(title = "盲盒", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DigBlindBox digBlindBox) {
        return toAjax(digBlindBoxService.updateDigBlindBox(digBlindBox));
    }

    /**
     * 删除盲盒
     */
    @PreAuthorize("@ss.hasPermi('box:box:remove')")
    @Log(title = "盲盒", businessType = BusinessType.DELETE)
    @DeleteMapping("/{boxIds}")
    public AjaxResult remove(@PathVariable Long[] boxIds) {
        return toAjax(digBlindBoxService.deleteDigBlindBoxByBoxIds(boxIds));
    }

    /**
     * 查询未配置的盲盒列表
     */
    @PreAuthorize("@ss.hasPermi('box:box:add')")
    @GetMapping("/listNotConfig")
    public AjaxResult listNotConfig(DigBlindBox digBlindBox) {
        List<DigBlindBox> digBlindBoxes = digBlindBoxService.selectDigBlindBoxList(digBlindBox);
        List<Long> ids = new ArrayList<>();
        if (digBlindBoxes != null && digBlindBoxes.size() > 0) {
            ids = digBlindBoxes.stream().map(DigBlindBox::getAssetId).collect(Collectors.toList());
        }
        List<DigDigitalAsset> unconfiguredBoxList = digDigitalAssetService.getUnconfiguredBoxList(ids);
        return success(unconfiguredBoxList);
    }


    /**-------------------------------------------------------客户端-----------------------------------------------------------*/

    /**
     * 获取用户盲盒列表
     */
    @GetMapping("/getUserBoxList")
    public TableDataInfo getUserBoxList() {
        startPage();
        List<DigUserBlindBox> userBoxList = digBlindBoxService.getUserBoxList(getUserId());
        return getDataTable(userBoxList);
    }

    /**
     * 开启盲盒
     */
    @PostMapping("/openBox")
    public AjaxResult openBox(@RequestBody DigUserBlindBox digUserBlindBox) {
        return digBlindBoxService.openBox(digUserBlindBox.getBoxCode(),getUserId());
    }
}
