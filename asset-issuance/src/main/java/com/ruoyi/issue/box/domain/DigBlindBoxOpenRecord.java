package com.ruoyi.issue.box.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 盲盒开启记录表实体
 * 对应表：dig_blind_box_open_record
 */
@Data
@ApiModel(value = "盲盒开启记录", description = "记录用户开启盲盒的详细信息")
public class DigBlindBoxOpenRecord {

    @ApiModelProperty(value = "记录ID")
    @TableId(value = "record_id", type = IdType.AUTO)
    private Long recordId;

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "盲盒ID")
    private Long boxId;

    @ApiModelProperty(value = "盲盒唯一编号")
    private String boxCode;

    @ApiModelProperty(value = "奖品类型")
    private String prizeType;

    @ApiModelProperty(value = "奖品ID")
    private Long prizeId;

    @ApiModelProperty(value = "奖品编号/兑换码")
    private String prizeCode;

    @ApiModelProperty(value = "开启时间")
    private LocalDateTime openTime;

    @ApiModelProperty(value = "状态代码")
    private String statusCd;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;
} 