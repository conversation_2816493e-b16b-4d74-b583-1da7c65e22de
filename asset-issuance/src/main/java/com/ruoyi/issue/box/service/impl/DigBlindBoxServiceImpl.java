package com.ruoyi.issue.box.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.HashSet;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.issue.asset.domain.DigAssetInventory;
import com.ruoyi.issue.asset.domain.DigDigitalAsset;
import com.ruoyi.issue.asset.domain.DigDigitalAssetAddVo;
import com.ruoyi.issue.asset.mapper.DigAssetInventoryMapper;
import com.ruoyi.issue.asset.service.DigDigitalAssetService;
import com.ruoyi.issue.assetData.domain.DigDataAsset;
import com.ruoyi.issue.assetData.mapper.DigDataAssetMapper;
import com.ruoyi.issue.box.domain.DigBlindBoxOpenRecord;
import com.ruoyi.issue.box.domain.DigBlindBoxPrize;
import com.ruoyi.issue.box.mapper.DigBlindBoxOpenRecordMapper;
import com.ruoyi.issue.box.mapper.DigBlindBoxPrizeMapper;
import com.ruoyi.issue.common.constant.Constants;
import com.ruoyi.issue.common.redis.QueueSender;
import com.ruoyi.issue.coupon.domain.DigCouponCode;
import com.ruoyi.issue.coupon.service.IDigCouponService;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.issue.box.mapper.DigBlindBoxMapper;
import com.ruoyi.issue.box.domain.DigBlindBox;
import com.ruoyi.issue.box.service.IDigBlindBoxService;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.issue.box.mapper.DigUserBlindBoxMapper;
import com.ruoyi.issue.box.domain.DigUserBlindBox;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import java.util.concurrent.TimeUnit;

/**
 * 盲盒主Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DigBlindBoxServiceImpl extends ServiceImpl<DigBlindBoxMapper, DigBlindBox> implements IDigBlindBoxService {

    private final DigBlindBoxMapper digBlindBoxMapper;

    private final DigBlindBoxPrizeMapper digBlindBoxPrizeMapper;

    private final IDigCouponService digCouponService;

    private final DigDigitalAssetService digDigitalAssetService;

    private final DigUserBlindBoxMapper digUserBlindBoxMapper;
    private final DigAssetInventoryMapper digAssetInventoryMapper;
    private final DigBlindBoxOpenRecordMapper digBlindBoxOpenRecordMapper;
    private final AttachmentInfoService attachmentInfoService;
    private final RedissonClient redissonClient;
    private final DigDataAssetMapper digDataAssetMapper;
    private final QueueSender sender;

    /**
     * 查询盲盒主
     *
     * @param boxId 盲盒主主键
     * @return 盲盒主
     */
    @Override
    public DigBlindBox selectDigBlindBoxByBoxId(Long boxId) {
        DigBlindBox box = digBlindBoxMapper.selectDigBlindBoxByBoxId(boxId);
        if (box != null) {
            List<DigBlindBoxPrize> digBlindBoxPrizes = digBlindBoxPrizeMapper.selectList(Wrappers.<DigBlindBoxPrize>lambdaQuery().eq(DigBlindBoxPrize::getBoxId, boxId));
            digBlindBoxPrizes.forEach(digBlindBoxPrize -> {
                if (Constants.DIG_BLIND_BOX_PRIZE_TYPE_COUPON.equals(digBlindBoxPrize.getPrizeType())) {
                    digBlindBoxPrize.setAssetName(digCouponService.selectDigCouponByCouponId(digBlindBoxPrize.getPrizeRelId()).getCouponName());
                } else if (Constants.DIG_BLIND_BOX_PRIZE_TYPE_DIG_ASSET.equals(digBlindBoxPrize.getPrizeType())) {
                    digBlindBoxPrize.setAssetName(digDigitalAssetService.selectDigDigitalAssetByAssetId(digBlindBoxPrize.getPrizeRelId()).getAssetName());
                }
            });
            box.setDigBlindBoxPrizes(digBlindBoxPrizes);
        }
        return box;
    }

    /**
     * 查询盲盒主列表
     *
     * @param digBlindBox 盲盒主
     * @return 盲盒主
     */
    @Override
    public List<DigBlindBox> selectDigBlindBoxList(DigBlindBox digBlindBox) {
        return digBlindBoxMapper.selectDigBlindBoxList(digBlindBox);
    }

    /**
     * 新增盲盒主
     *
     * @param digBlindBox 盲盒主
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertDigBlindBox(DigBlindBox digBlindBox) {
        int i = digBlindBoxMapper.insertDigBlindBox(digBlindBox);
        List<DigBlindBoxPrize> digBlindBoxPrizes = digBlindBox.getDigBlindBoxPrizes();
        digBlindBoxPrizes.forEach(digBlindBoxPrize -> {
            // 保存奖品
            digBlindBoxPrize.setBoxId(digBlindBox.getBoxId());
            digBlindBoxPrizeMapper.insert(digBlindBoxPrize);
            // 锁定库存
            if (Constants.DIG_BLIND_BOX_PRIZE_TYPE_COUPON.equals(digBlindBoxPrize.getPrizeType())) {
                digCouponService.lockInventory(digBlindBoxPrize.getPrizeRelId(), digBlindBoxPrize.getQuantity(), Constants.COUPON_INVENTORY_SALE_TYPE_BLIND_BOX, digBlindBox.getBoxId());
            } else if (Constants.DIG_BLIND_BOX_PRIZE_TYPE_DIG_ASSET.equals(digBlindBoxPrize.getPrizeType())) {
                digDigitalAssetService.lockInventory(digBlindBoxPrize.getPrizeRelId(), digBlindBoxPrize.getQuantity(), Constants.INVENTORY_LOCK_TYPE_BLIND_BOX, digBlindBox.getBoxId());
            }
        });
        return i;
    }

    /**
     * 修改盲盒主
     *
     * @param digBlindBox 盲盒主
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateDigBlindBox(DigBlindBox digBlindBox) {
        digBlindBoxMapper.deleteDigBlindBoxByBoxId(digBlindBox.getBoxId());
        // 删除奖品
        digBlindBoxPrizeMapper.delete(Wrappers.<DigBlindBoxPrize>lambdaQuery().eq(DigBlindBoxPrize::getBoxId, digBlindBox.getBoxId()));
        // 解锁库存
        digCouponService.unlockInventory(Constants.COUPON_INVENTORY_SALE_TYPE_BLIND_BOX, digBlindBox.getBoxId());
        digDigitalAssetService.unlockInventory(Constants.INVENTORY_LOCK_TYPE_BLIND_BOX, digBlindBox.getBoxId());

        return insertDigBlindBox(digBlindBox);
    }

    /**
     * 批量删除盲盒主
     *
     * @param boxIds 需要删除的盲盒主主键
     * @return 结果
     */
    @Override
    public int deleteDigBlindBoxByBoxIds(Long[] boxIds) {
        return digBlindBoxMapper.deleteDigBlindBoxByBoxIds(boxIds);
    }

    /**
     * 删除盲盒主信息
     *
     * @param boxId 盲盒主主键
     * @return 结果
     */
    @Override
    public int deleteDigBlindBoxByBoxId(Long boxId) {
        return digBlindBoxMapper.deleteDigBlindBoxByBoxId(boxId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult openBox(String boxCode, Long userId) {
        // 1. 参数校验、查找用户盲盒
        DigUserBlindBox userBox = digUserBlindBoxMapper.selectOne(
                Wrappers.<DigUserBlindBox>lambdaQuery()
                        .eq(DigUserBlindBox::getUserId, userId)
                        .eq(DigUserBlindBox::getBoxCode, boxCode)
        );
        if (userBox == null) {
            return AjaxResult.error("未找到该盲盒");
        }
        if (Constants.DIG_BLIND_BOX_PRIZE_STATE_DISABLE.equals(userBox.getIsOpened())) {
            return AjaxResult.error("您已开启过该盲盒");
        }
        String lockKey = "blindbox:open:" + userBox.getBoxId();
        RLock lock = redissonClient.getLock(lockKey);
        boolean locked = false;
        try {
            locked = lock.tryLock(5, 3, TimeUnit.SECONDS); // 最多等5秒，锁3秒
            if (!locked) {
                return AjaxResult.error("操作太频繁，请稍后再试");
            }

            // 2. 查询所有可用奖品（status='locked'）
            List<Object> prizeList = new ArrayList<>();
            List<DigCouponCode> couponList = digCouponService.selectCouponCodeList(Constants.COUPON_INVENTORY_SALE_TYPE_BLIND_BOX, userBox.getBoxId());
            if (couponList != null) prizeList.addAll(couponList);

            List<DigAssetInventory> assetList = digAssetInventoryMapper.selectList(
                Wrappers.<DigAssetInventory>lambdaQuery()
                    .eq(DigAssetInventory::getLockType, Constants.INVENTORY_LOCK_TYPE_BLIND_BOX)
                    .eq(DigAssetInventory::getLockRelId, userBox.getBoxId())
            );
            if (assetList != null) prizeList.addAll(assetList);

            if (prizeList.isEmpty()) {
                return AjaxResult.error("奖品池为空，无法开启盲盒");
            }

            // 4. 随机抽奖
            int idx = (int) (Math.random() * prizeList.size());
            Object prize = prizeList.get(idx);
            Object prizeData;
            Long prizeId;
            String prizeCode;
            String prizeType;

            // 5. 根据奖品类型处理
            if (prize instanceof DigCouponCode) {
                DigCouponCode coupon = (DigCouponCode) prize;
                // 设置为已领取
                coupon.setUserId(userId);
                coupon.setUseTime(LocalDateTime.now());
                digCouponService.updateCouponCodeById(coupon);
                prizeData = digCouponService.getCouponInfo(coupon.getCouponCode());
                prizeId = coupon.getCouponId();
                prizeCode = coupon.getCouponCode();
                prizeType = Constants.DIG_BLIND_BOX_PRIZE_TYPE_COUPON;
            } else if (prize instanceof DigAssetInventory) {
                DigAssetInventory asset = (DigAssetInventory) prize;
                // 设置为已转移/已领取
                asset.setStatusCd(Constants.INVENTORY_STATE_DISABLE);
                asset.setRemark(String.valueOf(userId));
                digAssetInventoryMapper.updateById(asset);
                DigDigitalAsset byId = digDigitalAssetService.getById(asset.getAssetId());
                byId.setAssetCover(attachmentInfoService.getObjectUrl(byId.getAssetCover()));
                prizeData = byId;
                prizeId = asset.getAssetId();
                prizeCode = asset.getAssetCode();
                prizeType = Constants.DIG_BLIND_BOX_PRIZE_TYPE_DIG_ASSET;
            } else {
                return AjaxResult.error("奖品类型未知，无法发放");
            }

            // 6. 更新用户盲盒记录为已开启
            userBox.setIsOpened(Constants.DIG_BLIND_BOX_PRIZE_STATE_DISABLE);
            userBox.setOpenTime(java.time.LocalDateTime.now());
            userBox.setUpdateDate(java.time.LocalDateTime.now());
            digUserBlindBoxMapper.updateById(userBox);

            DigBlindBoxOpenRecord openRecord = new DigBlindBoxOpenRecord();
            openRecord.setBoxId(userBox.getBoxId());
            openRecord.setUserId(userId);
            openRecord.setPrizeId(prizeId);
            openRecord.setPrizeCode(prizeCode);
            openRecord.setBoxId(userBox.getBoxId());
            openRecord.setBoxCode(userBox.getBoxCode());
            openRecord.setPrizeType(prizeType);
            openRecord.setOpenTime(userBox.getOpenTime());
            openRecord.setStatusCd(Constants.GENERAL_STATE_ENABLE);
            openRecord.setCreateDate(userBox.getOpenTime());
            digBlindBoxOpenRecordMapper.insert(openRecord);
            if (prizeType.equals(Constants.DIG_BLIND_BOX_PRIZE_TYPE_DIG_ASSET)){
                DigBlindBox box = digBlindBoxMapper.selectDigBlindBoxByBoxId(userBox.getBoxId());
                DigDigitalAsset asset = digDigitalAssetService.getById(box.getAssetId());
                DigDataAsset dataAsset = new DigDataAsset();
                dataAsset.setUserId(userId);
                dataAsset.setAcquireType(Constants.DIG_ACQUIRE_TYPE_BLIND_BOX);
                dataAsset.setAcquireRecordId(openRecord.getRecordId());
                dataAsset.setAssetId(asset.getAssetId());
                dataAsset.setAssetName(asset.getAssetName());
                dataAsset.setAssetCode(((DigAssetInventory) prize).getAssetCode());
                dataAsset.setIsRedeemed(Constants.DIG_ASSET_IS_REDEEMED_NO);
                dataAsset.setStatusCd(Constants.DIG_ASSET_DATA_STATE_ING);
                dataAsset.setCreateDate(LocalDateTime.now());
                dataAsset.setStatusDate(LocalDateTime.now());
                digDataAssetMapper.insert(dataAsset);
                sender.sendMsg(CacheConstants.CHAIN_QUEUE, dataAsset);
            }
            // 7. 返回奖品信息，包含类型
            AjaxResult result = AjaxResult.success("恭喜您获得奖品！");
            result.put("data", prizeData);
            return result;
        } catch (Exception e) {
            log.error("开启盲盒报错--", e);
            // 日志记录
            return AjaxResult.error("系统繁忙，请稍后再试");
        } finally {
            if (locked) lock.unlock();
        }
    }

    @Override
    public List<DigUserBlindBox> getUserBoxList(Long userId) {
        List<DigUserBlindBox> digUserBlindBoxes = digUserBlindBoxMapper.selectList(Wrappers.<DigUserBlindBox>lambdaQuery()
                .eq(DigUserBlindBox::getUserId, userId)
                .eq(DigUserBlindBox::getIsOpened, Constants.DIG_BLIND_BOX_PRIZE_STATE_ENABLE));
        digUserBlindBoxes.forEach(digUserBlindBox -> {
            DigBlindBox box = digBlindBoxMapper.selectDigBlindBoxByBoxId(digUserBlindBox.getBoxId());
            digUserBlindBox.setBoxName(box.getBoxName());
            digUserBlindBox.setBoxCover(attachmentInfoService.getObjectUrl(box.getBoxCover()));
        });
        return digUserBlindBoxes;
    }

}
