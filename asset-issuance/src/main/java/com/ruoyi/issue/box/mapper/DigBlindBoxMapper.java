package com.ruoyi.issue.box.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.issue.box.domain.DigBlindBox;

/**
 * 盲盒主Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface DigBlindBoxMapper extends BaseMapper<DigBlindBox>
{
    /**
     * 查询盲盒主
     * 
     * @param boxId 盲盒主主键
     * @return 盲盒主
     */
    public DigBlindBox selectDigBlindBoxByBoxId(Long boxId);

    /**
     * 查询盲盒主列表
     * 
     * @param digBlindBox 盲盒主
     * @return 盲盒主集合
     */
    public List<DigBlindBox> selectDigBlindBoxList(DigBlindBox digBlindBox);

    /**
     * 新增盲盒主
     * 
     * @param digBlindBox 盲盒主
     * @return 结果
     */
    public int insertDigBlindBox(DigBlindBox digBlindBox);

    /**
     * 修改盲盒主
     * 
     * @param digBlindBox 盲盒主
     * @return 结果
     */
    public int updateDigBlindBox(DigBlindBox digBlindBox);

    /**
     * 删除盲盒主
     * 
     * @param boxId 盲盒主主键
     * @return 结果
     */
    public int deleteDigBlindBoxByBoxId(Long boxId);

    /**
     * 批量删除盲盒主
     * 
     * @param boxIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDigBlindBoxByBoxIds(Long[] boxIds);
}
