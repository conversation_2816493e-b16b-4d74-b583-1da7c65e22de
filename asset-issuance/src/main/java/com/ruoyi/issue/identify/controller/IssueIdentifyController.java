package com.ruoyi.issue.identify.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.issue.identify.domain.IssueIdentify;
import com.ruoyi.scwt.common.controller.AliIdentifyController;
import com.ruoyi.scwt.common.dto.CompanyIdentifyDto;
import com.ruoyi.scwt.common.dto.PersonIdentifyDto;
import com.ruoyi.scwt.common.util.SmsCodeUtils;
import com.ruoyi.scwt.file.entity.AttachmentInfo;
import com.ruoyi.scwt.identify.constant.IdentifyConstants;
import com.ruoyi.scwt.identify.entity.Identify;
import com.ruoyi.scwt.identify.enums.IdentifyEnum;
import com.ruoyi.scwt.identify.service.IdentifyService;
import com.ruoyi.scwt.works.entity.EvidenceOwner;
import com.ruoyi.scwt.works.entity.RegisterOwner;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@Api(value = "发行平台实名认证", tags = "发行平台实名认证")
@RequestMapping("/issue/identify")
public class IssueIdentifyController  extends BaseController {

    @Autowired
    private IdentifyService identifyService;
    @Autowired
    private SmsCodeUtils smsCodeUtils;
    @Autowired
    private AliIdentifyController aliIdentifyController;

    @ApiOperation("新增实名认证")
    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public R save(@RequestBody @Validated IssueIdentify identify) {
        String idNo = identify.getIdNo();
        Long userId = SecurityUtils.getUserId();
        if (null != identifyService.getIdentifyByUserId(userId)) {
            return R.fail("已实名认证，无需重新认证！");
        }
        if (!getLoginUser().getUser().getPhonenumber().equals(identify.getPhone())){
            return R.fail("手机号与登录手机号不一致");
        }
        if (IdentifyEnum.PERSON.getCode().equals(identify.getIdentifyType())) {
            smsCodeUtils.validateSmsCaptcha(identify.getPhone(), identify.getSmsCode());
            Identify people = identifyService.getOne(Wrappers.<Identify>lambdaQuery()
                    .eq(Identify::getIdName, identify.getIdName())
                    .eq(Identify::getIdNo, identify.getIdNo())
                    .eq(Identify::getStatusCd, IdentifyConstants.IDENTIFY_TAKE_EFFECT));
            if (ObjectUtil.isNotNull(people)) {
                return R.fail("该身份证号已实名认证");
            } else {
                identify.setStatusCd(IdentifyConstants.IDENTIFY_TAKE_EFFECT);
                PersonIdentifyDto personIdentifyDto = new PersonIdentifyDto(identify.getIdName(), idNo);
                R r = aliIdentifyController.eidIdentify(personIdentifyDto);
                if (!(Boolean) r.getData()) {
                    return R.fail(r.getMsg());
                }
            }
        } else {
            Identify people = identifyService.getOne(Wrappers.<Identify>lambdaQuery().eq(Identify::getIdName, identify.getIdName())
                    .eq(Identify::getIdNo, identify.getIdNo())
                    .eq(Identify::getLegalName, identify.getLegalName())
                    .eq(Identify::getStatusCd, IdentifyConstants.IDENTIFY_TAKE_EFFECT));
            if (ObjectUtil.isNotNull(people)) {
                return R.fail("该企业已经实名认证");
            } else {
                if (IdentifyEnum.BUSINESS_LICENSE.getCode().equals(identify.getIdType())) {
                    identify.setStatusCd(IdentifyConstants.IDENTIFY_TAKE_EFFECT);
                    CompanyIdentifyDto companyIdentifyDto = new CompanyIdentifyDto(identify.getIdName(), idNo, identify.getLegalName());
                    R r = aliIdentifyController.companyIdentify(companyIdentifyDto);
                    if (!(Boolean) r.getData()) {
                        return R.fail(r.getMsg());
                    }
                }
            }
        }
        identify.setStatusCd(IdentifyConstants.IDENTIFY_TAKE_EFFECT);
        identify.setCreateStaff(String.valueOf(userId));
        identify.setUserId(userId);
        identify.setCreateDate(LocalDateTime.now());
        identify.setStatusDate(LocalDateTime.now());
        Identify identifyVo = new Identify();
        BeanUtil.copyProperties(identify, identifyVo);
        identifyService.save(identifyVo);
        identifyService.sopForeign(identifyVo);
        return R.ok("实名认证成功");
    }
}
