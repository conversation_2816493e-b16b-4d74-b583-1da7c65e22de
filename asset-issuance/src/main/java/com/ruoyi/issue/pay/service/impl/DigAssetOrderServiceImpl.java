package com.ruoyi.issue.pay.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alipay.v3.model.AlipayTradeQueryResponseModel;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.issue.asset.domain.DigAssetInventory;
import com.ruoyi.issue.asset.domain.DigDigitalAsset;
import com.ruoyi.issue.asset.mapper.DigAssetInventoryMapper;
import com.ruoyi.issue.asset.mapper.DigDigitalAssetMapper;
import com.ruoyi.issue.asset.service.DigAssetInventoryService;
import com.ruoyi.issue.assetData.domain.DigDataAsset;
import com.ruoyi.issue.assetData.mapper.DigDataAssetMapper;
import com.ruoyi.issue.assetData.service.IDigDataAssetService;
import com.ruoyi.issue.box.domain.DigBlindBox;
import com.ruoyi.issue.box.domain.DigUserBlindBox;
import com.ruoyi.issue.box.mapper.DigBlindBoxMapper;
import com.ruoyi.issue.box.mapper.DigUserBlindBoxMapper;
import com.ruoyi.issue.common.constant.Constants;
import com.ruoyi.issue.common.redis.QueueSender;
import com.ruoyi.issue.pay.domain.DigOrderCreateVo;
import com.ruoyi.issue.pay.service.*;
import com.ruoyi.scwt.exception.BusinessException;
import com.ruoyi.scwt.pay.constant.OrderConstants;
import com.ruoyi.scwt.pay.service.OrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.ruoyi.issue.pay.mapper.DigAssetOrderMapper;
import com.ruoyi.issue.pay.domain.DigAssetOrder;
import org.springframework.transaction.annotation.Transactional;

/**
 * 数字资产订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-01
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class DigAssetOrderServiceImpl extends ServiceImpl<DigAssetOrderMapper, DigAssetOrder> implements DigAssetOrderService {

    private final DigAssetOrderMapper digAssetOrderMapper;

    private final DigDigitalAssetMapper digDigitalAssetMapper;
    private final DigDataAssetMapper digDataAssetMapper;
    private final DigAssetInventoryMapper digAssetInventoryMapper;
    private final DigUserBlindBoxMapper digUserBlindBoxMapper;
    private final DigBlindBoxMapper digBlindBoxMapper;

    private final RedisQueueService redisQueueService;

    private final AlipayV2Service alipayV2Service;
    private final ChinaumsPayService chinaumsPayService;
    private final QueueSender sender;
    private final DigAssetInventoryService digAssetInventoryService;


    /**
     * 查询数字资产订单
     *
     * @param orderId 数字资产订单主键
     * @return 数字资产订单
     */
    @Override
    public DigAssetOrder selectDigAssetOrderByOrderId(Long orderId) {
        return digAssetOrderMapper.selectDigAssetOrderByOrderId(orderId);
    }

    /**
     * 查询数字资产订单列表
     *
     * @param digAssetOrder 数字资产订单
     * @return 数字资产订单
     */
    @Override
    public List<DigAssetOrder> selectDigAssetOrderList(DigAssetOrder digAssetOrder) {
        return digAssetOrderMapper.selectDigAssetOrderList(digAssetOrder);
    }

    /**
     * 新增数字资产订单
     *
     * @param digAssetOrder 数字资产订单
     * @return 结果
     */
    @Override
    public int insertDigAssetOrder(DigAssetOrder digAssetOrder) {
        return digAssetOrderMapper.insertDigAssetOrder(digAssetOrder);
    }

    /**
     * 修改数字资产订单
     *
     * @param digAssetOrder 数字资产订单
     * @return 结果
     */
    @Override
    public int updateDigAssetOrder(DigAssetOrder digAssetOrder) {
        return digAssetOrderMapper.updateDigAssetOrder(digAssetOrder);
    }

    /**
     * 批量删除数字资产订单
     *
     * @param orderIds 需要删除的数字资产订单主键
     * @return 结果
     */
    @Override
    public int deleteDigAssetOrderByOrderIds(Long[] orderIds) {
        return digAssetOrderMapper.deleteDigAssetOrderByOrderIds(orderIds);
    }

    /**
     * 删除数字资产订单信息
     *
     * @param orderId 数字资产订单主键
     * @return 结果
     */
    @Override
    public int deleteDigAssetOrderByOrderId(Long orderId) {
        return digAssetOrderMapper.deleteDigAssetOrderByOrderId(orderId);
    }

    @Override
    public DigAssetOrder createOrder(DigOrderCreateVo digOrderCreateVo) {
        DigAssetOrder digAssetOrder = new DigAssetOrder();
        DigDigitalAsset asset = digDigitalAssetMapper.selectById(digOrderCreateVo.getAssetId());
        digAssetOrder.setBuyerId(digOrderCreateVo.getBuyerId());
        digAssetOrder.setSellerId(Long.valueOf(asset.getCreateStaff()));
        digAssetOrder.setOrderNo(digOrderCreateVo.getOrderNo());
        digAssetOrder.setAssetId(digOrderCreateVo.getAssetId());
        digAssetOrder.setBuyQuantity(digOrderCreateVo.getBuyQuantity());
        digAssetOrder.setOrderAmount(asset.getIssuePrice().multiply(BigDecimal.valueOf(digOrderCreateVo.getBuyQuantity())).setScale(2, RoundingMode.HALF_UP));
        digAssetOrder.setTradeType(digOrderCreateVo.getTradeType());
        digAssetOrder.setPayChannel(digOrderCreateVo.getPayChannel());
        digAssetOrder.setAssetName(asset.getAssetName());
        digAssetOrder.setAssetCode(digOrderCreateVo.getAssetCode());
        digAssetOrder.setStatusCd(Constants.DIG_ORDER_STATE_UNPAID);
        digAssetOrder.setCreateDate(LocalDateTime.now());
        digAssetOrderMapper.insert(digAssetOrder);
        return digAssetOrder;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void paySuccess(String orderNo) {
        DigAssetOrder digAssetOrder = baseMapper.selectOne(Wrappers.<DigAssetOrder>lambdaQuery().eq(DigAssetOrder::getOrderNo, orderNo));
        if (ObjectUtil.isEmpty(digAssetOrder)) {
            log.error("订单不存在:{}", orderNo);
        } else if (digAssetOrder.getStatusCd().equals(Constants.DIG_ORDER_STATE_PAID)) {
            log.error("订单已支付:{}", orderNo);
        } else {
            digAssetOrder.setStatusCd(Constants.DIG_ORDER_STATE_PAID);
            digAssetOrder.setPayTime(LocalDateTime.now());
            digAssetOrder.setUpdateDate(LocalDateTime.now());
            digAssetOrderMapper.updateDigAssetOrder(digAssetOrder);
            DigDigitalAsset asset = digDigitalAssetMapper.selectById(digAssetOrder.getAssetId());
            digAssetInventoryMapper.update(null, Wrappers.<DigAssetInventory>lambdaUpdate()
                    .set(DigAssetInventory::getStatusCd, Constants.INVENTORY_STATE_DISABLE)
                    .eq(DigAssetInventory::getAssetId, asset.getAssetId())
                    .eq(DigAssetInventory::getAssetCode, digAssetOrder.getAssetCode()));
            if (asset.getIsBlindBox().equals(Constants.DIG_ASSET_IS_BLIND_BOX_NO)) {
                DigDataAsset dataAsset = new DigDataAsset();
                dataAsset.setUserId(digAssetOrder.getBuyerId());
                dataAsset.setAcquireType(Constants.DIG_ACQUIRE_TYPE_SALE_BUY);
                dataAsset.setAcquireRecordId(digAssetOrder.getOrderId());
                dataAsset.setAssetId(asset.getAssetId());
                dataAsset.setAssetName(asset.getAssetName());
                dataAsset.setAssetCode(digAssetOrder.getAssetCode());
                dataAsset.setIsRedeemed(Constants.DIG_ASSET_IS_REDEEMED_NO);
                dataAsset.setStatusCd(Constants.DIG_ASSET_DATA_STATE_ING);
                dataAsset.setCreateDate(LocalDateTime.now());
                dataAsset.setStatusDate(LocalDateTime.now());
                digDataAssetMapper.insert(dataAsset);
                sender.sendMsg(CacheConstants.CHAIN_QUEUE, dataAsset);
            } else if (asset.getIsBlindBox().equals(Constants.DIG_ASSET_IS_BLIND_BOX_YES)) {
                DigUserBlindBox digUserBlindBox = new DigUserBlindBox();
                digUserBlindBox.setUserId(digAssetOrder.getBuyerId());
                DigBlindBox digBlindBox = digBlindBoxMapper.selectOne(Wrappers.<DigBlindBox>lambdaQuery().eq(DigBlindBox::getAssetId, asset.getAssetId()));
                digUserBlindBox.setBoxId(digBlindBox.getBoxId());
                digUserBlindBox.setBoxCode(digAssetOrder.getAssetCode());
                digUserBlindBox.setAcquireType(Constants.DIG_ACQUIRE_TYPE_SALE_BUY);
                digUserBlindBox.setAcquireRecordId(digAssetOrder.getOrderId());
                digUserBlindBox.setIsOpened(Constants.DIG_BLIND_BOX_PRIZE_STATE_ENABLE);
                digUserBlindBox.setStatusCd(Constants.GENERAL_STATE_ENABLE);
                digUserBlindBox.setCreateDate(LocalDateTime.now());
                digUserBlindBoxMapper.insert(digUserBlindBox);
            }
        }
    }

    /**
     * 生成订单号
     */
    @Override
    public String generateOrderNumber(Long userId) {
        String orderNo = Constants.DIG_ORDER_CODE_PREFIX;
        String time = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String traceId = UUID.randomUUID().toString().substring(0, 5).toUpperCase();
        return orderNo + userId + time + traceId;
    }

    @Override
    public DigAssetOrder selectByOrderNo(String orderNo) {
        DigAssetOrder digAssetOrder = baseMapper.selectOne(Wrappers.<DigAssetOrder>lambdaQuery().eq(DigAssetOrder::getOrderNo, orderNo));
        return digAssetOrder;
    }

    @Override
    public String chenkOrderPayStatus(String orderNo, String payChannel) {
        try {
            switch (payChannel) {
                case Constants.DIG_ORDER_CHANNEL_ALIPAYH5:
                    String body = alipayV2Service.queryOrder(orderNo, null);
                    String code = JSONUtil.parseObj(body).getJSONObject("alipay_trade_query_response").get("code", String.class);
//                    if (!"10000".equals(code)) return "查询订单请求异常";
                    String tradeStatus = JSONUtil.parseObj(body).getJSONObject("alipay_trade_query_response").get("trade_status", String.class);
                    if ("10000".equals(code) && "TRADE_SUCCESS".equals(tradeStatus)) {
                        paySuccess(orderNo);
                        return Constants.DIG_ORDER_STATE_PAID;
                    }
                    return Constants.DIG_ORDER_STATE_UNPAID;

                case Constants.DIG_ORDER_CHANNEL_WECHATH5:
                    return "微信支付功能暂未开放";
                case Constants.DIG_ORDER_CHANNEL_UNION_GATEWAY_PAY:
                    DigAssetOrder digAssetOrder = selectByOrderNo(orderNo);
                    if (digAssetOrder.getSharingRule() == null) return Constants.DIG_ORDER_STATE_UNPAID;
                    JSONObject jsonObject = chinaumsPayService.queryGatewayOrder(digAssetOrder.getUnionOrderNo(), digAssetOrder.getSharingRule());
                    String status = jsonObject.getStr("status");
                    if (StringUtils.isNotEmpty(status) && "TRADE_SUCCESS".equals(status)) {
                        paySuccess(orderNo);
                        return Constants.DIG_ORDER_STATE_PAID;
                    }
                    return Constants.DIG_ORDER_STATE_UNPAID;
                default:
                    return "支付渠道错误";
            }

        } catch (Exception e) {
            log.error("订单查询异常:{}", e.getMessage());
            return e.getMessage();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkOverTimeOrder() {
        List<DigAssetOrder> digAssetOrders = baseMapper.selectList(Wrappers.<DigAssetOrder>lambdaQuery()
                .eq(DigAssetOrder::getStatusCd, Constants.DIG_ORDER_STATE_UNPAID));
        for (DigAssetOrder digAssetOrder : digAssetOrders) {
            if (digAssetOrder.getCreateDate().plusMinutes(Constants.DIG_ORDER_OVERTIME).isBefore(LocalDateTime.now())) {
                log.info("订单{}已超时", digAssetOrder.getOrderNo());
                String payStatus = chenkOrderPayStatus(digAssetOrder.getOrderNo(), digAssetOrder.getPayChannel());
                if (Constants.DIG_ORDER_STATE_PAID.equals(payStatus)) continue;
                baseMapper.update(null, Wrappers.<DigAssetOrder>lambdaUpdate()
                        .set(DigAssetOrder::getStatusCd, Constants.DIG_ORDER_STATE_CLOSED)
                        .set(DigAssetOrder::getUpdateDate, LocalDateTime.now())
                        .set(DigAssetOrder::getCloseTime, LocalDateTime.now())
                        .eq(DigAssetOrder::getOrderNo, digAssetOrder.getOrderNo()));
                DigOrderCreateVo digOrderCreateVo = new DigOrderCreateVo();
                digOrderCreateVo.setAssetId(digAssetOrder.getAssetId());
                digOrderCreateVo.setAssetCode(digAssetOrder.getAssetCode());
                digAssetInventoryService.returnInventory(digOrderCreateVo);
            }
        }


    }


    @Async("threadPoolTaskExecutor")
    public void processOrderTask() {
        // 动态生成消费者名称
        String consumerName = "CONSUMER_" + UUID.randomUUID().toString().replace("-", "");

        while (!Thread.currentThread().isInterrupted()) {
            try {
                // 批量获取消息（最多10条）
                List<MapRecord<String, Object, Object>> messages = redisQueueService.consumeMessages(
                        RedisQueueService.ORDER_CREATE_QUEUE,
                        RedisQueueService.ORDER_GROUP,
                        consumerName,
                        5,  // 阻塞5秒
                        10  // 每次最多10条
                );

                if (messages != null && !messages.isEmpty()) {
                    for (MapRecord<String, Object, Object> message : messages) {
                        try {
                            DigOrderCreateVo orderTask = (DigOrderCreateVo) message.getValue();
                            createOrderTask(orderTask);
                            // 成功处理后ACK消息
                            redisQueueService.acknowledge(
                                    RedisQueueService.ORDER_CREATE_QUEUE,
                                    RedisQueueService.ORDER_GROUP,
                                    message.getId()
                            );
                        } catch (BusinessException e) {
                            log.warn("订单业务处理失败: {}", e.getMessage());
                            // 业务异常不重试
                        } catch (Exception e) {
                            log.error("订单处理系统异常: {}", e.getMessage());
                            // 系统异常不ACK，等待重试
                        }
                    }
                } else {
                    // 无消息时短暂休眠
                    Thread.sleep(500);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.info("订单处理线程被中断");
            } catch (Exception e) {
                log.error("订单队列处理异常: {}", e.getMessage());
                try {
                    Thread.sleep(1000); // 异常后休眠1秒
                } catch (InterruptedException ex) {
                    Thread.currentThread().interrupt();
                }
            }
        }
    }

    private void createOrderTask(DigOrderCreateVo digOrderCreateVo) {
        // 生成订单
        createOrder(digOrderCreateVo);
    }
}
