package com.ruoyi.issue.pay.controller;

import com.alipay.api.response.AlipayTradeCloseResponse;
import com.alipay.v3.model.AlipayTradeCloseResponseModel;
import com.alipay.v3.model.AlipayTradeQueryResponseModel;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.issue.pay.domain.DigAssetOrder;
import com.ruoyi.issue.pay.service.AlipayService;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.ruoyi.issue.pay.service.AlipayV2Service;
import com.ruoyi.issue.pay.service.DigAssetOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@Api(value = "支付管理", tags = "支付管理")
@RequestMapping("/h5/pay")
@RequiredArgsConstructor
@Slf4j
public class PayController {

    private final AlipayService alipayService;
    private final AlipayV2Service alipayV2Service;

    private final DigAssetOrderService digAssetOrderService;

    /**
     * 创建支付宝支付
     * @param orderNo 订单ID
     * @return 支付页面HTML
     */
    @PostMapping("/createAlipay")
    @ApiOperation("创建支付宝支付")
    public String createAlipayPayment(String orderNo) {
        DigAssetOrder digAssetOrder = digAssetOrderService.selectByOrderNo(orderNo);
        try {
            return alipayService.createWapPayment(digAssetOrder.getOrderNo(), String.valueOf(digAssetOrder.getOrderAmount()), digAssetOrder.getAssetName());
        } catch (Exception e) {
            return "支付创建失败: " + e.getMessage();
        }
    }

    /**
     * 查询订单状态
     * @param orderNo 订单号
     * @return 订单状态
     */
    @PostMapping("/queryOrder")
    @ApiOperation("查询订单状态")
    public AjaxResult queryOrder(String orderNo) {
        try {
            String response = alipayV2Service.queryOrder(orderNo, null);
            return AjaxResult.success( response);
        } catch (Exception e) {
            log.error("查询订单失败: " + e.getMessage());
            return AjaxResult.error(e.getMessage());
        }
    }

    @PostMapping("/closeOrder")
    @ApiOperation("关闭订单")
    public AjaxResult closeOrder(String orderNo) {
        try {
            AlipayTradeQueryResponseModel query = alipayService.queryOrder(orderNo, null);
            if ("WAIT_BUYER_PAY".equals(query.getTradeStatus())) {
                AlipayTradeCloseResponseModel response = alipayService.closeOrder(orderNo, null, "admin");
                return AjaxResult.success("订单关闭成功",response);
            }else {
                return AjaxResult.error("订单已支付");
            }
        }catch (Exception e){
            log.error("订单关闭失败: " + e.getMessage());
            return AjaxResult.error(e.getMessage());
        }
    }
}
