package com.ruoyi.issue.pay.service;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.scwt.pay.dto.ChinaumsSharingRuleVO;
import com.ruoyi.scwt.pay.entify.OrderSubDetail;
import com.ruoyi.sop.dto.ChinaumsPayProperties;
import com.ruoyi.sop.service.ChinaumsCommonService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChinaumsPayService {
    private final ChinaumsPayProperties chinaumsPayProperties;
    private final ChinaumsCommonService chinaumsCommonService;
    public String gatewayPay(String outTradeNo, BigDecimal totalAmount, String sharingRules,String subject) {
        String url = chinaumsPayProperties.getGatewayBaseUrl()+"/shop/gateway/pay";
        Map<String, Object> requestBody = new HashMap<>();
        ObjectMapper mapper = new ObjectMapper();
        String jsonStr = JSONUtil.toJsonStr(sharingRules);
        ChinaumsSharingRuleVO sharingRule = null;
        try {
            sharingRule = mapper.readValue(
                    jsonStr,
                    ChinaumsSharingRuleVO.class
            );
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        requestBody.put("vaFld", sharingRule);
        requestBody.put("sysSource", "OPT");
        requestBody.put("msgType", "upg.order");
        requestBody.put("vaMchntNo", chinaumsPayProperties.getMid());
        requestBody.put("vaTermNo", chinaumsPayProperties.getTid());
        requestBody.put("mchntOrderId", outTradeNo);
        requestBody.put("chnlType", "PC");
        requestBody.put("totalAmount", totalAmount.multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).intValue());
//        requestBody.put("billDesc", orderSubDetail.getWorksName());
        requestBody.put("notifyUrl", chinaumsPayProperties.getNotifyUrl());
        requestBody.put("transType", "UPG_BUSINESS");
        requestBody.put("bizType", "100001");
        requestBody.put("returnUrl", "https://lysz.sccdex.com/profile");
        String pay = chinaumsCommonService.createRequest(JSONUtil.toJsonStr(requestBody), url);
        JSONObject jsonObject = JSONUtil.parseObj(pay);
        String payUrl = jsonObject.getStr("url");
//        Map<String, String> map = new HashMap<>();
//        map.put("payUrl", payUrl);
//        map.put("orderNo", outTradeNo);
        return payUrl;
    }

    /***
     * 查询网关订单是否支付
     */

    public JSONObject queryGatewayOrder(String orderNo ,String sharingRules) {
        String vaMchntNo=chinaumsPayProperties.getMid();
        String vaTermNo=chinaumsPayProperties.getTid();
//        if (orderSubDetail.getTradeType().equals(OrderConstants.ORDER_TYPE_MARKET)){
//            Union union = baseMapper.selectOne(Wrappers.<Union>lambdaQuery().eq(Union::getCreateStaff, orderSubDetail.getSellerId()));
//            vaMchntNo=union.getMerNo();
//            vaTermNo=union.getTermNo();
//        }
        String url = chinaumsPayProperties.getGatewayBaseUrl() + "/shop/online/query";
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("mchntOrderId", orderNo);
        requestBody.put("vaMchntNo", vaMchntNo);
        requestBody.put("vaTermNo", vaTermNo);
        ObjectMapper mapper = new ObjectMapper();
        String jsonStr = JSONUtil.toJsonStr(sharingRules);
        ChinaumsSharingRuleVO sharingRule = null;
        try {
            sharingRule = mapper.readValue(
                    jsonStr,
                    ChinaumsSharingRuleVO.class
            );
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        requestBody.put("vaFld", sharingRule);
        String result = chinaumsCommonService.createRequest(JSONUtil.toJsonStr(requestBody), url);
        JSONObject jsonObject = JSONUtil.parseObj(result);
        return jsonObject;
    }
}
