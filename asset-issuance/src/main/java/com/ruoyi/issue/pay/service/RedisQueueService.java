package com.ruoyi.issue.pay.service;

import com.ruoyi.issue.pay.domain.DigOrderCreateVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.RedisSystemException;
import org.springframework.data.redis.connection.stream.*;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class RedisQueueService {

    // 队列键名常量
    public static final String ORDER_CREATE_QUEUE = "queue:order:create";
    public static final String BLOCKCHAIN_SUBMIT_QUEUE = "queue:blockchain:submit";

    // 消费者组常量
    public static final String ORDER_GROUP = "order-group";
    public static final String BLOCKCHAIN_GROUP = "blockchain-group";

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 发送消息到指定队列
     *
     * @param queueKey  队列键名
     * @param data      消息数据
     * @param groupName 消费者组名
     */
    public void sendMessage(String queueKey, Object data, String groupName) {
        Map<String, Object> message = Collections.singletonMap("data", data);
        RecordId recordId = redisTemplate.opsForStream().add(queueKey, message);
        if (recordId != null) {
            log.info("消息已发送: queueKey={}, messageId={}", queueKey, recordId);
        } else {
            log.error("消息发送失败: queueKey={}", queueKey);
        }

        // 确保消费者组存在
        createConsumerGroupIfNotExists(queueKey, groupName);
    }

    /**
     * 发送订单创建任务
     *
     * @param orderData 订单数据
     */
    public void sendOrderTask(Object orderData) {
        sendMessage(ORDER_CREATE_QUEUE, orderData, ORDER_GROUP);
    }

    /**
     * 发送上链任务
     *
     * @param chainData 上链数据
     */
    public void sendBlockchainTask(Object chainData) {
        sendMessage(BLOCKCHAIN_SUBMIT_QUEUE, chainData, BLOCKCHAIN_GROUP);
    }

    /**
     * 消费消息
     *
     * @param queueKey     队列键名
     * @param groupName    消费者组名
     * @param consumerName 消费者名称
     * @param timeout      超时时间（秒）
     * @return 消息数据
     */
    public Object consumeMessage(String queueKey, String groupName, String consumerName, long timeout) {
        Consumer consumer = Consumer.from(groupName, consumerName);
        StreamOffset streamOffset = StreamOffset.create(queueKey, ReadOffset.lastConsumed());

        StreamReadOptions options = StreamReadOptions.empty().block(Duration.ofSeconds(timeout));
        List<MapRecord<String, Object, Object>> records = redisTemplate.opsForStream()
                .read(consumer, options, streamOffset);

        if (records != null && !records.isEmpty()) {
            MapRecord<String, Object, Object> record = records.get(0);
            // 将 Map<Object, Object> 转换为 Map<String, Object>
            Map<String, Object> value = new HashMap<>();
            for (Map.Entry<Object, Object> entry : record.getValue().entrySet()) {
                value.put(entry.getKey().toString(), entry.getValue());
            }
            Object data = value.get("data");

            // 确认消息处理成功
            redisTemplate.opsForStream().acknowledge(queueKey, groupName, record.getId());
            return data;
        }
        return null;
    }

    /**
     * 创建消费者组（如果不存在）
     */
    private void createConsumerGroupIfNotExists(String queueKey, String groupName) {
        try {
            redisTemplate.opsForStream().createGroup(queueKey, groupName);
        } catch (Exception e) {
            // 消费者组可能已存在
        }
    }

    public List<MapRecord<String, Object, Object>> consumeMessages(
            String queueKey,
            String groupName,
            String consumerName,
            int timeout,
            int count
    ) {
        try {
            // 1. 确保消费者组存在（幂等操作）
            try {
                redisTemplate.opsForStream().createGroup(queueKey, groupName);
            } catch (RedisSystemException e) {
                // 消费者组已存在时忽略错误
                if (!e.getCause().getMessage().contains("BUSYGROUP")) {
                    throw e;
                }
            }

            // 2. 设置流读取选项
            StreamReadOptions options = StreamReadOptions.empty()
                    .block(Duration.ofSeconds(timeout))
                    .count(count);

            // 3. 创建消费者对象
            Consumer consumer = Consumer.from(groupName, consumerName);

            // 4. 设置流偏移量（从上次未消费的消息开始）
            StreamOffset<String> streamOffset = StreamOffset.create(queueKey, ReadOffset.lastConsumed());

            // 5. 读取消息
            return redisTemplate.opsForStream().read(consumer, options, streamOffset);

        } catch (Exception e) {
            log.error("消费消息失败: queueKey={}, group={}, consumer={}", queueKey, groupName, consumerName, e);
            return Collections.emptyList();
        }
    }

    public void acknowledge(String queueKey, String groupName, RecordId recordId) {
        // 实现消息ACK
        redisTemplate.opsForStream().acknowledge(queueKey, groupName, recordId);
        log.info("消息已ACK: queueKey={}, group={}, recordId={}", queueKey, groupName, recordId);

    }
}
