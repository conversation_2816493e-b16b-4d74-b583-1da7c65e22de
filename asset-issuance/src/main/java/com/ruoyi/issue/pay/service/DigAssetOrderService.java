package com.ruoyi.issue.pay.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.issue.pay.domain.DigAssetOrder;
import com.ruoyi.issue.pay.domain.DigOrderCreateVo;

/**
 * 数字资产订单Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface DigAssetOrderService extends IService<DigAssetOrder>
{
    /**
     * 查询数字资产订单
     * 
     * @param orderId 数字资产订单主键
     * @return 数字资产订单
     */
    public DigAssetOrder selectDigAssetOrderByOrderId(Long orderId);

    /**
     * 查询数字资产订单列表
     * 
     * @param digAssetOrder 数字资产订单
     * @return 数字资产订单集合
     */
    public List<DigAssetOrder> selectDigAssetOrderList(DigAssetOrder digAssetOrder);

    /**
     * 新增数字资产订单
     * 
     * @param digAssetOrder 数字资产订单
     * @return 结果
     */
    public int insertDigAssetOrder(DigAssetOrder digAssetOrder);

    /**
     * 修改数字资产订单
     * 
     * @param digAssetOrder 数字资产订单
     * @return 结果
     */
    public int updateDigAssetOrder(DigAssetOrder digAssetOrder);

    /**
     * 批量删除数字资产订单
     * 
     * @param orderIds 需要删除的数字资产订单主键集合
     * @return 结果
     */
    public int deleteDigAssetOrderByOrderIds(Long[] orderIds);

    /**
     * 删除数字资产订单信息
     * 
     * @param orderId 数字资产订单主键
     * @return 结果
     */
    public int deleteDigAssetOrderByOrderId(Long orderId);

    DigAssetOrder createOrder(DigOrderCreateVo digOrderCreateVo);

    void paySuccess(String orderNo);

    String generateOrderNumber(Long userId);

    DigAssetOrder selectByOrderNo(String orderNo);

    String chenkOrderPayStatus(String orderNo,String payChannel);

    void checkOverTimeOrder();
}
