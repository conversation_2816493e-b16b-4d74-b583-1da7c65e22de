package com.ruoyi.issue.pay.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alipay.v3.ApiException;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.annotation.RepeatSubmit;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.issue.asset.domain.DigAssetInventory;
import com.ruoyi.issue.asset.domain.DigDigitalAsset;
import com.ruoyi.issue.asset.service.DigAssetInventoryService;
import com.ruoyi.issue.asset.service.DigDigitalAssetService;
import com.ruoyi.issue.common.config.RequestRateLimiter;
import com.ruoyi.issue.common.constant.Constants;
import com.ruoyi.issue.common.redis.QueueSender;
import com.ruoyi.issue.issuer.domain.DigAssetIssuer;
import com.ruoyi.issue.issuer.service.DigAssetIssuerService;
import com.ruoyi.issue.pay.domain.DigOrderCreateVo;
import com.ruoyi.issue.pay.service.AlipayService;
import com.ruoyi.issue.pay.service.ChinaumsPayService;
import com.ruoyi.issue.pay.service.RedisQueueService;
import com.ruoyi.issue.pay.vo.DigAssetOrderVo;
import com.ruoyi.issue.rule.service.DigSaleRuleService;
import com.ruoyi.issue.zone.domain.DigAssetZone;
import com.ruoyi.issue.zone.service.DigAssetZoneService;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import com.ruoyi.scwt.identify.entity.Identify;
import com.ruoyi.scwt.identify.entity.WcUser;
import com.ruoyi.scwt.identify.mapper.WcUserMapper;
import com.ruoyi.scwt.identify.service.IdentifyService;
import com.ruoyi.scwt.pay.constant.OrderConstants;
import com.ruoyi.scwt.pay.service.OrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.issue.pay.domain.DigAssetOrder;
import com.ruoyi.issue.pay.service.DigAssetOrderService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 数字资产订单Controller
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@RestController
@Api(value = "数字资产订单管理", tags = "数字资产订单管理")
@RequestMapping("/pay/order")
public class DigAssetOrderController extends BaseController {
    @Autowired
    private DigAssetOrderService digAssetOrderService;

    @Autowired
    private DigAssetInventoryService digAssetInventoryService;

    @Autowired
    private AlipayService alipayService;

    @Autowired
    private ChinaumsPayService chinaumsPayService;

    @Autowired
    private DigSaleRuleService digSaleRuleService;

    @Autowired
    private DigDigitalAssetService digDigitalAssetService;

    @Autowired
    private IdentifyService identifyService;

    @Autowired
    private QueueSender sender;

    @Autowired
    private DigAssetIssuerService digAssetIssuerService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private DigAssetZoneService digAssetZoneService;

    @Autowired
    private WcUserMapper wcUserMapper;

    /**
     * 查询数字资产订单列表
     */
    @PreAuthorize("@ss.hasPermi('pay:order:list')")
    @GetMapping("/list")
    @ApiOperation("查询数字资产订单列表")
    public TableDataInfo list(DigAssetOrder digAssetOrder) {
        startPage();
        List<DigAssetOrder> list = digAssetOrderService.selectDigAssetOrderList(digAssetOrder);
        list.forEach(order -> {
            Identify identify = identifyService.getIdentifyByUserId(order.getBuyerId());
            order.setBuyerPhone(identify.getPhone());
            order.setBuyerName(identify.getIdName());
            List<DigAssetIssuer> digAssetIssuers = digAssetIssuerService.selectIssuerListByAssetId(order.getAssetId());
            order.setIssueName(digAssetIssuers.stream().map(DigAssetIssuer::getIssuerName).collect(Collectors.joining(",")));
        });
        return getDataTable(list);
    }

    /**
     * 导出数字资产订单列表
     */
    @PreAuthorize("@ss.hasPermi('pay:order:export')")
    @Log(title = "数字资产订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出数字资产订单列表")
    public void export(HttpServletResponse response, DigAssetOrder digAssetOrder) {
        List<DigAssetOrder> list = digAssetOrderService.selectDigAssetOrderList(digAssetOrder);
        ExcelUtil<DigAssetOrder> util = new ExcelUtil<DigAssetOrder>(DigAssetOrder.class);
        util.exportExcel(response, list, "数字资产订单数据");
    }

    /**
     * 获取数字资产订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('pay:order:query')")
    @GetMapping(value = "/{orderId}")
    @ApiOperation("获取数字资产订单详细信息")
    public AjaxResult getInfo(@PathVariable("orderId") Long orderId) {
        return success(digAssetOrderService.selectDigAssetOrderByOrderId(orderId));
    }

    /**
     * 新增数字资产订单
     */
    @PreAuthorize("@ss.hasPermi('pay:order:add')")
    @Log(title = "数字资产订单", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增数字资产订单")
    public AjaxResult add(@RequestBody DigAssetOrder digAssetOrder) {
        return toAjax(digAssetOrderService.insertDigAssetOrder(digAssetOrder));
    }

    /**
     * 修改数字资产订单
     */
    @PreAuthorize("@ss.hasPermi('pay:order:edit')")
    @Log(title = "数字资产订单", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改数字资产订单")
    public AjaxResult edit(@RequestBody DigAssetOrder digAssetOrder) {
        return toAjax(digAssetOrderService.updateDigAssetOrder(digAssetOrder));
    }

    /**
     * 删除数字资产订单
     */
    @PreAuthorize("@ss.hasPermi('pay:order:remove')")
    @Log(title = "数字资产订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    @ApiOperation("删除数字资产订单")
    public AjaxResult remove(@PathVariable Long[] orderIds) {
        return toAjax(digAssetOrderService.deleteDigAssetOrderByOrderIds(orderIds));
    }

    @Log(title = "数字资产订单", businessType = BusinessType.INSERT)
    @PostMapping("/addOrder")
    @Transactional(rollbackFor = Exception.class)
    @RequestRateLimiter(QPS = 2000, resMsg = "当前抢购人数较多，请稍后再试")
    @RepeatSubmit(interval = 2000, message = "请勿重复提交")
    @ApiOperation("创建购买订单")
    public AjaxResult addOrder(@RequestBody DigOrderCreateVo digOrderCreateVo) {
        try {
            digOrderCreateVo.setBuyerId(getUserId());
            // 判断是否实名认证
            boolean b = identifyService.isIdentify(String.valueOf(getUserId()));
            if (!b) return AjaxResult.error("请先完成实名认证");
            // 判断是否已经有链上地址
            WcUser wcUser = wcUserMapper.selectOne(Wrappers.<WcUser>lambdaQuery()
                    .eq(WcUser::getUserId, getUserId()));
            if (ObjectUtil.isEmpty(wcUser) || ObjectUtil.isEmpty(wcUser.getAddress()))
                return AjaxResult.error("暂无链上地址");
            // 下单资格判断
            AjaxResult result = digSaleRuleService.isMatch(digOrderCreateVo.getBuyerId(), digOrderCreateVo.getAssetId());
            if (result != null) return AjaxResult.error("无购买资格");
            //获取库存
            DigAssetInventory digAssetInventory = digAssetInventoryService.getInventory(digOrderCreateVo);
            if (digAssetInventory == null) return AjaxResult.error("已售罄");
            // 预生成订单号
            String orderNo = digAssetOrderService.generateOrderNumber(getUserId());
            digOrderCreateVo.setOrderNo(orderNo);
            digOrderCreateVo.setAssetCode(digAssetInventory.getAssetCode());
            // 将订单任务放入队列
            sender.sendMsg(CacheConstants.ORDER_QUEUE, digOrderCreateVo);

            // 返回排队中状态
            Map<String, Object> map = new HashMap<>();
            map.put("orderNo", orderNo);
            map.put("status", "processing");
            return AjaxResult.success("订单正在处理中", map);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    @GetMapping("/orderStatus/{orderNo}")
    @ApiOperation("查询订单状态")
    public AjaxResult getOrderStatus(@PathVariable String orderNo) {
        DigAssetOrder order = digAssetOrderService.selectByOrderNo(orderNo);
        if (order == null) {
            Map<String, Object> result = new HashMap<>();
            result.put("status", "processing");
            return AjaxResult.success("订单正在处理中", result);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("orderNo", orderNo);
        result.put("status", "success");
        return AjaxResult.success("订单正在处理中", result);
    }

    /***
     * 支付接口
     */
    @PostMapping("/pay")
    @ApiOperation("订单支付")
    public AjaxResult pay(String orderNo, String payChannel) {
        try {
            DigAssetOrder digAssetOrder = digAssetOrderService.selectByOrderNo(orderNo);
            if (ObjectUtil.isEmpty(digAssetOrder)) return AjaxResult.error("订单不存在");
            if (Constants.DIG_ORDER_STATE_PAID.equals(digAssetOrder.getStatusCd())) return AjaxResult.error("订单已支付");
            if (Constants.DIG_ORDER_STATE_CLOSED.equals(digAssetOrder.getStatusCd())) return AjaxResult.error("订单已关闭");
            if (Constants.DIG_ORDER_STATE_UNPAID.equals(digAssetOrder.getStatusCd())) {
                // 查询订单是否已经支付
                String orderStatus = null;
                if (StringUtils.isNotEmpty(digAssetOrder.getPayChannel())){
                    orderStatus = digAssetOrderService.chenkOrderPayStatus(orderNo, digAssetOrder.getPayChannel());
                }
                if (Constants.DIG_ORDER_STATE_UNPAID.equals(orderStatus) || orderStatus == null) {
                    digAssetOrder.setPayChannel(payChannel);
                    digAssetOrderService.updateDigAssetOrder(digAssetOrder);
                    switch (payChannel) {
                        case Constants.DIG_ORDER_CHANNEL_ALIPAYH5:
                            return AjaxResult.success(null, alipayService.createWapPayment(orderNo, String.valueOf(digAssetOrder.getOrderAmount()), digAssetOrder.getAssetName()));
                        case Constants.DIG_ORDER_CHANNEL_WECHATH5:
                            return AjaxResult.success("微信支付功能暂未开放");
                        case Constants.DIG_ORDER_CHANNEL_UNION_GATEWAY_PAY:
                            if (StringUtils.isEmpty(digAssetOrder.getSharingRule())){
                                DigAssetZone one = digAssetZoneService.getOne(Wrappers.<DigAssetZone>lambdaQuery().eq(DigAssetZone::getOperatorId, digAssetOrder.getSellerId()));
                                JSONObject jsonObject = orderService.setProfitSharing(OrderConstants.ORDER_TYPE_ISSUE, digAssetOrder.getOrderAmount(), digAssetOrder.getAssetName(), String.valueOf(one.getPayAccountId()));
                                digAssetOrder.setSharingRule(JSONUtil.toJsonStr(jsonObject));
                            }
                            digAssetOrder.setUnionOrderNo(digAssetOrderService.generateOrderNumber(getUserId()));
                            digAssetOrderService.updateDigAssetOrder(digAssetOrder);
                            return AjaxResult.success(null,chinaumsPayService.gatewayPay(digAssetOrder.getUnionOrderNo(), digAssetOrder.getOrderAmount(), digAssetOrder.getSharingRule(), digAssetOrder.getAssetName()));
                        default:
                            return AjaxResult.error("支付渠道错误");
                    }
                } else if (Constants.DIG_ORDER_STATE_PAID.equals(orderStatus)) {
                    return AjaxResult.success("交易完成");
                } else return AjaxResult.error("支付异常");
            } else return AjaxResult.error("订单状态不支持支持");
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
//        return AjaxResult.error("支付异常");
    }

    @PostMapping("/paySuccess")
    @ApiOperation("支付成功回调")
    public AjaxResult paySuccess(String orderNo) {
        digAssetOrderService.paySuccess(orderNo);
        return AjaxResult.success();
    }

    /**
     * -------------------------------------------------------------客户端-------------------------------------------------------------------
     */


    @GetMapping("/client/list")
    @ApiOperation("客户端订单列表")
    public TableDataInfo clientList(DigAssetOrder digAssetOrder) {
        startPage();
        digAssetOrder.setBuyerId(getUserId());
        List<DigAssetOrder> list = digAssetOrderService.selectDigAssetOrderList(digAssetOrder);
        List<DigAssetOrderVo> voList = new ArrayList<>();
        for (DigAssetOrder order : list) {
            DigAssetOrderVo vo = new DigAssetOrderVo();
            BeanUtils.copyProperties(order, vo);
            vo.setAssetCoverThumbnail(digDigitalAssetService.getCoverById(order.getAssetId()));
            voList.add(vo);
        }
        TableDataInfo dataTable = getDataTable(list);
        dataTable.setRows(voList);
        return dataTable;
    }

}
