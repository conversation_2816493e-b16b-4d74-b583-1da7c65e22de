package com.ruoyi.issue.pay.domain;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.issue.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;

/**
 * 数字资产订单对象 dig_asset_order
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@ApiModel(value = "数字资产订单对象")
public class DigAssetOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 订单ID */
    @ApiModelProperty(value = "订单ID")
    private Long orderId;

    /** 订单号(业务唯一) */
    @Excel(name = "订单号(业务唯一)")
    @ApiModelProperty(value = "订单号(业务唯一)")
    private String orderNo;

    /** 银联订单号(业务唯一) */
    @Excel(name = "银联订单号(业务唯一)")
    @ApiModelProperty(value = "银联订单号(业务唯一)")
    private String unionOrderNo;

    /** 买方ID */
    @Excel(name = "买方ID")
    @ApiModelProperty(value = "买方ID")
    private Long buyerId;

    /** 卖方ID */
    @Excel(name = "卖方ID")
    @ApiModelProperty(value = "卖方ID")
    private Long sellerId;

    /** 购买数量 */
    @Excel(name = "购买数量")
    @ApiModelProperty(value = "购买数量")
    private Long buyQuantity;

    /** 订单金额 */
    @Excel(name = "订单金额")
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmount;

    /** 交易类型 */
    @Excel(name = "交易类型")
    @ApiModelProperty(value = "交易类型")
    private String tradeType;

    /** 支付渠道 */
    @Excel(name = "支付渠道")
    @ApiModelProperty(value = "支付渠道")
    private String payChannel;

    /** 资产ID */
    @Excel(name = "资产ID")
    @ApiModelProperty(value = "资产ID")
    private Long assetId;

    /** 资产名称 */
    @Excel(name = "资产名称")
    @ApiModelProperty(value = "资产名称")
    private String assetName;

    /** 资产唯一编号 */
    @Excel(name = "资产唯一编号")
    @ApiModelProperty(value = "资产唯一编号")
    private String assetCode;

    /** 退款订单号 */
    @Excel(name = "退款订单号")
    @ApiModelProperty(value = "退款订单号")
    private String refundOrderNo;

    @ApiModelProperty(value = "分账规则")
    private String sharingRule;

    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "支付时间")
    private LocalDateTime payTime;

    /** 关闭时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "关闭时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "关闭时间")
    private LocalDateTime closeTime;

    /** 状态代码 */
    @Excel(name = "状态代码")
    @ApiModelProperty(value = "状态代码")
    private String statusCd;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateDate;

    @TableField(exist = false)
    @ApiModelProperty(value = "买方手机号")
    private String buyerPhone;

    @TableField(exist = false)
    @ApiModelProperty(value = "买方名称")
    private String buyerName;

    @TableField(exist = false)
    @ApiModelProperty(value = "发行方名称")
    private String issueName;



}
