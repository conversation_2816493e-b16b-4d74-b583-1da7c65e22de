package com.ruoyi.issue.pay.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@ApiModel(value = "数字资产订单VO对象")
public class DigAssetOrderVo {
    /** 订单ID */
    @ApiModelProperty(value = "订单ID")
    private Long orderId;

    /** 订单号(业务唯一) */
    @Excel(name = "订单号(业务唯一)")
    @ApiModelProperty(value = "订单号(业务唯一)")
    private String orderNo;

    /** 订单金额 */
    @Excel(name = "订单金额")
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmount;

    /** 资产ID */
    @Excel(name = "资产ID")
    @ApiModelProperty(value = "资产ID")
    private Long assetId;

    /** 资产名称 */
    @Excel(name = "资产名称")
    @ApiModelProperty(value = "资产名称")
    private String assetName;

    /** 资产封面 */
    @Excel(name = "资产封面")
    @ApiModelProperty(value = "资产封面")
    private String assetCover;

    /** 资产缩略图URL */
    @Excel(name = "资产缩略图URL")
    @ApiModelProperty(value = "资产缩略图URL")
    private String assetCoverThumbnail;

    /** 退款订单号 */
    @Excel(name = "退款订单号")
    @ApiModelProperty(value = "退款订单号")
    private String refundOrderNo;

    /** 状态代码 */
    @Excel(name = "状态代码")
    @ApiModelProperty(value = "状态代码")
    private String statusCd;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;
}
