package com.ruoyi.issue.pay.service;



import com.alipay.v3.ApiClient;
import com.alipay.v3.ApiException;
import com.alipay.v3.ApiResponse;
import com.alipay.v3.Configuration;
import com.alipay.v3.util.GenericExecuteApi;
import com.alipay.v3.util.model.AlipayConfig;
import com.alipay.v3.util.model.CustomizedParams;
import com.alipay.v3.util.model.OpenApiGenericRequest;
import com.alipay.v3.ApiException;
import com.alipay.v3.ApiClient;
import com.alipay.v3.util.model.AlipayConfig;
import com.alipay.v3.Configuration;
import com.alipay.v3.api.AlipayTradeApi;
import com.alipay.v3.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class AlipayService {

    @Value("${alipay.app_id}")
    private String APP_ID;

    @Value("${alipay.openApiDomain}")
    private String GET_WAY_URL;

    @Value("${alipay.private_key}")
    private String PRIVATE_KEY;

    @Value("${alipay.alipay_public_key}")
    private String ALIPAY_PUBLIC_KEY;

    @Value("${alipay.charset}")
    private String CHARSET;

    @Value("${alipay.signType}")
    private String SIGN_TYPE;

    @Value("${alipay.h5_notify_url}")
    private String NOTIFY_URL;

    @Value("${alipay.h5_return_url}")
    private String RETURN_URL;

    @Value("${alipay.notifyUrl}")
    private String notifyUrl;

    /**
     * 手机网站支付下单接口
     *
     * @param outTradeNo  商户订单号（唯一）
     * @param totalAmount 订单总金额（单位：元）
     * @param subject     订单标题
     * @return 支付页面HTML表单（可直接渲染）
     * @throws ApiException
     */
    public String createWapPayment(String outTradeNo, String totalAmount, String subject) throws ApiException {

        ApiClient defaultClient = Configuration.getDefaultApiClient();
        // 初始化alipay参数（全局设置一次）
        defaultClient.setAlipayConfig(getAlipayConfig());

        GenericExecuteApi api = new GenericExecuteApi();

        // 构造请求参数以调用接口
        Map<String, Object> bizParams = new HashMap<>();
        Map<String, Object> bizContent = new HashMap<>();

        // 设置商户订单号
        bizContent.put("out_trade_no", outTradeNo);

        // 设置订单总金额
        bizContent.put("total_amount", totalAmount);

        // 设置订单标题
        bizContent.put("subject", subject);

        // 设置产品码
        bizContent.put("product_code", "QUICK_WAP_WAY");

//        // 设置针对用户授权接口
//        bizContent.put("auth_token", "appopenBb64d181d0146481ab6a762c00714cC27");
//
//        // 设置订单附加信息
//        bizContent.put("body", "Iphone6 16G");
//
//        // 设置用户付款中途退出返回商户网站的地址
//        bizContent.put("quit_url", "http://www.taobao.com/product/113714.html");
//
//        // 设置订单包含的商品列表信息
//        List<Map<String, Object>> goodsDetail = new ArrayList<>();
//        Map<String, Object> goodsDetail0 = new HashMap<>();
//        goodsDetail0.put("out_sku_id", "outSku_01");
//        goodsDetail0.put("goods_name", "ipad");
//        goodsDetail0.put("alipay_goods_id", "20010001");
//        goodsDetail0.put("quantity", 1);
//        goodsDetail0.put("price", "2000");
//        goodsDetail0.put("out_item_id", "outItem_01");
//        goodsDetail0.put("goods_id", "apple-01");
//        goodsDetail0.put("goods_category", "34543238");
//        goodsDetail0.put("categories_tree", "124868003|126232002|126252004");
//        goodsDetail0.put("body", "特价手机");
//        goodsDetail0.put("show_url", "http://www.alipay.com/xxx.jpg");
//        goodsDetail.add(goodsDetail0);
//        bizContent.put("goods_detail", goodsDetail);
//
//        // 设置订单绝对超时时间
//        bizContent.put("time_expire", "2016-12-31 10:05:00");

        // 设置建议使用time_expire字段
//        bizContent.put("timeout_express", "30m");

        // 设置结算信息
//        Map<String, Object> settleInfo = new HashMap<>();
//        settleInfo.put("settle_period_time", "7d");
//        List<Map<String, Object>> settleDetailInfos = new ArrayList<>();
//        Map<String, Object> settleDetailInfos0 = new HashMap<>();
//        settleDetailInfos0.put("amount", "0.1");
//        settleDetailInfos0.put("trans_in", "A0001");
//        settleDetailInfos0.put("settle_entity_type", "SecondMerchant");
//        settleDetailInfos0.put("summary_dimension", "A0001");
//        settleDetailInfos0.put("actual_amount", "0.1");
//        settleDetailInfos0.put("settle_entity_id", "2088xxxxx;ST_0001");
//        settleDetailInfos0.put("trans_in_type", "cardAliasNo");
//        settleDetailInfos.add(settleDetailInfos0);
//        settleInfo.put("settle_detail_infos", settleDetailInfos);
//        bizContent.put("settle_info", settleInfo);

        // 设置业务扩展参数
//        Map<String, Object> extendParams = new HashMap<>();
//        extendParams.put("sys_service_provider_id", "2088511833207846");
//        extendParams.put("hb_fq_seller_percent", "100");
//        extendParams.put("hb_fq_num", "3");
//        extendParams.put("tc_installment_order_id", "2015042321001004720200028594");
//        extendParams.put("industry_reflux_info", "{\"scene_code\":\"metro_tradeorder\",\"channel\":\"xxxx\",\"scene_data\":{\"asset_name\":\"ALIPAY\"}}");
//        extendParams.put("specified_seller_name", "XXX的跨境小铺");
//        extendParams.put("royalty_freeze", "true");
//        extendParams.put("card_type", "S0JP0000");
//        extendParams.put("credit_ext_info", "{\"category\":\"CHARGE_PILE_CAR\",\"serviceId\":\"2020042800000000000001450466\"}");
//        extendParams.put("trade_component_order_id", "2023060801502300000008810000005657");
//        bizContent.put("extend_params", extendParams);
//
//        // 设置商户传入业务信息
//        bizContent.put("business_params", "{\"mc_create_trade_ip\":\"127.0.0.1\"}");
//
//        // 设置公用回传参数
//        bizContent.put("passback_params", "merchantBizType%3d3C%26merchantBizNo%3d2016010101111");
//
//        // 设置指定支付渠道
//        bizContent.put("enable_pay_channels", "pcredit,moneyFund,debitCardExpress");
//
//        // 设置禁用渠道
//        bizContent.put("disable_pay_channels", "pcredit,moneyFund,debitCardExpress");
//
//        // 设置指定单通道
//        bizContent.put("specified_channel", "pcredit");
//
//        // 设置商户的原始订单号
//        bizContent.put("merchant_order_no", "***********");
//
//        // 设置外部指定买家
//        Map<String, Object> extUserInfo = new HashMap<>();
//        extUserInfo.put("cert_type", "IDENTITY_CARD");
//        extUserInfo.put("cert_no", "362334768769238881");
//        extUserInfo.put("name", "李明");
//        extUserInfo.put("mobile", "***********");
//        extUserInfo.put("min_age", "18");
//        extUserInfo.put("need_check_info", "F");
//        extUserInfo.put("identity_hash", "27bfcd1dee4f22c8fe8a2374af9b660419d1361b1c207e9b41a754a113f38fcc");
//        bizContent.put("ext_user_info", extUserInfo);
//
//        // 设置返回参数选项
//        List<String> queryOptions = new ArrayList<>();
//        queryOptions.add("hyb_amount");
//        queryOptions.add("enterprise_pay_info");
//        bizContent.put("query_options", queryOptions);
//
        bizParams.put("biz_content", bizContent);
        bizParams.put("notify_url", notifyUrl);
        bizParams.put("return_url", RETURN_URL);

        try {
            // 如果是第三方代调用模式，请设置app_auth_token（应用授权令牌）
//            String pageRedirectionData = api.pageExecute("alipay.trade.wap.pay", "POST", bizParams, null, "<-- 请填写应用授权令牌 -->", null);
            // 如果需要返回GET请求，请使用
             String pageRedirectionData = api.pageExecute("alipay.trade.wap.pay", "GET", bizParams);
             return pageRedirectionData;
        } catch (ApiException e) {
            log.error("调用支付宝下单失败, url=" + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 订单查询接口
     *
     * @param outTradeNo 商户订单号（优先使用）
     * @param tradeNo    支付宝交易号（可选）
     * @return 订单状态信息
     * @throws AlipayTradeQueryResponseModel
     */
    public AlipayTradeQueryResponseModel queryOrder(String outTradeNo, String tradeNo)throws ApiException  {

        ApiClient defaultClient = Configuration.getDefaultApiClient();
        // 初始化alipay参数（全局设置一次）
        defaultClient.setAlipayConfig(getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayTradeApi api = new AlipayTradeApi();
        AlipayTradeQueryModel data = new AlipayTradeQueryModel();

        // 设置订单支付时传入的商户订单号
        data.setOutTradeNo(outTradeNo);

        // 设置支付宝交易号
//        data.setTradeNo("2014112611001004680 073956707");

        // 设置银行间联模式下有用
//        data.setOrgPid("2088101117952222");

        // 设置查询选项
        List<String> queryOptions = new ArrayList<String>();
        queryOptions.add("trade_settle_info");
        data.setQueryOptions(queryOptions);


        // 第三方代调用模式下请设置app_auth_token
        CustomizedParams params = new CustomizedParams();
//        params.setAppAuthToken("<-- 请填写应用授权令牌 -->");

        try {
            AlipayTradeQueryResponseModel response = api.query(data, params);
            log.info("调用成功:" + response);
            return response;
        } catch (ApiException e) {
            AlipayTradeQueryDefaultResponse errorObject = (AlipayTradeQueryDefaultResponse) e.getErrorObject();
            System.out.println("调用失败:" + errorObject);
        }
        return null;
    }

    /**
     * 订单关闭接口
     *
     * @param outTradeNo 商户订单号（优先使用）
     * @param tradeNo    支付宝交易号（可选）
     * @param operatorId 操作员ID（可选）
     * @return 关闭结果
     * @throws ApiException
     */
    public AlipayTradeCloseResponseModel closeOrder(String outTradeNo, String tradeNo, String operatorId)
            throws ApiException {

        ApiClient defaultClient = Configuration.getDefaultApiClient();
        // 初始化alipay参数（全局设置一次）
        defaultClient.setAlipayConfig(getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayTradeApi api = new AlipayTradeApi();
        AlipayTradeCloseModel data = new AlipayTradeCloseModel();

        // 设置该交易在支付宝系统中的交易流水号
//        data.setTradeNo("2013112611001004680073956707");

        // 设置订单支付时传入的商户订单号
        data.setOutTradeNo(outTradeNo);

        // 设置商家操作员编号 id
        data.setOperatorId("YX01");


        // 第三方代调用模式下请设置app_auth_token
        CustomizedParams params = new CustomizedParams();
//        params.setAppAuthToken("<-- 请填写应用授权令牌 -->");

        try {
            AlipayTradeCloseResponseModel response = api.close(data, params);
            return response;
        } catch (ApiException e) {
            AlipayTradeCloseDefaultResponse errorObject = (AlipayTradeCloseDefaultResponse) e.getErrorObject();
            System.out.println("调用失败:" + errorObject);
        }
        return null;
    }


    private AlipayConfig getAlipayConfig() {
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl("https://openapi.alipay.com");
        alipayConfig.setAppId(APP_ID);
        alipayConfig.setPrivateKey(PRIVATE_KEY);
        alipayConfig.setAlipayPublicKey(ALIPAY_PUBLIC_KEY);
        return alipayConfig;
    }

}
