package com.ruoyi.issue.pay.service;


import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.response.AlipayTradeQueryResponse;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.domain.AlipayTradeQueryModel;

import com.alipay.api.FileItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class AlipayV2Service {

    @Value("${alipay.app_id}")
    private String APP_ID;

    @Value("${alipay.openApiDomain}")
    private String GET_WAY_URL;

    @Value("${alipay.private_key}")
    private String PRIVATE_KEY;

    @Value("${alipay.alipay_public_key}")
    private String ALIPAY_PUBLIC_KEY;

    @Value("${alipay.charset}")
    private String CHARSET;

    @Value("${alipay.signType}")
    private String SIGN_TYPE;

    @Value("${alipay.h5_notify_url}")
    private String NOTIFY_URL;

    @Value("${alipay.h5_return_url}")
    private String RETURN_URL;

    @Value("${alipay.notifyUrl}")
    private String notifyUrl;



    /**
     * 订单查询接口
     *
     * @param outTradeNo 商户订单号（优先使用）
     * @param tradeNo    支付宝交易号（可选）
     * @return 订单状态信息
     * @throws String
     */
    public String queryOrder(String outTradeNo, String tradeNo)throws AlipayApiException   {

        // 初始化SDK
        AlipayClient alipayClient = new DefaultAlipayClient(getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
        AlipayTradeQueryModel model = new AlipayTradeQueryModel();

        // 设置订单支付时传入的商户订单号
        model.setOutTradeNo(outTradeNo);

        // 设置支付宝交易号
//        model.setTradeNo("2014112611001004680 073956707");

        // 设置银行间联模式下有用
//        model.setOrgPid("2088101117952222");

        // 设置查询选项
        List<String> queryOptions = new ArrayList<String>();
        queryOptions.add("trade_settle_info");
        model.setQueryOptions(queryOptions);

        request.setBizModel(model);
        // 第三方代调用模式下请设置app_auth_token
        // request.putOtherTextParam("app_auth_token", "<-- 请填写应用授权令牌 -->");

        AlipayTradeQueryResponse response = alipayClient.execute(request);

        System.out.println(response.getBody());

        if (response.isSuccess()) {
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
            // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
            // String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            // System.out.println(diagnosisUrl);
        }
        return response.getBody();
    }

//    /**
//     * 订单关闭接口
//     *
//     * @param outTradeNo 商户订单号（优先使用）
//     * @param tradeNo    支付宝交易号（可选）
//     * @param operatorId 操作员ID（可选）
//     * @return 关闭结果
//     * @throws ApiException
//     */
//    public AlipayTradeCloseResponseModel closeOrder(String outTradeNo, String tradeNo, String operatorId)
//            throws ApiException {
//
//        ApiClient defaultClient = Configuration.getDefaultApiClient();
//        // 初始化alipay参数（全局设置一次）
//        defaultClient.setAlipayConfig(getAlipayConfig());
//
//        // 构造请求参数以调用接口
//        AlipayTradeApi api = new AlipayTradeApi();
//        AlipayTradeCloseModel data = new AlipayTradeCloseModel();
//
//        // 设置该交易在支付宝系统中的交易流水号
////        data.setTradeNo("2013112611001004680073956707");
//
//        // 设置订单支付时传入的商户订单号
//        data.setOutTradeNo(outTradeNo);
//
//        // 设置商家操作员编号 id
//        data.setOperatorId("YX01");
//
//
//        // 第三方代调用模式下请设置app_auth_token
//        CustomizedParams params = new CustomizedParams();
////        params.setAppAuthToken("<-- 请填写应用授权令牌 -->");
//
//        try {
//            AlipayTradeCloseResponseModel response = api.close(data, params);
//            return response;
//        } catch (ApiException e) {
//            AlipayTradeCloseDefaultResponse errorObject = (AlipayTradeCloseDefaultResponse) e.getErrorObject();
//            System.out.println("调用失败:" + errorObject);
//        }
//        return null;
//    }


    private AlipayConfig getAlipayConfig() {
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl(GET_WAY_URL);
        alipayConfig.setAppId(APP_ID);
        alipayConfig.setPrivateKey(PRIVATE_KEY);
        alipayConfig.setFormat("json");
        alipayConfig.setAlipayPublicKey(ALIPAY_PUBLIC_KEY);
        alipayConfig.setCharset(CHARSET);
        alipayConfig.setSignType(SIGN_TYPE);
        return alipayConfig;
    }

}
