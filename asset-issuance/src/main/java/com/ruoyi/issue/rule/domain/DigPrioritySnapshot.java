package com.ruoyi.issue.rule.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.issue.common.entity.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 优先购资格快照表
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
public class DigPrioritySnapshot extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /** 快照ID */
    private Long snapshotId;

    /** 资产ID */
    private Long assetId;

    /** 规则ID */
    private Long ruleId;

    /** 用户ID */
    private Long userId;


    /** 快照时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime snapshotTime;

}
