package com.ruoyi.issue.rule.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 资产-发行方关联表 实体类
 */
@Data
@TableName("dig_rule_asset_rel")
@EqualsAndHashCode()
public class DigAssetRuleRel extends Model<DigAssetRuleRel> {

    /**
     * 关联ID
     */
    @TableId(type= IdType.AUTO)
    private Long relId;

    /**
     * 资产ID
     */
    @ApiModelProperty(value="资产ID")
    private Long assetId;

    /**
     * 规则ID
     */
    @ApiModelProperty(value="规则ID")
    private Long ruleId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createDate;

}
