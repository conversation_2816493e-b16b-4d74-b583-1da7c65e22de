package com.ruoyi.issue.rule.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.issue.common.constant.Constants;
import com.ruoyi.issue.quartz.domain.NewJob;
import com.ruoyi.issue.quartz.util.CronUtils;
import com.ruoyi.issue.rule.domain.DigAssetRuleRel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.issue.rule.domain.DigSaleRule;
import com.ruoyi.issue.rule.service.DigSaleRuleService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 发售规则Controller
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@RestController
@Api(value = "发售规则管理", tags = "发售规则管理")
@RequestMapping("/rule/rule")
public class DigSaleRuleController extends BaseController
{
    @Autowired
    private DigSaleRuleService digSaleRuleService;

    /**
     * 查询发售规则列表
     */
    @PreAuthorize("@ss.hasPermi('rule:rule:list')")
    @GetMapping("/list")
    @ApiOperation("查询发售规则列表")
    public TableDataInfo list(DigSaleRule digSaleRule)
    {
        startPage();
        List<DigSaleRule> list = digSaleRuleService.selectDigSaleRuleList(digSaleRule);
        return getDataTable(list);
    }

    /**
     * 导出发售规则列表
     */
    @PreAuthorize("@ss.hasPermi('rule:rule:export')")
    @Log(title = "发售规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出发售规则列表")
    public void export(HttpServletResponse response, DigSaleRule digSaleRule)
    {
        List<DigSaleRule> list = digSaleRuleService.selectDigSaleRuleList(digSaleRule);
        ExcelUtil<DigSaleRule> util = new ExcelUtil<DigSaleRule>(DigSaleRule.class);
        util.exportExcel(response, list, "发售规则数据");
    }

    /**
     * 获取发售规则详细信息
     */
    @PreAuthorize("@ss.hasPermi('rule:rule:query')")
    @GetMapping(value = "/{ruleId}")
    @ApiOperation("获取发售规则详细信息")
    public AjaxResult getInfo(@PathVariable("ruleId") Long ruleId)
    {
        DigSaleRule digSaleRule = digSaleRuleService.selectDigSaleRuleByRuleId(ruleId);
        digSaleRule.setAssetId(digSaleRuleService.selectDigAssetIdByRuleId(ruleId));
        return success(digSaleRule);
    }

    /**
     * 新增发售规则
     */
    @PreAuthorize("@ss.hasPermi('rule:rule:add')")
    @Log(title = "发售规则", businessType = BusinessType.INSERT)
    @PostMapping
    @Transactional
    @ApiOperation("新增发售规则")
    public AjaxResult add(@RequestBody DigSaleRule digSaleRule)
    {
        digSaleRuleService.addRule(digSaleRule);
        return success();
    }

    /**
     * 修改发售规则
     */
    @PreAuthorize("@ss.hasPermi('rule:rule:edit')")
    @Log(title = "发售规则", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改发售规则")
    public AjaxResult edit(@RequestBody DigSaleRule digSaleRule)
    {
        return toAjax(digSaleRuleService.updateDigSaleRule(digSaleRule));
    }

    /**
     * 删除发售规则
     */
    @PreAuthorize("@ss.hasPermi('rule:rule:remove')")
    @Log(title = "发售规则", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ruleIds}")
    @ApiOperation("删除发售规则")
    public AjaxResult remove(@PathVariable Long[] ruleIds)
    {
        return toAjax(digSaleRuleService.deleteDigSaleRuleByRuleIds(ruleIds));
    }
}
