package com.ruoyi.issue.rule.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.issue.rule.domain.DigAssetRuleRel;
import com.ruoyi.issue.rule.domain.DigSaleRule;

/**
 * 发售规则Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface DigSaleRuleService extends IService<DigSaleRule>
{
    /**
     * 查询发售规则
     * 
     * @param ruleId 发售规则主键
     * @return 发售规则
     */
    public DigSaleRule selectDigSaleRuleByRuleId(Long ruleId);

    /**
     * 查询发售规则列表
     * 
     * @param digSaleRule 发售规则
     * @return 发售规则集合
     */
    public List<DigSaleRule> selectDigSaleRuleList(DigSaleRule digSaleRule);

    /**
     * 新增发售规则
     * 
     * @param digSaleRule 发售规则
     * @return 结果
     */
    public int insertDigSaleRule(DigSaleRule digSaleRule);

    /**
     * 修改发售规则
     * 
     * @param digSaleRule 发售规则
     * @return 结果
     */
    public int updateDigSaleRule(DigSaleRule digSaleRule);

    /**
     * 批量删除发售规则
     * 
     * @param ruleIds 需要删除的发售规则主键集合
     * @return 结果
     */
    public int deleteDigSaleRuleByRuleIds(Long[] ruleIds);

    /**
     * 删除发售规则信息
     * 
     * @param ruleId 发售规则主键
     * @return 结果
     */
    public int deleteDigSaleRuleByRuleId(Long ruleId);

    List<DigSaleRule> selectDigSaleRuleListByAssetId(Long assetId);

    void insertDigSaleRuleRel(DigAssetRuleRel digAssetRuleRel);

    Long selectDigAssetIdByRuleId(Long ruleId);

    /**
     * 是否符合当前规则
     */
    AjaxResult isMatch(Long userId, Long assetId);

    /**
     * 将规则缓存到redis
     */
    void setRuleToRedis(Long assetId);

    /**
     * 获取规则缓存
     */
    List<DigSaleRule> getRuleFromRedis(Long assetId, String ruleType);


    /**
     * 优先购快照
     */
    void snapshotPriority(Long ruleId);

    void addRule(DigSaleRule digSaleRule);
}
