package com.ruoyi.issue.rule.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.issue.rule.domain.DigPrioritySnapshot;
import com.ruoyi.issue.rule.domain.DigSaleRule;

/**
 * 发售规则Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface DigSaleRuleMapper extends BaseMapper<DigSaleRule>
{
    /**
     * 查询发售规则
     * 
     * @param ruleId 发售规则主键
     * @return 发售规则
     */
    public DigSaleRule selectDigSaleRuleByRuleId(Long ruleId);

    /**
     * 查询发售规则列表
     * 
     * @param digSaleRule 发售规则
     * @return 发售规则集合
     */
    public List<DigSaleRule> selectDigSaleRuleList(DigSaleRule digSaleRule);

    /**
     * 新增发售规则
     * 
     * @param digSaleRule 发售规则
     * @return 结果
     */
    public int insertDigSaleRule(DigSaleRule digSaleRule);

    /**
     * 修改发售规则
     * 
     * @param digSaleRule 发售规则
     * @return 结果
     */
    public int updateDigSaleRule(DigSaleRule digSaleRule);

    /**
     * 删除发售规则
     * 
     * @param ruleId 发售规则主键
     * @return 结果
     */
    public int deleteDigSaleRuleByRuleId(Long ruleId);

    /**
     * 批量删除发售规则
     * 
     * @param ruleIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDigSaleRuleByRuleIds(Long[] ruleIds);

    void insertSnapshotsBatch(List<DigPrioritySnapshot> prioritySnapshots);
}
