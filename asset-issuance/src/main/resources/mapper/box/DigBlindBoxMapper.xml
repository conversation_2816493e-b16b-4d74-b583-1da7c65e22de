<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.issue.box.mapper.DigBlindBoxMapper">
    
    <resultMap type="DigBlindBox" id="DigBlindBoxResult">
        <result property="boxId"    column="BOX_ID"    />
        <result property="assetId"    column="ASSET_ID"    />
        <result property="boxName"    column="BOX_NAME"    />
        <result property="boxCover"    column="BOX_COVER"    />
        <result property="description"    column="DESCRIPTION"    />
        <result property="startTime"    column="START_TIME"    />
        <result property="endTime"    column="END_TIME"    />
        <result property="statusCd"    column="STATUS_CD"    />
        <result property="createStaff"    column="CREATE_STAFF"    />
        <result property="createDate"    column="CREATE_DATE"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDigBlindBoxVo">
        select BOX_ID, ASSET_ID, BOX_NAME, BOX_COVER, DESCRIPTION, START_TIME, END_TIME, STATUS_CD, CREATE_STAFF, CREATE_DATE, remark from dig_blind_box
    </sql>

    <select id="selectDigBlindBoxList" parameterType="DigBlindBox" resultMap="DigBlindBoxResult">
        <include refid="selectDigBlindBoxVo"/>
        <where>  
            <if test="assetId != null "> and ASSET_ID = #{assetId}</if>
            <if test="boxName != null  and boxName != ''"> and BOX_NAME like concat('%', #{boxName}, '%')</if>
            <if test="boxCover != null  and boxCover != ''"> and BOX_COVER = #{boxCover}</if>
            <if test="description != null  and DESCRIPTION != ''"> and DESCRIPTION = #{description}</if>
            <if test="startTime != null "> and START_TIME = #{startTime}</if>
            <if test="endTime != null "> and END_TIME = #{endTime}</if>
            <if test="statusCd != null  and statusCd != ''"> and STATUS_CD = #{statusCd}</if>
            <if test="createStaff != null  and createStaff != ''"> and CREATE_STAFF = #{createStaff}</if>
            <if test="createDate != null "> and CREATE_DATE = #{createDate}</if>
            <if test="remark != null  and remark != ''"> and remark = #{remark}</if>
        </where>
    </select>
    
    <select id="selectDigBlindBoxByBoxId" parameterType="Long" resultMap="DigBlindBoxResult">
        <include refid="selectDigBlindBoxVo"/>
        where BOX_ID = #{boxId}
    </select>

    <insert id="insertDigBlindBox" parameterType="DigBlindBox" useGeneratedKeys="true" keyProperty="boxId">
        insert into dig_blind_box
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetId != null">ASSET_ID,</if>
            <if test="boxName != null and boxName != ''">BOX_NAME,</if>
            <if test="boxCover != null and boxCover != ''">BOX_COVER,</if>
            <if test="description != null">DESCRIPTION,</if>
            <if test="startTime != null">START_TIME,</if>
            <if test="endTime != null">END_TIME,</if>
            <if test="statusCd != null">STATUS_CD,</if>
            <if test="createStaff != null">CREATE_STAFF,</if>
            <if test="createDate != null">CREATE_DATE,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetId != null">#{assetId},</if>
            <if test="boxName != null and boxName != ''">#{boxName},</if>
            <if test="boxCover != null and boxCover != ''">#{boxCover},</if>
            <if test="description != null">#{description},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="statusCd != null">#{statusCd},</if>
            <if test="createStaff != null">#{createStaff},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDigBlindBox" parameterType="DigBlindBox">
        update dig_blind_box
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">ASSET_ID = #{assetId},</if>
            <if test="boxName != null and boxName != ''">BOX_NAME = #{boxName},</if>
            <if test="boxCover != null and boxCover != ''">BOX_COVER = #{boxCover},</if>
            <if test="description != null">DESCRIPTION = #{description},</if>
            <if test="startTime != null">START_TIME = #{startTime},</if>
            <if test="endTime != null">END_TIME = #{endTime},</if>
            <if test="statusCd != null">STATUS_CD = #{statusCd},</if>
            <if test="createStaff != null">CREATE_STAFF = #{createStaff},</if>
            <if test="createDate != null">CREATE_DATE = #{createDate},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where BOX_ID = #{boxId}
    </update>

    <delete id="deleteDigBlindBoxByBoxId" parameterType="Long">
        delete from dig_blind_box where BOX_ID = #{boxId}
    </delete>

    <delete id="deleteDigBlindBoxByBoxIds" parameterType="String">
        delete from dig_blind_box where BOX_ID in 
        <foreach item="boxId" collection="array" open="(" separator="," close=")">
            #{boxId}
        </foreach>
    </delete>
</mapper>