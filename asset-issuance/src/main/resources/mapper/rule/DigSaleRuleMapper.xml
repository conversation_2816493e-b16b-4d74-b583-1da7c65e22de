<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.issue.rule.mapper.DigSaleRuleMapper">
    
    <resultMap type="DigSaleRule" id="DigSaleRuleResult">
        <result property="ruleId"    column="RULE_ID"    />
        <result property="ruleName"    column="RULE_NAME"    />
        <result property="ruleType"    column="RULE_TYPE"    />
        <result property="startTime"    column="START_TIME"    />
        <result property="endTime"    column="END_TIME"    />
        <result property="limitQuantity"    column="LIMIT_QUANTITY"    />
        <result property="couponId"    column="COUPON_ID"    />
        <result property="statusCd"    column="STATUS_CD"    />
        <result property="statusDate"    column="STATUS_DATE"    />
        <result property="createStaff"    column="CREATE_STAFF"    />
        <result property="createDate"    column="CREATE_DATE"    />
        <result property="updateStaff"    column="UPDATE_STAFF"    />
        <result property="updateDate"    column="UPDATE_DATE"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDigSaleRuleVo">
        select RULE_ID, RULE_NAME, RULE_TYPE, START_TIME, END_TIME, LIMIT_QUANTITY, COUPON_ID, STATUS_CD, STATUS_DATE, CREATE_STAFF, CREATE_DATE, UPDATE_STAFF, UPDATE_DATE, remark from dig_sale_rule
    </sql>

    <select id="selectDigSaleRuleList" parameterType="DigSaleRule" resultMap="DigSaleRuleResult">
        <include refid="selectDigSaleRuleVo"/>
        <where>  
            <if test="ruleName != null  and ruleName != ''"> and RULE_NAME like concat('%', #{ruleName}, '%')</if>
            <if test="ruleType != null  and ruleType != ''"> and RULE_TYPE = #{ruleType}</if>
            <if test="startTime != null "> and START_TIME = #{startTime}</if>
            <if test="endTime != null "> and END_TIME = #{endTime}</if>
            <if test="limitQuantity != null "> and LIMIT_QUANTITY = #{limitQuantity}</if>
            <if test="couponId != null "> and COUPON_ID = #{couponId}</if>
            <if test="statusCd != null  and statusCd != ''"> and STATUS_CD = #{statusCd}</if>
            <if test="statusDate != null "> and STATUS_DATE = #{statusDate}</if>
            <if test="createStaff != null  and createStaff != ''"> and CREATE_STAFF = #{createStaff}</if>
            <if test="createDate != null "> and CREATE_DATE = #{createDate}</if>
            <if test="updateStaff != null  and updateStaff != ''"> and UPDATE_STAFF = #{updateStaff}</if>
            <if test="updateDate != null "> and UPDATE_DATE = #{updateDate}</if>
            <if test="remark != null  and remark != ''"> and remark = #{remark}</if>
        </where>
    </select>
    
    <select id="selectDigSaleRuleByRuleId" parameterType="Long" resultMap="DigSaleRuleResult">
        <include refid="selectDigSaleRuleVo"/>
        where RULE_ID = #{ruleId}
    </select>

    <insert id="insertDigSaleRule" parameterType="DigSaleRule" useGeneratedKeys="true" keyProperty="ruleId">
        insert into dig_sale_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleName != null">RULE_NAME,</if>
            <if test="ruleType != null and ruleType != ''">RULE_TYPE,</if>
            <if test="startTime != null">START_TIME,</if>
            <if test="endTime != null">END_TIME,</if>
            <if test="limitQuantity != null">LIMIT_QUANTITY,</if>
            <if test="couponId != null">COUPON_ID,</if>
            <if test="statusCd != null">STATUS_CD,</if>
            <if test="statusDate != null">STATUS_DATE,</if>
            <if test="createStaff != null">CREATE_STAFF,</if>
            <if test="createDate != null">CREATE_DATE,</if>
            <if test="updateStaff != null">UPDATE_STAFF,</if>
            <if test="updateDate != null">UPDATE_DATE,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleName != null">#{ruleName},</if>
            <if test="ruleType != null and ruleType != ''">#{ruleType},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="limitQuantity != null">#{limitQuantity},</if>
            <if test="couponId != null">#{couponId},</if>
            <if test="statusCd != null">#{statusCd},</if>
            <if test="statusDate != null">#{statusDate},</if>
            <if test="createStaff != null">#{createStaff},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateStaff != null">#{updateStaff},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>
    <insert id="insertSnapshotsBatch">
        insert into dig_priority_snapshot (RULE_ID, ASSET_ID, USER_ID, SNAPSHOT_TIME) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.ruleId}, #{item.assetId}, #{item.userId}, #{item.snapshotTime})
        </foreach>
    </insert>

    <update id="updateDigSaleRule" parameterType="DigSaleRule">
        update dig_sale_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleName != null">RULE_NAME = #{ruleName},</if>
            <if test="ruleType != null and ruleType != ''">RULE_TYPE = #{ruleType},</if>
            <if test="startTime != null">START_TIME = #{startTime},</if>
            <if test="endTime != null">END_TIME = #{endTime},</if>
            <if test="limitQuantity != null">LIMIT_QUANTITY = #{limitQuantity},</if>
            <if test="couponId != null">COUPON_ID = #{couponId},</if>
            <if test="statusCd != null">STATUS_CD = #{statusCd},</if>
            <if test="statusDate != null">STATUS_DATE = #{statusDate},</if>
            <if test="createStaff != null">CREATE_STAFF = #{createStaff},</if>
            <if test="createDate != null">CREATE_DATE = #{createDate},</if>
            <if test="updateStaff != null">UPDATE_STAFF = #{updateStaff},</if>
            <if test="updateDate != null">UPDATE_DATE = #{updateDate},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where RULE_ID = #{ruleId}
    </update>

    <delete id="deleteDigSaleRuleByRuleId" parameterType="Long">
        delete from dig_sale_rule where RULE_ID = #{ruleId}
    </delete>

    <delete id="deleteDigSaleRuleByRuleIds" parameterType="String">
        delete from dig_sale_rule where RULE_ID in 
        <foreach item="ruleId" collection="array" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
    </delete>
</mapper>