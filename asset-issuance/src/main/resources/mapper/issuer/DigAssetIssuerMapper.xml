<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.issue.issuer.mapper.DigAssetIssuerMapper">
    
    <resultMap type="DigAssetIssuer" id="DigAssetIssuerResult">
        <result property="issuerId"    column="ISSUER_ID"    />
        <result property="issuerName"    column="ISSUER_NAME"    />
        <result property="issuerLogo"    column="ISSUER_LOGO"    />
        <result property="statusCd"    column="STATUS_CD"    />
        <result property="statusDate"    column="STATUS_DATE"    />
        <result property="createStaff"    column="CREATE_STAFF"    />
        <result property="createDate"    column="CREATE_DATE"    />
        <result property="updateStaff"    column="UPDATE_STAFF"    />
        <result property="updateDate"    column="UPDATE_DATE"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDigAssetIssuerVo">
        select ISSUER_ID, ISSUER_NAME, ISSUER_LOGO, STATUS_CD, STATUS_DATE, CREATE_STAFF, CREATE_DATE, UPDATE_STAFF, UPDATE_DATE, remark from dig_asset_issuer
    </sql>

    <select id="selectDigAssetIssuerList" parameterType="DigAssetIssuer" resultMap="DigAssetIssuerResult">
        <include refid="selectDigAssetIssuerVo"/>
        <where>  
            <if test="issuerName != null  and issuerName != ''"> and ISSUER_NAME like concat('%', #{issuerName}, '%')</if>
            <if test="issuerLogo != null  and issuerLogo != ''"> and ISSUER_LOGO = #{issuerLogo}</if>
            <if test="statusCd != null  and statusCd != ''"> and STATUS_CD = #{statusCd}</if>
            <if test="statusDate != null "> and STATUS_DATE = #{statusDate}</if>
            <if test="createStaff != null  and createStaff != ''"> and CREATE_STAFF = #{createStaff}</if>
            <if test="createDate != null "> and CREATE_DATE = #{createDate}</if>
            <if test="updateStaff != null  and updateStaff != ''"> and UPDATE_STAFF = #{updateStaff}</if>
            <if test="updateDate != null "> and UPDATE_DATE = #{updateDate}</if>
            <if test="remark != null  and remark != ''"> and remark = #{remark}</if>
        </where>
    </select>
    
    <select id="selectDigAssetIssuerByIssuerId" parameterType="Long" resultMap="DigAssetIssuerResult">
        <include refid="selectDigAssetIssuerVo"/>
        where ISSUER_ID = #{issuerId}
    </select>

    <insert id="insertDigAssetIssuer" parameterType="DigAssetIssuer" useGeneratedKeys="true" keyProperty="issuerId">
        insert into dig_asset_issuer
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="issuerName != null and issuerName != ''">ISSUER_NAME,</if>
            <if test="issuerLogo != null and issuerLogo != ''">ISSUER_LOGO,</if>
            <if test="statusCd != null">STATUS_CD,</if>
            <if test="statusDate != null">STATUS_DATE,</if>
            <if test="createStaff != null">CREATE_STAFF,</if>
            <if test="createDate != null">CREATE_DATE,</if>
            <if test="updateStaff != null">UPDATE_STAFF,</if>
            <if test="updateDate != null">UPDATE_DATE,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="issuerName != null and issuerName != ''">#{issuerName},</if>
            <if test="issuerLogo != null and issuerLogo != ''">#{issuerLogo},</if>
            <if test="statusCd != null">#{statusCd},</if>
            <if test="statusDate != null">#{statusDate},</if>
            <if test="createStaff != null">#{createStaff},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateStaff != null">#{updateStaff},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDigAssetIssuer" parameterType="DigAssetIssuer">
        update dig_asset_issuer
        <trim prefix="SET" suffixOverrides=",">
            <if test="issuerName != null and issuerName != ''">ISSUER_NAME = #{issuerName},</if>
            <if test="issuerLogo != null and issuerLogo != ''">ISSUER_LOGO = #{issuerLogo},</if>
            <if test="statusCd != null">STATUS_CD = #{statusCd},</if>
            <if test="statusDate != null">STATUS_DATE = #{statusDate},</if>
            <if test="createStaff != null">CREATE_STAFF = #{createStaff},</if>
            <if test="createDate != null">CREATE_DATE = #{createDate},</if>
            <if test="updateStaff != null">UPDATE_STAFF = #{updateStaff},</if>
            <if test="updateDate != null">UPDATE_DATE = #{updateDate},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where ISSUER_ID = #{issuerId}
    </update>

    <delete id="deleteDigAssetIssuerByIssuerId" parameterType="Long">
        delete from dig_asset_issuer where ISSUER_ID = #{issuerId}
    </delete>

    <delete id="deleteDigAssetIssuerByIssuerIds" parameterType="String">
        delete from dig_asset_issuer where ISSUER_ID in 
        <foreach item="issuerId" collection="array" open="(" separator="," close=")">
            #{issuerId}
        </foreach>
    </delete>
</mapper>