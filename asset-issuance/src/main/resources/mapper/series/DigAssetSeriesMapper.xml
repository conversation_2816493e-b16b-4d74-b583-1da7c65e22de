<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.issue.series.mapper.DigAssetSeriesMapper">
    
    <resultMap type="DigAssetSeries" id="DigAssetSeriesResult">
        <result property="seriesId"    column="SERIES_ID"    />
        <result property="seriesName"    column="SERIES_NAME"    />
        <result property="seriesCover"    column="SERIES_COVER"    />
        <result property="seriesDesc"    column="SERIES_DESC"    />
        <result property="statusCd"    column="STATUS_CD"    />
        <result property="statusDate"    column="STATUS_DATE"    />
        <result property="createStaff"    column="CREATE_STAFF"    />
        <result property="createDate"    column="CREATE_DATE"    />
        <result property="updateStaff"    column="UPDATE_STAFF"    />
        <result property="updateDate"    column="UPDATE_DATE"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDigAssetSeriesVo">
        select SERIES_ID, SERIES_NAME, SERIES_COVER, SERIES_DESC,STATUS_CD, STATUS_DATE, CREATE_STAFF, CREATE_DATE, UPDATE_STAFF, UPDATE_DATE, remark from dig_asset_series
    </sql>

    <select id="selectDigAssetSeriesList" parameterType="DigAssetSeries" resultMap="DigAssetSeriesResult">
        <include refid="selectDigAssetSeriesVo"/>
        <where>  
            <if test="seriesName != null  and seriesName != ''"> and SERIES_NAME like concat('%', #{seriesName}, '%')</if>
            <if test="seriesCover != null  and seriesCover != ''"> and SERIES_COVER = #{seriesCover}</if>
             <if test="seriesDesc != null  and seriesDesc != ''"> and SERIES_DESC = #{seriesDesc}</if>
            <if test="statusCd != null  and statusCd != ''"> and STATUS_CD = #{statusCd}</if>
            <if test="statusDate != null "> and STATUS_DATE = #{statusDate}</if>
            <if test="createStaff != null  and createStaff != ''"> and CREATE_STAFF = #{createStaff}</if>
            <if test="createDate != null "> and CREATE_DATE = #{createDate}</if>
            <if test="updateStaff != null  and updateStaff != ''"> and UPDATE_STAFF = #{updateStaff}</if>
            <if test="updateDate != null "> and UPDATE_DATE = #{updateDate}</if>
            <if test="remark != null  and remark != ''"> and remark = #{remark}</if>
        </where>
    </select>
    
    <select id="selectDigAssetSeriesBySeriesId" parameterType="Long" resultMap="DigAssetSeriesResult">
        <include refid="selectDigAssetSeriesVo"/>
        where SERIES_ID = #{seriesId}
    </select>

    <insert id="insertDigAssetSeries" parameterType="DigAssetSeries" useGeneratedKeys="true" keyProperty="seriesId">
        insert into dig_asset_series
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="seriesName != null and seriesName != ''">SERIES_NAME,</if>
            <if test="seriesCover != null and seriesCover != ''">SERIES_COVER,</if>
            <if test="seriesDesc != null and seriesDesc != ''">SERIES_DESC,</if>
            <if test="statusCd != null">STATUS_CD,</if>
            <if test="statusDate != null">STATUS_DATE,</if>
            <if test="createStaff != null">CREATE_STAFF,</if>
            <if test="createDate != null">CREATE_DATE,</if>
            <if test="updateStaff != null">UPDATE_STAFF,</if>
            <if test="updateDate != null">UPDATE_DATE,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="seriesName != null and seriesName != ''">#{seriesName},</if>
            <if test="seriesCover != null and seriesCover != ''">#{seriesCover},</if>
            <if test="seriesDesc != null and seriesDesc != ''">#{seriesDesc},</if>
            <if test="statusCd != null">#{statusCd},</if>
            <if test="statusDate != null">#{statusDate},</if>
            <if test="createStaff != null">#{createStaff},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateStaff != null">#{updateStaff},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDigAssetSeries" parameterType="DigAssetSeries">
        update dig_asset_series
        <trim prefix="SET" suffixOverrides=",">
            <if test="seriesName != null and seriesName != ''">SERIES_NAME = #{seriesName},</if>
            <if test="seriesCover != null and seriesCover != ''">SERIES_COVER = #{seriesCover},</if>
            <if test="seriesDesc != null and seriesDesc != ''">SERIES_DESC = #{seriesDesc},</if>
            <if test="statusCd != null">STATUS_CD = #{statusCd},</if>
            <if test="statusDate != null">STATUS_DATE = #{statusDate},</if>
            <if test="createStaff != null">CREATE_STAFF = #{createStaff},</if>
            <if test="createDate != null">CREATE_DATE = #{createDate},</if>
            <if test="updateStaff != null">UPDATE_STAFF = #{updateStaff},</if>
            <if test="updateDate != null">UPDATE_DATE = #{updateDate},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where SERIES_ID = #{seriesId}
    </update>

    <delete id="deleteDigAssetSeriesBySeriesId" parameterType="Long">
        delete from dig_asset_series where SERIES_ID = #{seriesId}
    </delete>

    <delete id="deleteDigAssetSeriesBySeriesIds" parameterType="String">
        delete from dig_asset_series where SERIES_ID in 
        <foreach item="seriesId" collection="array" open="(" separator="," close=")">
            #{seriesId}
        </foreach>
    </delete>
</mapper>