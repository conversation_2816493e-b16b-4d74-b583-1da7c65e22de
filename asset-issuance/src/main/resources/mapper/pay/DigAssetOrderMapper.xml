<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.issue.pay.mapper.DigAssetOrderMapper">
    
    <resultMap type="DigAssetOrder" id="DigAssetOrderResult">
        <result property="orderId"    column="ORDER_ID"    />
        <result property="orderNo"    column="ORDER_NO"    />
        <result property="unionOrderNo" column="UNION_ORDER_NO" />
        <result property="buyerId"    column="BUYER_ID"    />
        <result property="sellerId"    column="SELLER_ID"    />
        <result property="buyQuantity"    column="BUY_QUANTITY"    />
        <result property="orderAmount"    column="ORDER_AMOUNT"    />
        <result property="tradeType"    column="TRADE_TYPE"    />
        <result property="payChannel"    column="PAY_CHANNEL"    />
        <result property="assetId"    column="ASSET_ID"    />
        <result property="assetName"    column="ASSET_NAME"    />
        <result property="assetCode"    column="ASSET_CODE"    />
        <result property="refundOrderNo"    column="REFUND_ORDER_NO"    />
        <result property="payTime"    column="PAY_TIME"    />
        <result property="closeTime"    column="CLOSE_TIME"    />
        <result property="statusCd"    column="STATUS_CD"    />
        <result property="createDate"    column="CREATE_DATE"    />
        <result property="updateDate"    column="UPDATE_DATE"    />
        <result property="sharingRule"    column="sharing_rule"    />
    </resultMap>

    <sql id="selectDigAssetOrderVo">
        select ORDER_ID, ORDER_NO, UNION_ORDER_NO, BUYER_ID, SELLER_ID, BUY_QUANTITY,ORDER_AMOUNT, TRADE_TYPE, PAY_CHANNEL, ASSET_ID, ASSET_NAME, ASSET_CODE, REFUND_ORDER_NO, PAY_TIME, CLOSE_TIME, STATUS_CD, CREATE_DATE, UPDATE_DATE, sharing_rule from dig_asset_order
    </sql>

    <select id="selectDigAssetOrderList" parameterType="DigAssetOrder" resultMap="DigAssetOrderResult">
        <include refid="selectDigAssetOrderVo"/>
        <where>  
            <if test="orderNo != null  and orderNo != ''"> and ORDER_NO = #{orderNo}</if>
             <if test="unionOrderNo != null  and unionOrderNo != ''"> and UNION_ORDER_NO = #{unionOrderNo}</if>
            <if test="buyerId != null "> and BUYER_ID = #{buyerId}</if>
            <if test="sellerId != null "> and SELLER_ID = #{sellerId}</if>
             <if test="buyQuantity != null "> and TRADE_TYPE = #{buyQuantity}</if>
            <if test="orderAmount != null "> and ORDER_AMOUNT = #{orderAmount}</if>
            <if test="tradeType != null  and tradeType != ''"> and TRADE_TYPE = #{tradeType}</if>
            <if test="payChannel != null  and payChannel != ''"> and PAY_CHANNEL = #{payChannel}</if>
            <if test="assetId != null "> and ASSET_ID = #{assetId}</if>
            <if test="assetName != null  and assetName != ''"> and ASSET_NAME like concat('%', #{assetName}, '%')</if>
             <if test="assetCode != null  and assetCode != ''"> and assetCode like concat('%', #{assetCode}, '%')</if>
            <if test="refundOrderNo != null  and refundOrderNo != ''"> and REFUND_ORDER_NO = #{refundOrderNo}</if>
            <if test="payTime != null "> and PAY_TIME = #{payTime}</if>
            <if test="closeTime != null "> and CLOSE_TIME = #{closeTime}</if>
            <if test="statusCd != null  and statusCd != ''"> and STATUS_CD = #{statusCd}</if>
            <if test="createDate != null "> and CREATE_DATE = #{createDate}</if>
            <if test="updateDate != null "> and UPDATE_DATE = #{updateDate}</if>
             <if test="sharingRule != null  and sharingRule != ''"> and sharing_rule = #{sharingRule}</if>
        </where>
        order by CREATE_DATE desc
    </select>
    
    <select id="selectDigAssetOrderByOrderId" parameterType="Long" resultMap="DigAssetOrderResult">
        <include refid="selectDigAssetOrderVo"/>
        where ORDER_ID = #{orderId}
    </select>

    <insert id="insertDigAssetOrder" parameterType="DigAssetOrder" useGeneratedKeys="true" keyProperty="orderId">
        insert into dig_asset_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">ORDER_NO,</if>
            <if test="unionOrderNo != null and unionOrderNo != ''">UNION_ORDER_NO,</if>
            <if test="buyerId != null">BUYER_ID,</if>
            <if test="sellerId != null">SELLER_ID,</if>
            <if test="buyQuantity != null">BUY_QUANTITY,</if>
            <if test="orderAmount != null">ORDER_AMOUNT,</if>
            <if test="tradeType != null and tradeType != ''">TRADE_TYPE,</if>
            <if test="payChannel != null and payChannel != ''">PAY_CHANNEL,</if>
            <if test="assetId != null">ASSET_ID,</if>
            <if test="assetName != null and assetName != ''">ASSET_NAME,</if>
            <if test="assetCode != null and assetCode != ''">ASSET_CODE,</if>
            <if test="refundOrderNo != null">REFUND_ORDER_NO,</if>
            <if test="payTime != null">PAY_TIME,</if>
            <if test="closeTime != null">CLOSE_TIME,</if>
            <if test="statusCd != null">STATUS_CD,</if>
            <if test="createDate != null">CREATE_DATE,</if>
            <if test="updateDate != null">UPDATE_DATE,</if>
            <if test="sharingRule != null">sharing_rule,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">#{orderNo},</if>
            <if test="unionOrderNo != null and unionOrderNo != ''">#{unionOrderNo},</if>
            <if test="buyerId != null">#{buyerId},</if>
            <if test="sellerId != null">#{sellerId},</if>
            <if test="buyQuantity != null">#{buyQuantity},</if>
            <if test="orderAmount != null">#{orderAmount},</if>
            <if test="tradeType != null and tradeType != ''">#{tradeType},</if>
            <if test="payChannel != null and payChannel != ''">#{payChannel},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="assetCode != null and assetCode != ''">#{assetCode},</if>
            <if test="refundOrderNo != null">#{refundOrderNo},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="closeTime != null">#{closeTime},</if>
            <if test="statusCd != null">#{statusCd},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="sharingRule != null">#{sharingRule},</if>
         </trim>
    </insert>

    <update id="updateDigAssetOrder" parameterType="DigAssetOrder">
        update dig_asset_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNo != null and orderNo != ''">ORDER_NO = #{orderNo},</if>
            <if test="unionOrderNo != null and unionOrderNo != ''">UNION_ORDER_NO = #{unionOrderNo},</if>
            <if test="buyerId != null">BUYER_ID = #{buyerId},</if>
            <if test="sellerId != null">SELLER_ID = #{sellerId},</if>
            <if test="buyQuantity != null">BUY_QUANTITY = #{buyQuantity},</if>
            <if test="orderAmount != null">ORDER_AMOUNT = #{orderAmount},</if>
            <if test="tradeType != null and tradeType != ''">TRADE_TYPE = #{tradeType},</if>
            <if test="payChannel != null and payChannel != ''">PAY_CHANNEL = #{payChannel},</if>
            <if test="assetId != null">ASSET_ID = #{assetId},</if>
            <if test="assetName != null and assetName != ''">ASSET_NAME = #{assetName},</if>
            <if test="assetCode != null and assetCode != ''">ASSET_CODE = #{assetCode},</if>
            <if test="refundOrderNo != null">REFUND_ORDER_NO = #{refundOrderNo},</if>
            <if test="payTime != null">PAY_TIME = #{payTime},</if>
            <if test="closeTime != null">CLOSE_TIME = #{closeTime},</if>
            <if test="statusCd != null">STATUS_CD = #{statusCd},</if>
            <if test="createDate != null">CREATE_DATE = #{createDate},</if>
            <if test="updateDate != null">UPDATE_DATE = #{updateDate},</if>
            <if test="sharingRule != null">sharing_rule = #{sharingRule},</if>
        </trim>
        where ORDER_ID = #{orderId}
    </update>

    <delete id="deleteDigAssetOrderByOrderId" parameterType="Long">
        delete from dig_asset_order where ORDER_ID = #{orderId}
    </delete>

    <delete id="deleteDigAssetOrderByOrderIds" parameterType="String">
        delete from dig_asset_order where ORDER_ID in 
        <foreach item="orderId" collection="array" open="(" separator="," close=")">
            #{orderId}
        </foreach>
    </delete>
</mapper>