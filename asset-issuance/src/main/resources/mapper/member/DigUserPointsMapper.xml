<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.issue.member.mapper.DigUserPointsMapper">
    
    <resultMap type="DigUserPoints" id="DigUserPointsResult">
        <result property="ID"    column="ID"    />
        <result property="userId"    column="USER_ID"    />
        <result property="points"    column="POINTS"    />
        <result property="status"    column="STATUS"    />
        <result property="createDate"    column="CREATE_DATE"    />
        <result property="updateDate"    column="UPDATE_DATE"    />
    </resultMap>

    <sql id="selectDigUserPointsVo">
        select ID, USER_ID, POINTS, STATUS, CREATE_DATE, UPDATE_DATE from dig_user_points
    </sql>

    <select id="selectDigUserPointsList" parameterType="DigUserPoints" resultMap="DigUserPointsResult">
        <include refid="selectDigUserPointsVo"/>
        <where>  
            <if test="userId != null "> and USER_ID = #{userId}</if>
            <if test="points != null "> and POINTS = #{points}</if>
            <if test="status != null  and STATUS != ''"> and STATUS = #{status}</if>
            <if test="createDate != null "> and CREATE_DATE = #{createDate}</if>
            <if test="updateDate != null "> and UPDATE_DATE = #{updateDate}</if>
        </where>
    </select>
    
    <select id="selectDigUserPointsByID" parameterType="Long" resultMap="DigUserPointsResult">
        <include refid="selectDigUserPointsVo"/>
        where ID = #{ID}
    </select>

    <insert id="insertDigUserPoints" parameterType="DigUserPoints" useGeneratedKeys="true" keyProperty="ID">
        insert into dig_user_points
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">USER_ID,</if>
            <if test="points != null">POINTS,</if>
            <if test="status != null and status != ''">STATUS,</if>
            <if test="createDate != null">CREATE_DATE,</if>
            <if test="updateDate != null">UPDATE_DATE,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="points != null">#{points},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateDate != null">#{updateDate},</if>
         </trim>
    </insert>

    <update id="updateDigUserPoints" parameterType="DigUserPoints">
        update dig_user_points
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">USER_ID = #{userId},</if>
            <if test="points != null">POINTS = #{points},</if>
            <if test="status != null and status != ''">STATUS = #{status},</if>
            <if test="createDate != null">CREATE_DATE = #{createDate},</if>
            <if test="updateDate != null">UPDATE_DATE = #{updateDate},</if>
        </trim>
        where ID = #{ID}
    </update>

    <delete id="deleteDigUserPointsByID" parameterType="Long">
        delete from dig_user_points where ID = #{ID}
    </delete>

    <delete id="deleteDigUserPointsByIDs" parameterType="String">
        delete from dig_user_points where ID in 
        <foreach item="ID" collection="array" open="(" separator="," close=")">
            #{ID}
        </foreach>
    </delete>
</mapper>