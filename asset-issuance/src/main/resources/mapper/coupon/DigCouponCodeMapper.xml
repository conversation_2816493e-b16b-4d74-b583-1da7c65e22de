<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.issue.coupon.mapper.DigCouponCodeMapper">


    <update id="updateCoponCodeById">
        update dig_coupon_code
        <set>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="isUsed != null">
                is_used = #{isUsed},
            </if>
            <if test="useTime != null">
                use_time = #{useTime},
            </if>
        </set>
            where coupon_code_id = #{couponCodeId}
    </update>
</mapper>