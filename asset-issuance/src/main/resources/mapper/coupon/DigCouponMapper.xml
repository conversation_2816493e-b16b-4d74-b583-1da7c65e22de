<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.issue.coupon.mapper.DigCouponMapper">
    
    <resultMap type="DigCoupon" id="DigCouponResult">
        <result property="couponId"    column="COUPON_ID"    />
        <result property="couponType"    column="COUPON_TYPE"    />
        <result property="couponName"    column="COUPON_NAME"    />
        <result property="couponCover"    column="COUPON_COVER"    />
        <result property="quantity"    column="quantity"    />
        <result property="startTime"    column="START_TIME"    />
        <result property="endTime"    column="END_TIME"    />
        <result property="description"    column="description"    />
        <result property="statusCd"    column="STATUS_CD"    />
        <result property="statusDate"    column="STATUS_DATE"    />
        <result property="createStaff"    column="CREATE_STAFF"    />
        <result property="createDate"    column="CREATE_DATE"    />
        <result property="updateStaff"    column="UPDATE_STAFF"    />
        <result property="updateDate"    column="UPDATE_DATE"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDigCouponVo">
        select COUPON_ID, COUPON_TYPE, COUPON_NAME, COUPON_COVER, quantity, START_TIME, END_TIME, description, STATUS_CD, STATUS_DATE, CREATE_STAFF, CREATE_DATE, UPDATE_STAFF, UPDATE_DATE, remark from dig_coupon
    </sql>

    <select id="selectDigCouponList" parameterType="DigCoupon" resultMap="DigCouponResult">
        <include refid="selectDigCouponVo"/>
        <where>  
            <if test="couponType != null  and couponType != ''"> and COUPON_TYPE = #{couponType}</if>
            <if test="couponName != null  and couponName != ''"> and COUPON_NAME like concat('%', #{couponName}, '%')</if>
            <if test="couponCover != null  and couponCover != ''"> and COUPON_COVER = #{couponCover}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="startTime != null "> and START_TIME = #{startTime}</if>
            <if test="endTime != null "> and END_TIME = #{endTime}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="statusCd != null  and statusCd != ''"> and STATUS_CD = #{statusCd}</if>
            <if test="statusDate != null "> and STATUS_DATE = #{statusDate}</if>
            <if test="createStaff != null  and createStaff != ''"> and CREATE_STAFF = #{createStaff}</if>
            <if test="createDate != null "> and CREATE_DATE = #{createDate}</if>
            <if test="updateStaff != null  and updateStaff != ''"> and UPDATE_STAFF = #{updateStaff}</if>
            <if test="updateDate != null "> and UPDATE_DATE = #{updateDate}</if>
            <if test="remark != null  and remark != ''"> and remark = #{remark}</if>
        </where>
    </select>
    
    <select id="selectDigCouponByCouponId" parameterType="Long" resultMap="DigCouponResult">
        <include refid="selectDigCouponVo"/>
        where COUPON_ID = #{couponId}
    </select>

    <insert id="insertDigCoupon" parameterType="DigCoupon" useGeneratedKeys="true" keyProperty="couponId">
        insert into dig_coupon
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="couponType != null and couponType != ''">COUPON_TYPE,</if>
            <if test="couponName != null and couponName != ''">COUPON_NAME,</if>
            <if test="couponCover != null and couponCover != ''">COUPON_COVER,</if>
            <if test="quantity != null">quantity,</if>
            <if test="startTime != null">START_TIME,</if>
            <if test="endTime != null">END_TIME,</if>
            <if test="description != null and description != ''">description,</if>
            <if test="statusCd != null">STATUS_CD,</if>
            <if test="statusDate != null">STATUS_DATE,</if>
            <if test="createStaff != null">CREATE_STAFF,</if>
            <if test="createDate != null">CREATE_DATE,</if>
            <if test="updateStaff != null">UPDATE_STAFF,</if>
            <if test="updateDate != null">UPDATE_DATE,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="couponType != null and couponType != ''">#{couponType},</if>
            <if test="couponName != null and couponName != ''">#{couponName},</if>
            <if test="couponCover != null and couponCover != ''">#{couponCover},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="description != null and description != ''">#{description},</if>
            <if test="statusCd != null">#{statusCd},</if>
            <if test="statusDate != null">#{statusDate},</if>
            <if test="createStaff != null">#{createStaff},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateStaff != null">#{updateStaff},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>
    <insert id="batchInsert">
        insert into dig_coupon_code (COUPON_ID, COUPON_CODE, IS_USED, GENERATE_TIME) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.couponId}, #{item.couponCode}, #{item.isUsed}, #{item.generateTime})
        </foreach>
    </insert>

    <update id="updateDigCoupon" parameterType="DigCoupon">
        update dig_coupon
        <trim prefix="SET" suffixOverrides=",">
            <if test="couponType != null and couponType != ''">COUPON_TYPE = #{couponType},</if>
            <if test="couponName != null and couponName != ''">COUPON_NAME = #{couponName},</if>
            <if test="couponCover != null and couponCover != ''">COUPON_COVER = #{couponCover},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="startTime != null">START_TIME = #{startTime},</if>
            <if test="endTime != null">END_TIME = #{endTime},</if>
            <if test="description != null and description != ''">description = #{description},</if>
            <if test="statusCd != null">STATUS_CD = #{statusCd},</if>
            <if test="statusDate != null">STATUS_DATE = #{statusDate},</if>
            <if test="createStaff != null">CREATE_STAFF = #{createStaff},</if>
            <if test="createDate != null">CREATE_DATE = #{createDate},</if>
            <if test="updateStaff != null">UPDATE_STAFF = #{updateStaff},</if>
            <if test="updateDate != null">UPDATE_DATE = #{updateDate},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where COUPON_ID = #{couponId}
    </update>

    <delete id="deleteDigCouponByCouponId" parameterType="Long">
        delete from dig_coupon where COUPON_ID = #{couponId}
    </delete>

    <delete id="deleteDigCouponByCouponIds" parameterType="String">
        delete from dig_coupon where COUPON_ID in 
        <foreach item="couponId" collection="array" open="(" separator="," close=")">
            #{couponId}
        </foreach>
    </delete>
</mapper>