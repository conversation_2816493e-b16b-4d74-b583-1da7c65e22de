<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.issue.assetRec.mapper.DigAssetHotRecommendMapper">
    
    <resultMap type="DigAssetHotRecommend" id="DigAssetHotRecommendResult">
        <result property="recId"    column="REC_ID"    />
        <result property="assetId"    column="ASSET_ID"    />
        <result property="assetName"    column="ASSET_NAME"    />
        <result property="recType"    column="REC_TYPE"    />
        <result property="recPosition"    column="REC_POSITION"    />
        <result property="startTime"    column="START_TIME"    />
        <result property="endTime"    column="END_TIME"    />
        <result property="clickCount"    column="CLICK_COUNT"    />
        <result property="statusCd"    column="STATUS_CD"    />
        <result property="createStaff"    column="CREATE_STAFF"    />
        <result property="createDate"    column="CREATE_DATE"    />
        <result property="updateDate"    column="UPDATE_DATE"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDigAssetHotRecommendVo">
        select REC_ID, ASSET_ID, ASSET_NAME, REC_TYPE, REC_POSITION, START_TIME, END_TIME, CLICK_COUNT, STATUS_CD, CREATE_STAFF, CREATE_DATE, UPDATE_DATE, remark from dig_asset_hot_recommend
    </sql>

    <select id="selectDigAssetHotRecommendList" parameterType="DigAssetHotRecommend" resultMap="DigAssetHotRecommendResult">
        <include refid="selectDigAssetHotRecommendVo"/>
        <where>  
            <if test="assetId != null "> and ASSET_ID = #{assetId}</if>
             <if test="assetName != null  and assetName != ''"> and ASSET_NAME = #{assetName}</if>
            <if test="recType != null  and recType != ''"> and REC_TYPE = #{recType}</if>
            <if test="recPosition != null "> and REC_POSITION = #{recPosition}</if>
            <if test="startTime != null "> and START_TIME = #{startTime}</if>
            <if test="endTime != null "> and END_TIME = #{endTime}</if>
            <if test="clickCount != null "> and CLICK_COUNT = #{clickCount}</if>
            <if test="statusCd != null  and statusCd != ''"> and STATUS_CD = #{statusCd}</if>
            <if test="createStaff != null  and createStaff != ''"> and CREATE_STAFF = #{createStaff}</if>
            <if test="createDate != null "> and CREATE_DATE = #{createDate}</if>
            <if test="updateDate != null "> and UPDATE_DATE = #{updateDate}</if>
            <if test="remark != null  and remark != ''"> and remark = #{remark}</if>
        </where>
    </select>
    
    <select id="selectDigAssetHotRecommendByRecId" parameterType="Long" resultMap="DigAssetHotRecommendResult">
        <include refid="selectDigAssetHotRecommendVo"/>
        where REC_ID = #{recId}
    </select>

    <insert id="insertDigAssetHotRecommend" parameterType="DigAssetHotRecommend" useGeneratedKeys="true" keyProperty="recId">
        insert into dig_asset_hot_recommend
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetId != null">ASSET_ID,</if>
            <if test="assetName != null">ASSET_NAME,</if>
            <if test="recType != null and recType != ''">REC_TYPE,</if>
            <if test="recPosition != null">REC_POSITION,</if>
            <if test="startTime != null">START_TIME,</if>
            <if test="endTime != null">END_TIME,</if>
            <if test="clickCount != null">CLICK_COUNT,</if>
            <if test="statusCd != null">STATUS_CD,</if>
            <if test="createStaff != null">CREATE_STAFF,</if>
            <if test="createDate != null">CREATE_DATE,</if>
            <if test="updateDate != null">UPDATE_DATE,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetId != null">#{assetId},</if>
            <if test="assetName != null">#{assetName},</if>
            <if test="recType != null and recType != ''">#{recType},</if>
            <if test="recPosition != null">#{recPosition},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="clickCount != null">#{clickCount},</if>
            <if test="statusCd != null">#{statusCd},</if>
            <if test="createStaff != null">#{createStaff},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDigAssetHotRecommend" parameterType="DigAssetHotRecommend">
        update dig_asset_hot_recommend
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetId != null">ASSET_ID = #{assetId},</if>
            <if test="assetName != null">ASSET_NAME = #{assetName},</if>
            <if test="recType != null and recType != ''">REC_TYPE = #{recType},</if>
            <if test="recPosition != null">REC_POSITION = #{recPosition},</if>
            <if test="startTime != null">START_TIME = #{startTime},</if>
            <if test="endTime != null">END_TIME = #{endTime},</if>
            <if test="clickCount != null">CLICK_COUNT = #{clickCount},</if>
            <if test="statusCd != null">STATUS_CD = #{statusCd},</if>
            <if test="createStaff != null">CREATE_STAFF = #{createStaff},</if>
            <if test="createDate != null">CREATE_DATE = #{createDate},</if>
            <if test="updateDate != null">UPDATE_DATE = #{updateDate},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where REC_ID = #{recId}
    </update>

    <delete id="deleteDigAssetHotRecommendByRecId" parameterType="Long">
        delete from dig_asset_hot_recommend where REC_ID = #{recId}
    </delete>

    <delete id="deleteDigAssetHotRecommendByRecIds" parameterType="String">
        delete from dig_asset_hot_recommend where REC_ID in 
        <foreach item="recId" collection="array" open="(" separator="," close=")">
            #{recId}
        </foreach>
    </delete>
</mapper>