sop:
  # 访问路径
  access-url: https://openapi.bmark.cn/
  # 平台应用id
  appKey: 202411151307005946370719744
  # 应用私钥
  privateKey: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCfgr1KGBVYpsLBzNqG5Lh4dzsSwS+XdCVT3/8kXofyGMPRy5ytRG9KRxuZ33JmgnKgdUlj2bEKOLVrweYWV0yeC7Cond3hBb2GkOuRWCiWMQhYljqbm605l4napQ4PW1Vt8HHnK8v67SNUhLm00OdAnuznnsPHjK47KC+XPYvSLRtTFqghnaT3TuormvRdz4OEPEOsiGyXPYEMEtw0M9sawdw3BC5AxkSUsNj98v3ONwMljrBlxkpf2fzNvhO4yyzh75T/BAzQKj7l0ZGXEfW1dsL8H+YPBnZlcvlOb4BRcYqhjigmS1+rkkGZ/hF0GpDqvaVGGXoGg28X0RTBJMiTAgMBAAECggEBAIuTGRM2k6co9I4kNKuIg6pVmklE5eH+7ZLBFD68wPQkm2R+aDBtC3zRT+OK1E3Co46oUnmeyP2KtaT+29h8tzwsOn6w9K+sGDUhph9eJtWVPhpWCh6TUDZu1V+OwgjKAKzckq6Xcju1OQ8BvIXtrgyrg90A3YtFHEgDV/FdF3XmO2a/z8vWCsOQKgm7hRW6bkTK6wMUygdwOli/PyfkbK8MpkudHQbnNoD8s1wO9MqabghNvKKoMTFiS4VaLjQI0JHqZCYqwImQcWGifjdfzdGE7QJpOUx62EIraBKbrCOLyk7GoKoHkcsWZt1clWswEyuRtpGO72A38fj+HmTvaJECgYEA/hWkqn+wrLyfOQZfepQqt2HY4jamWv/RDd5IOoFZQYm0MtOUOJJzvUnCLbs4P5hZ3+F+8kWqUC8NLUTankTzoygsh+cZqfJaxFgnqbSlSva9zU0WSMSVZXu8gPdm0itSEkTpAMJ8N62RKaDsojlkHYX7Jz2XyL543aSCnWMFx48CgYEAoLaUGCIXcE6jBkkpX99OTOj7rgnFjAF/x9LHVmkU4YfegWpZt27b9r5HsO09blum1U9ZDGXRRCwNcDWSQh0yk6aNjX9GJdBHn0HRlWorboktKJ0OFHCS2OkNt0i9q63lMjEyL+nW1dchN72gT1j5C4hhKixfp1/0RgCfYsY+TL0CgYBFmWghzBh5OLHkzPoaebu5v/PJA29OPgTl552haQ0qBq51vb88P+G0WB/4pvAhe3oi+YHsm2irzjetzs6TzA9xAfAwyf0dsGVsozk6I2GjxzL2qccQ58iEpABe02Y26+bvWjD5JE0ic0jwPkSTq2ThMXI6rYSGWKaDEC03L4jFwQKBgEB3nMZlfoN1d93AoVEs8WrWlobEsrKrs0dD3ORjuJWTfATr1VtIpAHBaF6ne9n19+FfN/hKK3yuMyHMy4Or1Kr3x5Z39Hqcpu5SZ7EI6q0nZ67/+TLnyXArZG4eRr9EPUk6BMjFg4E6D8AdV94ccaJXrapOLHymeAVQoJ7tqPX9AoGAVWY7iClU29JxCEq6SopDafPpq4ZHCEY+IDg0xKPUZg6ljqd+wuMyobYrtM/XS3Av80AInxy6TD+RpBCLwn62moHGclnIku4PCfjMMtPS69JC9TvxcmTazUMHnntknsGDErCN1u8x4X4voisAo1qGrIcCaBmd4RKgQTsOlz9/qkc=
  # 应用公钥
  publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAn4K9ShgVWKbCwczahuS4eHc7EsEvl3QlU9//JF6H8hjD0cucrURvSkcbmd9yZoJyoHVJY9mxCji1a8HmFldMnguwqJ3d4QW9hpDrkVgoljEIWJY6m5utOZeJ2qUOD1tVbfBx5yvL+u0jVIS5tNDnQJ7s557Dx4yuOygvlz2L0i0bUxaoIZ2k907qK5r0Xc+DhDxDrIhslz2BDBLcNDPbGsHcNwQuQMZElLDY/fL9zjcDJY6wZcZKX9n8zb4TuMss4e+U/wQM0Co+5dGRlxH1tXbC/B/mDwZ2ZXL5Tm+AUXGKoY4oJktfq5JBmf4RdBqQ6r2lRhl6BoNvF9EUwSTIkwIDAQAB
  # 平台公钥
  platformPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmjfv3aqGy8wV4y8LjNNOOT3rrSbeuzjo82vlwzyJdlN3kUhPLsulzHsnwi1tFpe8yrDoypx5xB09A6oIxcLKINIXeFtMloFXPlZ/jhSN9F4dF8xE6bEYUQqEwmMHcbo5zD051i62geQiKEi8Ujbo6nr6OsDRqSDR4KYivAlbpsp789j7LlKEHlVZ5aRi3g0WyqtXd3Bjg55TjrHJCU7z6K01lu/ChHXgfMvuHwNBOq/+wFkp2D5rme4G0mtoQFf1+aqDL4XSyvgjUdWmWDloY0YGcDRJXk1b4FsTv2W/elo8o5HfAJ+WietyMJ09sT/PwmTvSjkU5a7TgAmfgqm93wIDAQAB
  # 银联code
  unionCode: 31Q77UA
  #----------------登记存证----------------
  # 商户登记存证注册
  registerForeign: register.foreign
  # 文件上传
  fileEvidenceFileUpload: fileEvidence.fileUpload
  # 新增著作权人
  fileEvidenceAddOwner: fileEvidence.addOwner
  #查询著作权人列表
  ownersGetOwnersList: owners.getOwnersList
  #查询著作权人详情
  ownersGetOwnersById: owners.getOwnersById
  #通过id删除著作权人
  ownersDelete: owners.delete
  #字典查询
  workDict: work.dict
  #登记-新增登记作品信息
  workSave: work.save
  #登记-更新登记作品信息
  workUpdate: work.update
  #登记-查询登记作品列表
  workPage: work.page
  #登记-根据受理号查询登记作品
  workGetWorkByAcceptNo: work.getWorkByAcceptNo
  #登记-根据受理号查询审核进度
  workProgress: work.progress
  #登记-下载作品存证证书
  workEvidence: work.evidence
  #登记-下载作品登记证书
  workRegistration: work.registration
  #登记-获取作品登记数据
  workRegistrationData: work.registrationData
  #存证-新增存证
  fileEvidenceBaseSave: fileEvidence.baseSave
  #存证-查询存证作品列表
  fileEvidencePage: fileEvidence.page
  #存证-查询存证作品详情
  fileEvidenceInfo: fileEvidence.info
  #存证-下载作品存证证书
  fileEvidenceEvidence: fileEvidence.evidence
  #核验-统一证据编号核验
  verificationWorkHash: verification.workHash

  #----------------数字出版物上链----------------
  #用户注册，获取私钥地址和公钥地址
  blockchainRegister: blockchain.register
  #文创链数据
  blockchainBlockChinaCount: blockchain.blockChinaCount
  #资产发行
  issueNft: blockchain.issueNft


  #----------------银联----------------
  #图片资料上传
  chinaumsPicUpload: chinaums.merchant.pic.upload
  #商户入网1.1-银行四要素实名检查
  picAgreementUpload: chinaums.merchant.pic.agreement.upload
  #个人商户入网v2.0
  chinaumsAutoRegPerson: chinaums.merchant.autoReg.person
  #企业商户入网V2.0
  chinaumsAutoRegEnterprise: chinaums.merchant.autoReg.enterprise
  #商户入网2.0-采集入网用户档案资料信息上传
  complexUpload: chinaums.merchant.complex.upload
  #商户入网3.0-发起对公账户验证交易接口(对公账户打款接口,非对公账户无需调用)
  requestAccountVerify: chinaums.merchant.request.account.verify
  #对公账户认证
  chinaumsAccountVerify: chinaums.merchant.company.account.verify
  #组装前台签约地址
  chinaumsAgreementSignUrl: chinaums.merchant.complex.agreement.sign
  #订单支付V1
  chinaumsOrderPay: chinaums.order.pay
  #订单查询
  chinaumsOrderQueryOrder: chinaums.order.queryOrder
  #退款
  chinaumsOrderRefund: chinaums.order.refund
  #关闭订单
  chinaumsOrderClose: chinaums.order.close
  #入网状态查询接口
  chinaumsApplyQry: chinaums.merchant.complex.apply.qry