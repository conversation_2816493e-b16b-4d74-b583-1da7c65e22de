package com.ruoyi.scwt.exception;


import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2019/3/24 21:58
 */
@Getter
public class BusinessException extends RuntimeException {

    private BusinessCodeEnum type;

    public BusinessException(String message){
        super(message);
    }

    public BusinessException(String message, Throwable e){
        super(message,e);
    }

    public BusinessException(String message, BusinessCodeEnum type){
        super(message);
        this.type = type;
    }

    public BusinessException(String message, Throwable e, BusinessCodeEnum type){
        super(message,e);
        this.type = type;
    }

    public static BusinessException resourceCreateException() {
        return new BusinessException("资源新增失败", BusinessCodeEnum.RESOURCE_CREATE_EXCEPTION);
    }

    public static BusinessException resourceNotFoundException() {
        return new BusinessException("未找到该资源，请确认请求参数是否正确", BusinessCodeEnum.RESOURCE_NOT_FOUND_EXCEPTION);
    }

    public static BusinessException resourceDeleteException() {
        return new BusinessException("资源删除失败", BusinessCodeEnum.RESOURCE_DELETE_EXCEPTION);
    }

    public static BusinessException resourceUpdateException() {
        return new BusinessException("资源更新失败", BusinessCodeEnum.RESOURCE_UPDATE_EXCEPTION);
    }
}
