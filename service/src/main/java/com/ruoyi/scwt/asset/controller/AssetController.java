package com.ruoyi.scwt.asset.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.scwt.asset.constant.AssetConstants;
import com.ruoyi.scwt.asset.dto.AssetDto;
import com.ruoyi.scwt.asset.dto.AssetStatusDto;
import com.ruoyi.scwt.asset.entity.*;
import com.ruoyi.scwt.asset.service.*;
import com.ruoyi.scwt.asset.vo.AssetVo;
import com.ruoyi.scwt.common.service.SysLabelService;
import com.ruoyi.scwt.file.entity.AttachmentInfo;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import com.ruoyi.scwt.file.util.OssUtil;
import com.ruoyi.scwt.identify.entity.Identify;
import com.ruoyi.scwt.identify.service.IdentifyService;
import com.ruoyi.scwt.pay.constant.OrderConstants;
import com.ruoyi.scwt.pay.entify.OrderSubDetail;
import com.ruoyi.scwt.pay.service.OrderSubDetailService;
import com.ruoyi.scwt.pay.vo.OrderVo;
import com.ruoyi.scwt.shop.constant.ShopConstants;
import com.ruoyi.scwt.shop.dto.ShopInfoDto;
import com.ruoyi.scwt.shop.entity.ShopHome;
import com.ruoyi.scwt.shop.entity.ShopInfo;
import com.ruoyi.scwt.shop.entity.ShopLabelRel;
import com.ruoyi.scwt.shop.service.ShopInfoService;
import com.ruoyi.scwt.shop.service.ShopLabelRelService;
import com.ruoyi.scwt.shop.vo.ShopInfoVo;
import com.ruoyi.scwt.zone.dto.ZoneStatusDto;
import com.ruoyi.scwt.zone.entity.Zone;
import com.ruoyi.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 实名认证
 */
@RestController
@AllArgsConstructor
@RequestMapping("/asset")
@Api(value = "asset", tags = "资产")
@Slf4j
public class AssetController extends BaseController {


    private final AssetService assetService;

    private final OrderSubDetailService orderSubDetailService;

    private final AssetZoneShopService assetZoneShopService;

    private final ShopInfoService shopInfoService;

    private final AssetHotService assetHotService;

    private final AssetHomeService assetHomeService;

    private final AttachmentInfoService attachmentInfoService;

    private final AssetAuditRecordsService assetAuditRecordsService;

    private final IdentifyService identifyService;

    private final OssUtil ossUtil;


    /**
     * 分页查询
     *
     * @param dto 分页对象
     * @return
     */
    @ApiOperation(value = "分页查询-不需要登录", notes = "分页查询-不需要登录")
    @GetMapping("/home")
    @Anonymous
    public R getShopHome(AssetDto dto) {
        List<Asset> list =new ArrayList<>();
        if (dto.getIsHome()){
            List<AssetHome> assetHomes = assetHomeService.list(Wrappers.<AssetHome>lambdaQuery().orderByDesc(AssetHome::getCreateDate));
            List<Long> assetIds = assetHomes.stream().map(AssetHome::getAssetId).collect(Collectors.toList());
            for (Long assetId : assetIds) {
                Asset one = assetService.getOne(Wrappers.<Asset>query().lambda().eq(Asset::getAssetId, assetId).eq(Asset::getStatusCd, AssetConstants.ASSET_ON_SHELF));
                if (ObjectUtil.isNotNull(one) && one.getShopStatus().equals(ShopConstants.SHOP_TAKE_EFFECT)){
                    list.add(one);
                }
            }
        } else if (dto.getIsHot()) {
            List<AssetHot> assetHots = assetHotService.list(Wrappers.<AssetHot>lambdaQuery().orderByDesc(AssetHot::getCreateDate));
            List<Long> assetIds = assetHots.stream().map(AssetHot::getAssetId).collect(Collectors.toList());
            for (Long assetId : assetIds) {
                Asset one = assetService.getOne(Wrappers.<Asset>query().lambda().eq(Asset::getAssetId, assetId).eq(Asset::getStatusCd, AssetConstants.ASSET_ON_SHELF));
                if (ObjectUtil.isNotNull(one) && one.getShopStatus().equals(ShopConstants.SHOP_TAKE_EFFECT)){
                    list.add(one);
                }
                list.add(one);
            }
        } else {
//            List<Long> assetIds = null;
//            if (StringUtils.isNotEmpty(dto.getLabelIds())) {
//                List<AssetLabelRel> assetLabelRels = assetLabelRelService.list(Wrappers.<AssetLabelRel>query().lambda().in(AssetLabelRel::getLabelId, StrUtil.split(dto.getLabelIds(), ",")));
//                assetIds = assetLabelRels.stream().map(AssetLabelRel::getAssetId).collect(Collectors.toList());
//            }
            LambdaQueryWrapper<Asset> lambda = Wrappers.<Asset>query().lambda()
                    .like(StringUtils.isNotEmpty(dto.getAssetName()), Asset::getAssetName, dto.getAssetName())
                    .in(StringUtils.isNotEmpty(dto.getAssetType()), Asset::getAssetType, dto.getAssetType().split(","))
                    .in(StringUtils.isNotEmpty(dto.getBusinessCategory()), Asset::getBusinessCategory, dto.getBusinessCategory().split(","))
                    .in(StringUtils.isNotEmpty(dto.getTransactionType()), Asset::getTransactionType, dto.getTransactionType().split(","))
                    .eq(Asset::getStatusCd, AssetConstants.ASSET_ON_SHELF)
                    .eq(Asset::getShopStatus, ShopConstants.SHOP_TAKE_EFFECT)
                    .like(StringUtils.isNotEmpty(dto.getLabelName()), Asset::getLabelName, dto.getLabelName())
                    .orderByDesc(Asset::getCreateDate);
            startPage();
            list = assetService.list(lambda);
        }
        List<AssetVo> voList = list.stream().map(item -> {
            AssetVo assetVo = new AssetVo();
            BeanUtils.copyProperties(item, assetVo);
//            assetVo.setLabels(sysLabelService.getLabelListByAssetId(assetVo.getAssetId()));
            assetVo.setAssetCover(ossUtil.getFileUrl(assetVo.getAssetCover()));
            ShopInfoVo shopInfoVo= shopInfoService.getShopByAssetId(assetVo.getAssetId());
            assetVo.setShopInfoVo(shopInfoVo);
            return assetVo;
        }).collect(Collectors.toList());
        TableDataInfo dataTable = getDataTable(list);
        dataTable.setRows(voList);
        return R.ok(dataTable);
    }
    /**
     * 分页查询
     *
     * @param dto 分页对象
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public R getShopPage(AssetDto dto) {
//        List<Long> assetIds = null;
//        if (StringUtils.isNotEmpty(dto.getLabelIds())) {
//            List<AssetLabelRel> assetLabelRels = assetLabelRelService.list(Wrappers.<AssetLabelRel>query().lambda().in(AssetLabelRel::getLabelId, StrUtil.split(dto.getLabelIds(), ",")));
//            assetIds = assetLabelRels.stream().map(AssetLabelRel::getAssetId).collect(Collectors.toList());
//        }
        LambdaQueryWrapper<Asset> lambda = Wrappers.<Asset>query().lambda()
                .like(StringUtils.isNotEmpty(dto.getAssetName()), Asset::getAssetName, dto.getAssetName())
                .eq(StringUtils.isNotEmpty(dto.getAssetType()), Asset::getAssetType, dto.getAssetType())
                .eq(StringUtils.isNotEmpty(dto.getBusinessCategory()), Asset::getBusinessCategory, dto.getBusinessCategory())
                .eq(StringUtils.isNotEmpty(dto.getTransactionType()), Asset::getTransactionType, dto.getTransactionType())
                .eq(StringUtils.isNotEmpty(dto.getStatusCd()), Asset::getStatusCd, dto.getStatusCd())
                .eq(StringUtils.isEmpty(dto.getStatusCd()), Asset::getShopStatus, ShopConstants.SHOP_TAKE_EFFECT)
                .eq(!SecurityUtils.isManager(), Asset::getCreateStaff, getUserId())
                .in(StringUtils.isNotEmpty(dto.getLabelName()), Asset::getLabelName, dto.getLabelName())
                .orderByDesc(Asset::getCreateDate);
        startPage();
        List<Asset> list = assetService.list(lambda);
        List<AssetVo> voList = list.stream().map(item -> {
            AssetVo assetVo = new AssetVo();
            BeanUtils.copyProperties(item, assetVo);
            return assetVo;
        }).collect(Collectors.toList());
        TableDataInfo dataTable = getDataTable(list);
        dataTable.setRows(voList);
        return R.ok(dataTable);
    }


    @ApiOperation(value = "导出", notes = "导出")
    @PostMapping("/page/export")
    public void getShopPageExport(HttpServletResponse response, AssetDto dto) {
//        List<Long> assetIds = null;
//        if (StringUtils.isNotEmpty(dto.getLabelIds())) {
//            List<AssetLabelRel> assetLabelRels = assetLabelRelService.list(Wrappers.<AssetLabelRel>query().lambda().in(AssetLabelRel::getLabelId, StrUtil.split(dto.getLabelIds(), ",")));
//            assetIds = assetLabelRels.stream().map(AssetLabelRel::getAssetId).collect(Collectors.toList());
//        }
        LambdaQueryWrapper<Asset> lambda = Wrappers.<Asset>query().lambda()
                .like(StringUtils.isNotEmpty(dto.getAssetName()), Asset::getAssetName, dto.getAssetName())
                .eq(StringUtils.isNotEmpty(dto.getAssetType()), Asset::getAssetType, dto.getAssetType())
                .eq(StringUtils.isNotEmpty(dto.getBusinessCategory()), Asset::getBusinessCategory, dto.getBusinessCategory())
                .eq(StringUtils.isNotEmpty(dto.getTransactionType()), Asset::getTransactionType, dto.getTransactionType())
                .eq(StringUtils.isNotEmpty(dto.getStatusCd()), Asset::getStatusCd, dto.getStatusCd())
                .eq(StringUtils.isEmpty(dto.getStatusCd()), Asset::getShopStatus, ShopConstants.SHOP_TAKE_EFFECT)
                .eq(!SecurityUtils.isManager(), Asset::getCreateStaff, getUserId())
                .in(StringUtils.isNotEmpty(dto.getLabelName()), Asset::getLabelName, dto.getLabelName())
                .orderByDesc(Asset::getCreateDate);
//        startPage();
        List<Asset> list = assetService.list(lambda);
        list.forEach(item->{
            AssetZoneShop assetZoneShop = assetZoneShopService.getById(item.getAssetId());
            ShopInfo shopInfo = shopInfoService.getById(assetZoneShop.getShopId());
            item.setShopName(shopInfo.getShopName());
        });

        ExcelUtil<Asset> util = new ExcelUtil<Asset>(Asset.class);
        util.exportExcel(response,list, "资产数据");
    }

    /**
     * 新增
     *
     * @param asset
     * @return R
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping
    public R save(@Validated @RequestBody Asset asset) {
        ShopInfo shopInfo= shopInfoService.getOne(Wrappers.<ShopInfo>lambdaQuery().eq(ShopInfo::getCreateStaff, getUserId()).ne(ShopInfo::getStatusCd, ShopConstants.SHOP_DELETE));
        if (ObjectUtil.isEmpty(shopInfo)){
            return R.fail("未创建店铺");
        }

        String userId = String.valueOf(SecurityUtils.getUserId());
        asset.setCreateStaff(userId);
        asset.setCreateDate(LocalDateTime.now());
        asset.setStatusDate(LocalDateTime.now());
        asset.setShopStatus(shopInfo.getStatusCd());
        asset.setStatusCd(AssetConstants.ASSET_TOBE_CERTIFIED);
        if (asset.getTransactionType().equals(AssetConstants.ASSET_PUBLIC_SALE)){
            asset.setTransactionPrice("0");
        }
        if (ObjectUtil.isNotEmpty(asset.getAssetId())){
            assetService.updateById(asset);
        }else {
            assetService.save(asset);
            AssetZoneShop assetZoneShop=new AssetZoneShop();
            assetZoneShop.setAssetId(asset.getAssetId());
            assetZoneShop.setShopId(shopInfo.getShopId());
            assetZoneShopService.save(assetZoneShop);
        }
        return R.ok("资产信息新增成功！");
    }

    @ApiOperation(value = "资产暂存", notes = "资产暂存（不校验字段）")
    @PostMapping("/temp")
    public R tempSave(@RequestBody Asset asset) { // 移除 @Validated 注解
        // 保留店铺检查逻辑
        ShopInfo shopInfo= shopInfoService.getOne(Wrappers.<ShopInfo>lambdaQuery()
                .eq(ShopInfo::getCreateStaff, getUserId())
                .ne(ShopInfo::getStatusCd, ShopConstants.SHOP_DELETE));
        if (ObjectUtil.isEmpty(shopInfo)){
            return R.fail("未创建店铺");
        }

        // 自动设置临时状态
        asset.setIsTemp(true);
        asset.setStatusCd(AssetConstants.ASSET_TEMP);

        // 保留基础字段设置
        String userId = String.valueOf(SecurityUtils.getUserId());
        asset.setCreateStaff(userId);
        asset.setCreateDate(LocalDateTime.now());
        asset.setStatusDate(LocalDateTime.now());
        asset.setShopStatus(shopInfo.getStatusCd());

        // 保存逻辑与原方法一致
        if (ObjectUtil.isNotEmpty(asset.getAssetId())){
            Asset byId = assetService.getById(asset.getAssetId());
            if (StringUtils.equalsAny(byId.getStatusCd(), AssetConstants.ASSET_CERTIFYING, AssetConstants.ASSET_ON_SHELF, AssetConstants.ASSET_DELETED)){
                return R.fail("资产该状态不允许暂存");
            }
            assetService.updateById(asset);
        }else {
            assetService.save(asset);
            AssetZoneShop assetZoneShop = new AssetZoneShop();
            assetZoneShop.setAssetId(asset.getAssetId());
            assetZoneShop.setShopId(shopInfo.getShopId());
            assetZoneShopService.save(assetZoneShop);
        }

        return R.ok("资产暂存成功！");
    }


    /**
     * 通过id查询
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @GetMapping
    @Anonymous
    public R getById(Long id) {
        Asset asset = assetService.getById(id);
        AssetVo assetVo=new AssetVo();
        BeanUtils.copyProperties(asset,assetVo);
        ShopInfoVo shopInfoVo= shopInfoService.getShopByAssetId(id);
//        shopInfoVo.setForSaleNum(Math.toIntExact(assetService.getForSaleNumByShopId(shopInfoVo.getShopId())));
        assetVo.setShopInfoVo(shopInfoVo);
        String thirtyDealNum = orderSubDetailService.thirtyDealNum(id);
        assetVo.setThirtyDealNum(thirtyDealNum);
        assetVo.setAssetCover(ossUtil.getFileUrl(assetVo.getAssetCover()));
        if (StringUtils.isNotEmpty(assetVo.getAgreementDoc()))assetVo.setAgreementDoc(ossUtil.getFileUrl(assetVo.getAgreementDoc()));
        List<AttachmentInfo> fileList = new ArrayList<>();
        if (StringUtils.isNotEmpty(asset.getAssetFileIds())) {
            for (String attId : asset.getAssetFileIds().split(",")) {
                AttachmentInfo att = attachmentInfoService.getAtt(Long.valueOf(attId));
                fileList.add(att);
            }
        }
        List<String> copyrightMaterials = new ArrayList<>();
        if (StringUtils.isNotEmpty(asset.getCopyrightMaterials())){
            if (StringUtils.isNotEmpty(asset.getCopyrightMaterials())){
                for (String s : asset.getCopyrightMaterials().split(",")) {
                    String fileUrl = ossUtil.getFileUrl(s);
                    copyrightMaterials.add(fileUrl);
                }
            }
        }
        assetVo.setCopyrightMaterials(String.join(",", copyrightMaterials));
        assetVo.setFileList(fileList);
        return R.ok(assetVo);
    }
    @ApiOperation(value = "新增/取消推荐资产", notes = "新增/取消推荐资产")
    @PutMapping("/assetHome")
    public R assetHome(Long assetId) {
        AssetHome assetHome = assetHomeService.getOne(Wrappers.<AssetHome>query().lambda().eq(AssetHome::getAssetId, assetId));
        if (ObjectUtil.isEmpty(assetHome)){
            assetHome = new AssetHome();
            assetHome.setAssetId(assetId);
            assetHome.setCreateDate(LocalDateTime.now());
            assetHome.setSort(1L);
            assetHomeService.save(assetHome);
        }else {
            assetHomeService.remove(Wrappers.<AssetHome>query().lambda().eq(AssetHome::getAssetId, assetId));
        }
        return R.ok("操作成功");
    }
    @ApiOperation(value = "新增/取消热门资产", notes = "新增/取消热门资产")
    @PutMapping("/assetHot")
    public R assetHot(Long assetId) {
        AssetHot assetHot = assetHotService.getOne(Wrappers.<AssetHot>query().lambda().eq(AssetHot::getAssetId, assetId));
        if (ObjectUtil.isEmpty(assetHot)){
            assetHot = new AssetHot();
            assetHot.setAssetId(assetId);
            assetHot.setSort(1L);
            assetHot.setCreateDate(LocalDateTime.now());
            assetHotService.save(assetHot);
        }else {
            assetHotService.remove(Wrappers.<AssetHot>query().lambda().eq(AssetHot::getAssetId, assetId));
        }
        return R.ok("操作成功");
    }
    /**
     * 修改
     * @param asset
     * @return R
     */
    @ApiOperation(value = "修改", notes = "修改")
    @PutMapping
    public R updateById(@Validated @RequestBody Asset asset) {
        Asset byId = assetService.getById(asset.getAssetId());
        if (StringUtils.equalsAny(byId.getStatusCd(), AssetConstants.ASSET_CERTIFYING, AssetConstants.ASSET_ON_SHELF, AssetConstants.ASSET_DELETED)){
            return R.fail("资产该状态不允许修改");
        }
        String userId = String.valueOf(SecurityUtils.getUserId());
        asset.setUpdateStaff(userId);
        asset.setUpdateDate(LocalDateTime.now());
        asset.setStatusCd(AssetConstants.ASSET_TOBE_CERTIFIED);
        asset.setStatusDate(LocalDateTime.now());
        assetService.updateById(asset);
//        assetLabelRelService.remove(Wrappers.<AssetLabelRel>query().lambda().eq(AssetLabelRel::getAssetId, asset.getAssetId()));
//        assetLabelRelService.insertLabelRel(asset.getAssetId(),asset.getLabelIds());
        return R.ok("资产信息修改成功！");
    }
    /**
     * 删除
     * @param assetId
     * @return R
     */
    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping
    public R updateById(Long assetId) {
        Asset byId = assetService.getById(assetId);
        if (byId.getStatusCd().equals(AssetConstants.ASSET_ON_SHELF)){
            return R.fail("上架资产不可删除");
        }
        String userId = String.valueOf(SecurityUtils.getUserId());
        byId.setStatusCd(AssetConstants.ASSET_DELETED);
        byId.setStatusDate(LocalDateTime.now());
        byId.setUpdateStaff(userId);
        byId.setUpdateDate(LocalDateTime.now());
        assetService.updateById(byId);
//        assetLabelRelService.remove(Wrappers.<AssetLabelRel>query().lambda().eq(AssetLabelRel::getAssetId, asset.getAssetId()));
//        assetLabelRelService.insertLabelRel(asset.getAssetId(),asset.getLabelIds());
        return R.ok("删除成功！");
    }

    @ApiOperation(value = "上下架", notes = "上下架")
    @PutMapping("/upDown")
    public R updateStatusById(@Validated @RequestBody AssetStatusDto dto) {
        Asset asset = assetService.getById(dto.getAssetId());
        if (asset.getStatusCd().equals(AssetConstants.ASSET_TOBE_CERTIFIED) && dto.getStatusCd().equals(AssetConstants.ASSET_CERTIFYING)){
            if (!asset.getShopStatus().equals(ShopConstants.SHOP_TAKE_EFFECT)){
                return R.fail("店铺未上架，请上架店铺后再进行操作");
            }
            String s = assetService.updateStatus(dto, getUserId());
            if (!s.isEmpty()){
                return R.fail(s);
            }
            return R.ok("上架审核中");
        }else if (asset.getStatusCd().equals(AssetConstants.ASSET_ON_SHELF) && dto.getStatusCd().equals(AssetConstants.ASSET_TOBE_CERTIFIED)){
            String s = assetService.updateStatus(dto, getUserId());
            if (!s.isEmpty()){
                return R.fail(s);
            }
            return R.ok("下架成功");
        }else {
            return R.fail("状态码错误");
        }
    }
    /**
     * 审核
     * @param assetAuditRecords
     * @return R
     */
    @ApiOperation(value = "审核", notes = "审核")
    @PostMapping("/audit")
    public R AssetAudit(@Validated @RequestBody AssetAuditRecords assetAuditRecords) {
        Asset asset = assetService.getById(assetAuditRecords.getAssetId());
        if (asset.getStatusCd().equals(AssetConstants.ASSET_CERTIFYING)){
            if (assetAuditRecords.getAuditResult().equals(AssetConstants.ASSET_ON_SHELF) || assetAuditRecords.getAuditResult().equals(AssetConstants.ASSET_CERTIFY_FAILURE)){
                AssetStatusDto dto=new AssetStatusDto(assetAuditRecords.getAssetId(),assetAuditRecords.getAuditResult(),assetAuditRecords.getAuditDescribe());
                String s = assetService.updateStatus(dto, getUserId());
                if (!s.isEmpty()){
                    return R.fail(s);
                }
                assetAuditRecords.setCreateStaff(String.valueOf(getUserId()));
                assetAuditRecords.setCreateDate(LocalDateTime.now());
                assetAuditRecordsService.save(assetAuditRecords);
            }else {
                return R.fail("审核失败，审核结果未知状态");
            }
        }else {
            return R.fail("审核失败，不符合审核状态");
        }
        return R.ok("审核成功！");
    }

    @ApiOperation(value = "获取审核记录", notes = "获取审核记录")
    @GetMapping("/auditRecords")
    public R AssetAudit(Long assetId) {
        List<AssetAuditRecords> list = assetAuditRecordsService.list(Wrappers.<AssetAuditRecords>lambdaQuery().eq(AssetAuditRecords::getAssetId, assetId).orderByAsc(AssetAuditRecords::getCreateDate));
        for (AssetAuditRecords assetAuditRecords : list) {
            Identify identify = identifyService.getIdentifyByUserId(Long.valueOf(assetAuditRecords.getCreateStaff()));
            assetAuditRecords.setCreateStaff(identify.getIdName());
        }
        return R.ok(list);
    }

    @ApiOperation(value = "文件下载", notes = "文件下载")
    @GetMapping("/fileDwon")
    public R fileDwon(String assetId) {
        Long userId = getUserId();
        long count = orderSubDetailService.count(Wrappers.<OrderSubDetail>lambdaQuery()
                .eq(OrderSubDetail::getAssetId, assetId)
                .eq(OrderSubDetail::getCreateStaff, userId)
                .eq(OrderSubDetail::getStatusCd, OrderConstants.ORDER_STATUS_PAID));
        if (count >0){
            Map<String, Object> map = new HashMap<>();
            Asset asset = assetService.getById(assetId);
            List<String> urlList=new ArrayList<>();
            for (String s : asset.getAssetFileIds().split(",")) {
                String s1 = attachmentInfoService.getById(s).getFilePath();
                String url = attachmentInfoService.getObjectUrl(s1);
                urlList.add(url);
            }
            map.put("urlList",urlList);
            map.put("assetName",asset.getAssetName());
            return R.ok(map);
        }
        return R.fail("您没有下载权限");
    }

    @ApiOperation(value = "是否已购买", notes = "是否已购买")
    @GetMapping("/isBuy")
    public R isBuy(String assetId) {
        Long userId = getUserId();
        long count = orderSubDetailService.count(Wrappers.<OrderSubDetail>lambdaQuery()
                .eq(OrderSubDetail::getAssetId, assetId)
                .eq(OrderSubDetail::getCreateStaff, userId)
                .eq(OrderSubDetail::getStatusCd, OrderConstants.ORDER_STATUS_PAID));
        if (count >0){
            return R.ok(true,"已购买");
        }
        return R.ok(false,"未购买");
    }
}
