package com.ruoyi.scwt.asset.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;


@Data
@ApiModel
@TableName("purchased_asset")
@EqualsAndHashCode(callSuper = true)
public class PurchasedAsset extends Model<PurchasedAsset> {
private static final long serialVersionUID = 1L;


  @TableId(type= IdType.AUTO)
  @ApiModelProperty(value = "ID",hidden = true)
  private Long id;
  /**
   * 资产ID
   */
  @ApiModelProperty(value = "资产ID")
  private Long assetId;
  @ApiModelProperty(value = "用户ID")
  private Long userId;


  @ApiModelProperty(value = "资产交易类型")
  private String transactionType;


  @ApiModelProperty(value = "权利，多个以逗号隔开")
  private String rights;


  @ApiModelProperty(value = "期限（天）")
  private int days;


  @ApiModelProperty(value = "购买日期")
  private LocalDateTime buyDate;



}
