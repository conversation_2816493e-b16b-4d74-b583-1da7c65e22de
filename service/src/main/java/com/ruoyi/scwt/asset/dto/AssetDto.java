package com.ruoyi.scwt.asset.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.poi.hpsf.Decimal;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;


@Data
public class AssetDto  {

  @ApiModelProperty(value = "资产ID",hidden = true)
  private Long assetId;

  @ApiModelProperty(value = "用户ID",hidden = true)
  private Long userId;

  @ApiModelProperty(value = "资产来源")
  private String assetSource;

  @ApiModelProperty(value = "资产名称")
  private String assetName;

  @ApiModelProperty(value = "资产类型")
  private String assetType;

  @ApiModelProperty(value = "资产业务类型")
  private String businessCategory;

  @ApiModelProperty(value = "资产交易类型")
  private String transactionType;

  @ApiModelProperty(value = "状态")
  private String statusCd;

  @ApiModelProperty(value = "标签")
  private String labelName;

  @ApiModelProperty(value="是否是首页展示")
  private Boolean isHome;

  @ApiModelProperty(value="是否是热门推荐")
  private Boolean isHot;

}
