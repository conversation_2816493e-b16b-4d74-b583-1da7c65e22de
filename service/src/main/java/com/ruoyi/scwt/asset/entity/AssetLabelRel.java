package com.ruoyi.scwt.asset.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 全国省市区
 *
 * <AUTHOR>
 * @date 2019-10-17 16:21:21
 */
@Data
@ApiModel
@TableName("asset_label_rel")
@EqualsAndHashCode(callSuper = true)
public class AssetLabelRel extends Model<AssetLabelRel> {
private static final long serialVersionUID = 1L;

  /**
   * ID
   */
  @TableId(type= IdType.AUTO)
  @ApiModelProperty(value = "ID")
  private Long id;

  /**
   * 资产ID
   */
  @ApiModelProperty(value = "资产ID")
  private Long assetId;

  /**
   * 标签ID
   */
  @ApiModelProperty(value = "标签ID")
  private Long labelId;


}
