package com.ruoyi.scwt.asset.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.scwt.asset.entity.Asset;
import com.ruoyi.scwt.asset.entity.AssetZoneShop;
import com.ruoyi.scwt.asset.mapper.AssetMapper;
import com.ruoyi.scwt.asset.mapper.AssetZoneShopMapper;
import com.ruoyi.scwt.asset.service.AssetService;
import com.ruoyi.scwt.asset.service.AssetZoneShopService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 实名认证
 *
 * <AUTHOR>
 * @date 2020-10-29 15:48:38
 */
@Service
public class AssetZoneShopServiceImpl extends ServiceImpl<AssetZoneShopMapper, AssetZoneShop> implements AssetZoneShopService {


    @Override
    public List<AssetZoneShop> getListByShopId(Long shopId) {
        return baseMapper.selectList(Wrappers.<AssetZoneShop>query().lambda().eq(AssetZoneShop::getShopId, shopId));
    }

    @Override
    public List<AssetZoneShop> getListByZoneId(Long zoneId) {
        return baseMapper.selectList(Wrappers.<AssetZoneShop>query().lambda().eq(AssetZoneShop::getZoneId, zoneId));
    }
}
