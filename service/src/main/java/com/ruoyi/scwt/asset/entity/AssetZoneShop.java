package com.ruoyi.scwt.asset.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.poi.hpsf.Decimal;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;


@Data
@ApiModel
@TableName("asset_zone_shop")
@EqualsAndHashCode(callSuper = true)
public class AssetZoneShop extends Model<AssetZoneShop> {
private static final long serialVersionUID = 1L;


  @TableId
  @ApiModelProperty(value = "资产ID")
  private Long assetId;

  @ApiModelProperty(value = "专区ID")
  private Long zoneId;

  @ApiModelProperty(value = "专区ID")
  private LocalDateTime zoneUpTime;

  @ApiModelProperty(value = "店铺ID")
  private Long shopId;



}
