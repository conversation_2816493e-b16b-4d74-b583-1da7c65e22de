package com.ruoyi.scwt.asset.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@ApiModel("资产收藏")
@TableName("asset_collect")
@EqualsAndHashCode(callSuper = true)
public class AssetCollect extends Model<AssetCollect> {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "收藏ID", hidden = true)
    private Long collectId;

    @ApiModelProperty(value = "用户ID", hidden = true)
    private Long userId;

    @NotNull(message = "资产ID不能为空")
    @ApiModelProperty(value = "资产ID", required = true)
    private Long assetId;

    @ApiModelProperty(value = "收藏时间", hidden = true)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "收藏状态（0正常 1取消）", hidden = true)
    private String statusCd;

    // 可根据需要添加冗余字段（如资产名称、封面等）
    @ApiModelProperty(hidden = true)
//    @TableField(exist = false)
    private String assetName;

    @ApiModelProperty(hidden = true)
//    @TableField(exist = false)
    private String assetCover;
}
