package com.ruoyi.scwt.asset.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.scwt.asset.entity.Asset;
import com.ruoyi.scwt.asset.entity.AssetCollect;
import com.ruoyi.scwt.asset.service.AssetCollectService;
import com.ruoyi.scwt.asset.service.AssetService;
import com.ruoyi.scwt.file.util.OssUtil;
import com.ruoyi.scwt.shop.constant.ShopConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 资产收藏管理
 * 
 * <AUTHOR>
 * @date 2023-11-20
 */
@RestController
@RequestMapping("/asset/collect")
public class AssetCollectController extends BaseController {

    @Autowired
    private AssetCollectService assetCollectService;
    @Autowired
    private AssetService assetService;
    @Autowired
    private OssUtil ossUtil;

    /**
     * 新增资产收藏
     */
    @PostMapping
    public R add(@RequestBody String assetIds) {
        for (String id : assetIds.split(",")) {
            AssetCollect assetCollect = new AssetCollect();
            assetCollect.setUserId(getUserId());
            assetCollect.setAssetId(Long.parseLong(id));
            assetCollect.setCreateTime(LocalDateTime.now());
            Asset asset = assetService.getById(id);
            assetCollect.setAssetName(asset.getAssetName());
            assetCollect.setAssetCover(asset.getAssetCover());
            assetCollect.setStatusCd("0");
            assetCollectService.save(assetCollect);
        }
        return R.ok(null, "添加成功");
    }

    /**
     * 删除资产收藏（支持两种删除方式）
     */
    @DeleteMapping("/{assetIds}")
    public R remove(@PathVariable String assetIds) {
        Long userId = getUserId();
        assetCollectService.remove(new QueryWrapper<AssetCollect>().eq("user_id", userId).in("asset_id", Arrays.asList(assetIds.split(","))));
        return R.ok(null, "删除成功");
    }

    /**
     * 分页查询收藏列表
     */
    @GetMapping("/page")
    public R list(AssetCollect assetCollect) {
        Long userId = getUserId();
        LambdaQueryWrapper<AssetCollect> lambda = Wrappers.<AssetCollect>query().lambda()
                .like(StringUtils.isNotEmpty(assetCollect.getAssetName()), AssetCollect::getAssetName, assetCollect.getAssetName())
                .eq( AssetCollect::getUserId, userId)
                .orderByDesc(AssetCollect::getCreateTime);
        startPage();
        List<AssetCollect> list = assetCollectService.list(lambda);
        list.forEach(item->{
            item.setAssetCover(ossUtil.getFileUrl(item.getAssetCover()));
        });
        return R.ok(getDataTable(list));
    }

    /**
     * 查询资产是否收藏
     */
    @GetMapping("/isCollect")
    public R isCollect(String assetId) {
        Long userId = getUserId();
        AssetCollect one = assetCollectService.getOne(new QueryWrapper<AssetCollect>().eq("user_id", userId).eq("asset_id", assetId));
        return R.ok(ObjectUtil.isNotEmpty(one));
    }

}
