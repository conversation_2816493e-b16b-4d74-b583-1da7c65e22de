package com.ruoyi.scwt.asset.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.poi.hpsf.Decimal;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@Data
@ApiModel
@TableName("asset")
@EqualsAndHashCode(callSuper = true)
public class Asset extends Model<Asset> {
private static final long serialVersionUID = 1L;


  @TableId(type= IdType.AUTO)
  @ApiModelProperty(value = "资产ID",hidden = true)
  private Long assetId;

  @ApiModelProperty(value = "用户ID",hidden = true)
  private Long userId;

  @ApiModelProperty(value = "店铺ID")
  @TableField(exist = false)
  private Long shopId;

  @ApiModelProperty(value = "店铺名称")
  @TableField(exist = false)
  @Excel(name = "店铺名称")
  private String shopName;

  @ApiModelProperty(value = "资产来源")
  private String assetSource;

  @NotBlank(message = "资产名称不能为空")
  @Size(max = 30,message = "资产名称不能超过30个字符")
  @ApiModelProperty(value = "资产名称")
  @Excel(name = "资产名称")
  private String assetName;

  @NotBlank(message = "资产封面不能为空")
  @ApiModelProperty(value = "资产封面")
  private String assetCover;

  @NotBlank(message = "资产类型不能为空")
  @ApiModelProperty(value = "资产类型")
  @Excel(name = "资产类型", readConverterExp = "dataSet=数据集,api=API,dataService=数据服务,datareport=数据报告,video=视频,music=音频,text=文本,img=图片,3D=3D模型")
  private String assetType;

  @NotBlank(message = "资产业务类型不能为空")
  @ApiModelProperty(value = "资产业务类型")
  @Excel(name = "资产业务类型", readConverterExp = "digitalArtworks=数字艺术品,digitalCulturalRelics=数字文物,digitalCreativeContent=数字创意内容,culturalDerivatives=文博衍生,brandMarketing=品牌营销,consumerScenario=消费场景,other=其他数字文化资产")
  private String businessCategory;

  @NotBlank(message = "资产简介不能为空")
  @Size(max = 150,message = "资产简介不能超过150个字符")
  @ApiModelProperty(value = "资产简介")
  @Excel(name = "资产简介")
  private String assetAbstract;

  @NotBlank(message = "资产交易类型不能为空")
  @ApiModelProperty(value = "资产交易类型")
  @Excel(name = "资产交易类型", readConverterExp = "bargaining=议价磋商,copyrightAuthorization=权利授权,transferOfOwnership=所有权转让,pricingSales=定价销售,dynamicTrading=动态交易,publicWelfare=公益直领")
  private String transactionType;

  @ApiModelProperty(value = "授权类型")
//  @Excel(name = "授权类型", readConverterExp = "exclusive=独家,nonExclusive=非独家")
  private String authorizationType;

  @ApiModelProperty(value = "授权地区")
//  @Excel(name = "授权地区", readConverterExp = "chinese=中国大陆地区,worldwide=全球范围")
  private String authorizationArea;

  @NotBlank(message = "资产文件ID不能为空")
  @ApiModelProperty(value = "资产文件ID，多个以逗号隔开")
  private String assetFileIds;

  @Min(value = 1,message = "个人授权价格不能小于1元")
  @ApiModelProperty(value = "个人授权价格")
  private BigDecimal personalPrice;

  @Min(value = 1,message = "企业授权价格不能小于1元")
  @ApiModelProperty(value = "企业授权价格")
  private BigDecimal enterprisePrice;

  @Min(value = 1,message = "转让价格不能小于1元")
  @ApiModelProperty(value = "转让价格")
  private BigDecimal transferPrice;

  @ApiModelProperty(value = "分辨率")
  private String resolvingPower;

  @ApiModelProperty(value = "文件大小")
  private String fileSize;

  @ApiModelProperty(value = "码率")
  private String bitRate;

  @ApiModelProperty(value = "时长")
  private String duration;

  @ApiModelProperty(value = "像素")
  private String pixel;

  @ApiModelProperty(value = "版权证明材料")
//  @NotNull(message = "版权证明材料不能为空")
  private String copyrightMaterials;

  @ApiModelProperty(value = "权利，多个以逗号隔开")
  private String rights;

  @ApiModelProperty(value = "期限（天）")
  private int days;

  @ApiModelProperty(value = "备注内容")
  private String rightRemark;

  @ApiModelProperty(value = "交易价格")
  private String transactionPrice;

  @ApiModelProperty(value = "协议文件")
  private String agreementDoc;

  @ApiModelProperty(hidden = true)
  @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime createDate;
  @ApiModelProperty(hidden = true)
  private String createStaff;
  @ApiModelProperty(hidden = true)
  private LocalDateTime updateDate;
  @ApiModelProperty(hidden = true)
  private String updateStaff;
  @ApiModelProperty(hidden = true)
  @Excel(name = "状态", readConverterExp = "3100=未上架,3200=审核中,3300=审核通过,3300=审核通过,3400=审核驳回,3500=已上架,3900=删除,3800=暂存")
  private String statusCd;
  @ApiModelProperty(hidden = true)
  private LocalDateTime statusDate;
  @ApiModelProperty(hidden = true)
  private String remark;

  @ApiModelProperty(value="标签,多个以逗号隔开")
  @NotBlank(message = "标签不能为空")
  private String labelName;
  @ApiModelProperty(value = "下架原因")
  private String downReason;
  @ApiModelProperty(value = "店铺上下架状态")
//  @Excel(name = "店铺上下架状态", readConverterExp = "3100=未上架,3200=审核中,3300=审核通过,3300=审核通过,3400=审核驳回,3500=已上架,3900=删除,3800=暂存")
  private String shopStatus;
  @ApiModelProperty(hidden = true)
  @TableField(exist = false)
  private String belongUser;

  @ApiModelProperty(value = "暂存")
  @TableField(exist = false)
  private Boolean isTemp;
}
