package com.ruoyi.scwt.asset.constant;

/**
 * <AUTHOR>
 * 通用常量
 */
public interface AssetConstants {

	/***-----------------------------------------资产状态-------------------------------------------------*/
	/**未上架*/
	String ASSET_TOBE_CERTIFIED = "3100";
	/**审核中*/
	String ASSET_CERTIFYING = "3200";
	/**审核通过*/
	String ASSET_CERTIFIED = "3300";
	/**审核驳回*/
	String ASSET_CERTIFY_FAILURE = "3400";
	/**已上架*/
	String ASSET_ON_SHELF = "3500";
	/**删除*/
	String ASSET_DELETED = "3900";
	/**暂存*/
	String ASSET_TEMP = "3800";


	/***-----------------------------------------资产交易类型-------------------------------------------------*/
	/**定价销售*/
	String ASSET_PRICE_SALE = "pricingSales";
	/**议价磋商*/
	String ASSET_BARGAINING_SALE = "bargaining";
	/**版权售价*/
	String ASSET_COPYRIGHT_SALE = "copyrightAuthorization";
	/**所有权转让*/
	String ASSET_TRANSFER_SALE = "transferOfOwnership";
	/**动态交易*/
	String ASSET_DYNAMIC_SALE = "dynamicTrading";
	/**公益直领*/
	String ASSET_PUBLIC_SALE = "publicWelfare";
}
