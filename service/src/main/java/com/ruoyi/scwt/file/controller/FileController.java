package com.ruoyi.scwt.file.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.scwt.file.entity.AttachmentInfo;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import com.ruoyi.scwt.file.vo.PreSignedObjectVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 作品管理
 *
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@RestController
@AllArgsConstructor
@RequestMapping("/file" )
@Api(value = "file", tags = "文件管理")
@Slf4j
public class FileController extends BaseController {


    @Autowired
    private AttachmentInfoService attachmentInfoService;



    @PostMapping("/cosUpload")
//    @ResponseStatus(HttpStatus.CREATED)
    @ApiOperation(value="文件上传")
    public R<AttachmentInfo> save(@ApiParam(value="文件",required=true) @RequestParam("file") MultipartFile file,
                                  @ApiParam("附件类型code,必传") @RequestParam("attachmentObjectTypeCode") String attachmentObjectTypeCode,
                                  @ApiParam("附件描述") @RequestParam(value = "fileDesc", defaultValue = "") String fileDesc) throws IOException {
        AttachmentInfo attachmentInfoVo = attachmentInfoService.upload(file, attachmentObjectTypeCode, fileDesc,String.valueOf(getUserId()));
        return R.ok(attachmentInfoVo);
    }

    @ApiOperation(value="文件上传签名url")
    @PostMapping("/preUpload")
    public R getPreSignedUploadUrl(@Validated @RequestBody PreSignedObjectVo preSignedObjectVo) {
        String signedUploadUrl = attachmentInfoService.getPreSignedUploadUrl(preSignedObjectVo, String.valueOf(getUserId()));
        return R.ok(signedUploadUrl);
    }

    @GetMapping("/getObjectUrl")
    public R getObjectUrl(String key) {
        String objectUrl = attachmentInfoService.getObjectUrl(key);
        return R.ok(objectUrl);
    }

}
