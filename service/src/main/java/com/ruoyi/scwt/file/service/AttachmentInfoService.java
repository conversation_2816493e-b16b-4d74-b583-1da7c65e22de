package com.ruoyi.scwt.file.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.scwt.file.entity.AttachmentInfo;
import com.ruoyi.scwt.file.vo.PreSignedObjectVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


public interface AttachmentInfoService extends IService<AttachmentInfo> {

    AttachmentInfo upload(MultipartFile file, String attachmentObjectTypeCode, String fileDesc, String userId);

    String getPreSignedUploadUrl(PreSignedObjectVo preSignedObjectVo, String userId);

    String getObjectUrl(String key);

    AttachmentInfo getAtt(Long valueOf);

    List<AttachmentInfo> getIdByPath(String idCard);

     String getFileBase64(String key);
}
