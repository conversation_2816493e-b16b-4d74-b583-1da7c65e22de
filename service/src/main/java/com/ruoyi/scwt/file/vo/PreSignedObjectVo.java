package com.ruoyi.scwt.file.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @ClassName IdentifyVo
 */
@Data
@ApiModel
public class PreSignedObjectVo {
    @NotNull
    @ApiModelProperty("附件所属对象类型")
    private String attachmentObjectTypeCode;
    @NotNull
    @ApiModelProperty("附件源文件名称")
    @Size(max = 30, message = "文件名长度不能超过30")
    private String originalFilename;
    @NotNull
    @ApiModelProperty("附件大小")
    private String fileSize;
    @NotNull
    @ApiModelProperty("附件描述")
    @Size(max = 50, message = "附件描述不能超过50")
    private String fileDesc;
    @NotNull
    @ApiModelProperty("附件md5")
    private String md5;


}
