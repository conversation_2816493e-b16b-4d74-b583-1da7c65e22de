package com.ruoyi.scwt.file.service.impl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestAlgorithm;
import cn.hutool.crypto.digest.Digester;
import com.aliyun.oss.model.PutObjectResult;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.scwt.exception.BusinessCodeEnum;
import com.ruoyi.scwt.exception.BusinessException;
import com.ruoyi.scwt.file.entity.AttachmentInfo;
import com.ruoyi.scwt.file.entity.AttachmentObjectType;
import com.ruoyi.scwt.file.enums.MimeTypeEnum;
import com.ruoyi.scwt.file.mapper.AttachmentInfoMapper;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import com.ruoyi.scwt.file.service.AttachmentObjectTypeService;
import com.ruoyi.scwt.file.util.OssUtil;
import com.ruoyi.scwt.file.vo.PreSignedObjectVo;
import lombok.Cleanup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 *
 *
 * <AUTHOR>
 * @date 2019-09-27 16:00:17
 */
@Service
public class AttachmentInfoServiceImpl extends ServiceImpl<AttachmentInfoMapper, AttachmentInfo> implements AttachmentInfoService {

    @Autowired
    private AttachmentObjectTypeService attachmentObjectTypeService;


    @Autowired
    private OssUtil ossUtil;
    @Value("${tencent.oss.downUrl}")
    private String downUrl;

    @Override
    public AttachmentInfo upload(MultipartFile file, String attachmentObjectTypeCode, String fileDesc, String userId) {
        if (StrUtil.isEmpty(userId)){
            userId= String.valueOf(SecurityUtils.getUserId());
        }
        if (Objects.requireNonNull(file.getOriginalFilename()).length()>30){
            throw new BusinessException("文件名称在30个字符以内");
        }
        AttachmentObjectType attachmentObjectType = attachmentObjectTypeService.getById(attachmentObjectTypeCode);
        if (attachmentObjectType == null) {
            throw new BusinessException("文件类型不能为空");
        }
        String ext;
        if (Objects.requireNonNull(file.getOriginalFilename()).contains(StrUtil.DOT)) {
            ext = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(StrUtil.DOT) + 1);
        } else {
            ext = Objects.requireNonNull(file.getContentType()).substring(file.getContentType().indexOf(StrUtil.SLASH) + 1);
        }
        ext = ext.toLowerCase();
        if(StrUtil.isNotBlank(ext)){
            if(!ossUtil.fileTypeVerify(ext)){
                String fileType = ossUtil.allowFileType();
                throw new BusinessException("不合法的文件类型,请上传以下格式文件:{}", BusinessCodeEnum.valueOf(fileType));
            }
        }
        String mimeType = MimeTypeEnum.getByExtension(ext.toLowerCase()).getMimeType();
        String filePath = attachmentObjectType.getAttachmentObjectTypeName()+StrUtil.SLASH+ UUID.fastUUID() + StrUtil.DOT + ext;
        PutObjectResult putObjectResult = null;
        try {
            @Cleanup
            InputStream inputStream = file.getInputStream();
            putObjectResult = ossUtil.fileUpload(filePath, inputStream);
            inputStream.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
//        log.info("文件上传返回数据：{}" + result.getObjectUrl());
        Digester md5 = new Digester(DigestAlgorithm.MD5);
        byte[] bytes = new byte[0];
        try {
            bytes = md5.digest(file.getInputStream(), IoUtil.DEFAULT_MIDDLE_BUFFER_SIZE);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        String hashCode = HexUtil.encodeHexStr(bytes);
        AttachmentInfo attachmentInfo = new AttachmentInfo(attachmentObjectTypeCode,file.getOriginalFilename(),mimeType,String.valueOf(file.getSize()),fileDesc,filePath,null,null,hashCode,"1000",Integer.valueOf(userId),null);
        boolean b = attachmentInfo.insert();
        if (b) {
            attachmentInfo.setFileUrl(downUrl+StrUtil.SLASH + filePath);
            return attachmentInfo;
        } else {
            throw BusinessException.resourceCreateException();
        }
    }

    @Override
    public String getPreSignedUploadUrl(PreSignedObjectVo preSignedObjectVo, String userId) {
        try {
            AttachmentObjectType attachmentObjectType = attachmentObjectTypeService.getById(preSignedObjectVo.getAttachmentObjectTypeCode());
            if (attachmentObjectType == null) {
                throw new BusinessException("文件类型code不正确");
            }
            String ext;
            if (Objects.requireNonNull(preSignedObjectVo.getOriginalFilename()).contains(StrUtil.DOT)) {
                ext = preSignedObjectVo.getOriginalFilename().substring(preSignedObjectVo.getOriginalFilename().lastIndexOf(StrUtil.DOT) + 1);
            } else {
                ext = "";
            }
            ext = ext.toLowerCase();
            if(StrUtil.isNotBlank(ext)){
                if(!ossUtil.fileTypeVerify(ext)){
                    throw new BusinessException("不合法的文件类型");
                }
            }
            String mimeType = MimeTypeEnum.getByExtension(ext.toLowerCase()).getMimeType();
            String fileName = attachmentObjectType.getAttachmentObjectTypeName()+StrUtil.SLASH + UUID.fastUUID() + StrUtil.DOT + ext;
            Map<String, String> headers = new HashMap<>();
//            String contentType = "text/plain";
            String contentType = "application/octet-stream";
            headers.put("Content-Type", contentType);
            String preSignedUrl = ossUtil.generatePreSignedUrl(fileName, null, headers);
            AttachmentInfo attachmentInfo = new AttachmentInfo(attachmentObjectType.getAttachmentObjectTypeName(),preSignedObjectVo.getOriginalFilename(),mimeType,preSignedObjectVo.getFileSize(),preSignedObjectVo.getFileDesc(),fileName,null,null,preSignedObjectVo.getMd5(),"1000",Integer.valueOf(userId),null);
            boolean b = attachmentInfo.insert();
            return preSignedUrl;
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }

    @Override
    public String getObjectUrl(String key) {
        return ossUtil.getFileUrl(key);
    }

    @Override
    public AttachmentInfo getAtt(Long id) {
        AttachmentInfo attachmentInfo = baseMapper.selectById(id);
        attachmentInfo.setFileUrl(getObjectUrl(attachmentInfo.getFilePath()));
        return attachmentInfo;
    }

    @Override
    public List<AttachmentInfo> getIdByPath(String idCard) {
        String[] split = idCard.split(",");
        List<AttachmentInfo> list = new ArrayList<>();
        for (String s : split) {
            AttachmentInfo attachmentInfo = baseMapper.selectOne(Wrappers.<AttachmentInfo>lambdaQuery().eq(AttachmentInfo::getFilePath,s));
            list.add(attachmentInfo);
        }
        return list;
    }

    /**
     * 根据地址获取文件并转为base64
     * @param key
     * @return
     */
    @Override
    public String getFileBase64(String key) {
        InputStream inputStream = ossUtil.createCosClient().getObject(ossUtil.getBucketName(), key).getObjectContent();
        byte[] bytes = IoUtil.readBytes(inputStream);
        return Base64.getEncoder().encodeToString(bytes);
    }
}
