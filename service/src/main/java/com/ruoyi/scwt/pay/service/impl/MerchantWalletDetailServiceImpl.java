package com.ruoyi.scwt.pay.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.scwt.pay.constant.OrderConstants;
import com.ruoyi.scwt.pay.entify.MerchantWalletDetail;
import com.ruoyi.scwt.pay.mapper.MerchantWalletDetailMapper;
import com.ruoyi.scwt.pay.service.MerchantWalletDetailService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2023-03-17
 */
@Service
@AllArgsConstructor
public class MerchantWalletDetailServiceImpl extends ServiceImpl<MerchantWalletDetailMapper, MerchantWalletDetail> implements MerchantWalletDetailService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(String orderNo, BigDecimal totalAmount, String recordType, String recordRemark, String merchantNo,String qurWithdrawBalanceData, String processWithdrawData) {
        MerchantWalletDetail merchantWalletDetail = new MerchantWalletDetail();
        merchantWalletDetail.setMerchantNo(merchantNo);
        merchantWalletDetail.setRecordNo(orderNo);
        merchantWalletDetail.setRecordType(recordType);
        merchantWalletDetail.setMoney(totalAmount);
        merchantWalletDetail.setRecordRemark(recordRemark);
        merchantWalletDetail.setCreateDate(LocalDateTime.now());
        merchantWalletDetail.setQurWithdrawBalanceData(qurWithdrawBalanceData);
        merchantWalletDetail.setProcessWithdrawData(processWithdrawData);
        merchantWalletDetail.setStatusCd(OrderConstants.WITHDRAW_STATUS_INIT);
        return merchantWalletDetail.insert();
    }

}