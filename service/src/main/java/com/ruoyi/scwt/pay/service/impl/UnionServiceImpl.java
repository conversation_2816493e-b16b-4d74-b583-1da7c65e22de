package com.ruoyi.scwt.pay.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.scwt.common.util.AESUtils;
import com.ruoyi.scwt.common.util.ImageUtil;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import com.ruoyi.scwt.identify.entity.Identify;
import com.ruoyi.scwt.identify.service.IdentifyService;
import com.ruoyi.scwt.pay.constant.OrderConstants;
import com.ruoyi.scwt.pay.dto.ChinaumsSharingRuleVO;
import com.ruoyi.scwt.pay.dto.PicUploadResultDto;
import com.ruoyi.scwt.pay.entify.*;
import com.ruoyi.scwt.pay.mapper.*;
import com.ruoyi.scwt.pay.service.UnionService;
import com.ruoyi.scwt.shop.constant.ShopConstants;
import com.ruoyi.scwt.shop.entity.ShopInfo;
import com.ruoyi.scwt.shop.service.ShopInfoService;
import com.ruoyi.scwt.sop.dto.*;
import com.ruoyi.sop.dto.ChinaumsPayProperties;
import com.ruoyi.sop.service.ChinaumsCommonService;
import com.ruoyi.sop.service.MerchantSignBySelfService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.awt.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UnionServiceImpl extends ServiceImpl<UnionMapper, Union> implements UnionService {

    @Autowired
    private MerchantSignBySelfService merchantSignBySelfService;


    private final ChinaumsPayProperties chinaumsPayProperties;
    private final UnionPicUploadMapper unionPicUploadMapper;
    private final UnionBnfMapper unionBnfMapper;
    private final UnionMccMapper unionMccMapper;
    private final UnionRegionMapper unionRegionMapper;
    private final ShopInfoService shopInfoService;
    private final IdentifyService identifyService;
    private final AttachmentInfoService attachmentInfoService;

    private final ChinaumsCommonService chinaumsCommonService;

    @Override
    public String createSeq(Long userId) {
        String unionCode = chinaumsPayProperties.getSystemId();
        String time = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String traceId = UUID.randomUUID().toString().substring(0, 3).toUpperCase();
        return unionCode + userId + time + traceId;
    }

    @Override
    public R upload(UnionPicUpload unionPicUpload) {
        com.ruoyi.sop.dto.ChinaumsMerchantPicUploadDto dto = new com.ruoyi.sop.dto.ChinaumsMerchantPicUploadDto();
        dto.setRequestSeq(unionPicUpload.getRequestSeq());
        dto.setBase64(unionPicUpload.getBase64());
        String response = merchantSignBySelfService.uploadPic(dto);
        JSONObject jsonObject = JSONUtil.parseObj(response.replace("\"", "").replace("\\", ""));
        unionPicUploadMapper.insert(unionPicUpload);
        if (jsonObject.getStr("res_code").equals("0000")) {
            unionPicUpload.setFilePath(jsonObject.getStr("file_path"));
            unionPicUpload.setFileType(jsonObject.getStr("file_type"));
            unionPicUpload.setFileSize(jsonObject.getStr("file_size"));
            unionPicUpload.setCreateDate(LocalDateTime.now());
            unionPicUploadMapper.updateById(unionPicUpload);
            return R.ok(unionPicUpload, "上传成功");
        } else {
            return R.fail("上传失败:{}", jsonObject.getStr("res_msg"));
        }
    }

    /***
     * 银联进件
     * @param union
     * @return
     */
    @Override
    public R autoReg(Union union) {
        ShopInfo shopInfo = shopInfoService.getOne(Wrappers.<ShopInfo>lambdaQuery().eq(ShopInfo::getCreateStaff, union.getCreateStaff()).ne(ShopInfo::getStatusCd, ShopConstants.SHOP_DELETE));
        if (!shopInfo.getUnionStatus().equals(OrderConstants.UNION_TOBE_CERTIFIED)){
            return R.fail("店铺当前状态不可进行结算认证");
        }
        Identify identify = identifyService.getIdentifyByUserId(Long.valueOf(union.getCreateStaff()));
        union.setShopId(shopInfo.getShopId());
        if (union.getBankAcctType().equals(OrderConstants.UNION_BANK_PERSONAL)){
            if (!identify.getIdName().equals(union.getLegalName()) || !identify.getIdNo().equals(union.getLegalIdcardNo())){
                return R.fail("结算主体与实名认证主体不一致");
            }
        }else {
            if (!identify.getIdName().equals(union.getBankAcctName()) || !identify.getIdNo().equals(union.getShopLic()) || !identify.getLegalName().equals(union.getLegalName())){
                return R.fail("结算主体与实名认证主体不一致");
            }
        }

        Union one = baseMapper.selectOne(Wrappers.<Union>lambdaQuery().eq(Union::getShopId, union.getShopId()));
        if (ObjectUtil.isNotEmpty(one)){
            if (StringUtils.isNotEmpty(one.getUmsRegId())){
                return R.fail("已有认证信息，请勿重复提交");
            }else {
                List<String> collect = union.getPicList().stream().map(ChinaumsMerchantPicDto::getFilePath).collect(Collectors.toList());
                this.deleteDuplicate(union.getShopId(),union.getId(),collect);
            }
        }
        union.setCreateDate(LocalDateTime.now());
        union.setStatusCd(OrderConstants.UNION_TOBE_CERTIFIED);
        baseMapper.insert(union);
        for (ChinaumsMerchantPicDto picUpload : union.getPicList()) {
            UnionPicUpload unionPicUpload = unionPicUploadMapper.selectOne(Wrappers.<UnionPicUpload>lambdaQuery().eq(UnionPicUpload::getFilePath, picUpload.getFilePath()));
            unionPicUpload.setDocumentName(picUpload.getDocumentName());
            unionPicUpload.setDocumentType(picUpload.getDocumentType());
            unionPicUploadMapper.updateById(unionPicUpload);
        }

        if (union.getBnfList().size()>0){
            union.getBnfList().forEach(bnf -> {
                UnionBnf unionBnf=new UnionBnf();
                BeanUtil.copyProperties(bnf,unionBnf);
                unionBnf.setUnionId(union.getId());
                unionBnfMapper.insert(unionBnf);
            });
        }
        if (!union.getIsTemp()) {
            Map<String, Object> map = createAgreement(union.getLegalName(), union.getBankAcctName(), union.getRequestSeq());
            union.getPicList().add((ChinaumsMerchantPicDto) map.get("dto"));
            union.setAgreementResult(map.get("data").toString());
//            ChinaumsMerchantPicUploadDto picUploadDto = new ChinaumsMerchantPicUploadDto()
//                    .setRequestSeq(union.getRequestSeq()+"0034")
//                    .setBase64(chinaumsPayProperties.getShopBgBase64());
//            Map<String, Object> map1 = uploadPic(picUploadDto, "0034", "商户网站", "商户网站");
//            union.getPicList().add((ChinaumsMerchantPicDto) map1.get("dto"));
            ChinaumsMerchantComplexUploadDto merchantComplexUploadDto = new ChinaumsMerchantComplexUploadDto();
            BeanUtil.copyProperties(union, merchantComplexUploadDto);
            List<JSONObject>  product = new ArrayList<JSONObject>();
            String[] ids = {"8","16"};
            for (String id : ids) {
                // 开通业务id 字典项，暂时写死
                JSONObject productObject = new JSONObject();
                productObject.put("product_id", id);
                product.add(productObject);
            }
            merchantComplexUploadDto.setProduct(product);
            merchantComplexUploadDto.setAccesserId(chinaumsPayProperties.getAccesserId());
            merchantComplexUploadDto.setAccesserUserId(union.getCreateStaff());
            log.info("商户进件信息：{}",JSONUtil.toJsonStr(merchantComplexUploadDto));
            String response = merchantSignBySelfService.unifiedPost(JSONUtil.toJsonStr(merchantComplexUploadDto));
            JSONObject complexUpload = JSONUtil.parseObj(response.replace("\\", "").replace("\"{", "{").replace("}\"", "}"));
            System.out.println(complexUpload);
            if (complexUpload.getStr("res_code").equals("0000")) {
                union.setUmsRegId(complexUpload.getStr("ums_reg_id"));
                if (union.getBankAcctType().equals(OrderConstants.UNION_BANK_ENTERPRISE)) {
                    union.setStatusCd(OrderConstants.UNION_AUTH_WAIT);
                } else {
                    union.setStatusCd(OrderConstants.UNION_AUTH_WAIT_SIGN);
                }
                baseMapper.updateById(union);
                shopInfo.setUnionStatus(union.getStatusCd());
                shopInfoService.updateById(shopInfo);
                return R.ok("进件成功");
            } else {
                shopInfo.setUnionStatus(OrderConstants.UNION_AUTH_FAILURE);
                return R.fail(complexUpload.getStr("res_msg"));
            }
        }else {
            return R.ok("暂存成功");
        }
    }

    /**
     * 生成协议文件
     * @param legalName 法人名称
     * @param requestSeq 请求流水号
     * @return
     */
    @Override
    public Map<String,Object> createAgreement(String legalName,String bankAcctName, String requestSeq) {
        String url = chinaumsPayProperties.getBgImgUrl();
        List<String> list = Lists.newLinkedList();
        list.add(bankAcctName);
        list.add(legalName);
        String base64 = ImageUtil.word2Image(url, 1, "宋体", Font.PLAIN, 35, Color.BLACK, list, 195, 768, "png");
        com.ruoyi.sop.dto.ChinaumsMerchantPicUploadDto picUploadDto = new com.ruoyi.sop.dto.ChinaumsMerchantPicUploadDto()
                .setRequestSeq(requestSeq+"0099")
                .setBase64(base64);
        // 上传协议文件
        return uploadPic(picUploadDto, "0099", "其他材料", "其他材料");
//        String s = merchantSignBySelfService.uploadPic(picUploadDto);
//        PicUploadResultDto resultDto = JSONUtil.toBean(s, PicUploadResultDto.class);
//        ChinaumsMerchantPicDto chinaumsMerchantPicDto = new ChinaumsMerchantPicDto()
//                .setDocumentType("0099")
//                .setDocumentName("其他材料")
//                .setFilePath(resultDto.getFilePath())
//                .setFileSize(resultDto.getFileSize())
//                .setRemark("其他材料");
//        Map<String,Object> map = new HashMap<>();
//        map.put("data",s);
//        map.put("dto",chinaumsMerchantPicDto);
//        return map;
    }

    @Override
    public AjaxResult gatewayPay(OrderSubDetail orderSubDetail) {
        String url = chinaumsPayProperties.getGatewayBaseUrl()+"/shop/gateway/pay";
        Map<String, Object> requestBody = new HashMap<>();
        ObjectMapper mapper = new ObjectMapper();
        String jsonStr = JSONUtil.toJsonStr(orderSubDetail.getSharingRule());
        ChinaumsSharingRuleVO sharingRule = null;
        try {
            sharingRule = mapper.readValue(
                    jsonStr,
                    ChinaumsSharingRuleVO.class
            );
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        requestBody.put("vaFld", sharingRule);
        requestBody.put("sysSource", "OPT");
        requestBody.put("msgType", "upg.order");
        requestBody.put("vaMchntNo", chinaumsPayProperties.getMid());
        requestBody.put("vaTermNo", chinaumsPayProperties.getTid());
        requestBody.put("mchntOrderId", orderSubDetail.getSopOrderNo());
        requestBody.put("chnlType", "PC");
        requestBody.put("totalAmount", orderSubDetail.getOrderMoney().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).intValue());
//        requestBody.put("billDesc", orderSubDetail.getWorksName());
        requestBody.put("notifyUrl", chinaumsPayProperties.getNotifyUrl());
        requestBody.put("transType", "UPG_BUSINESS");
        requestBody.put("bizType", "100001");
        requestBody.put("returnUrl", "https://www.sccdex.com/userCenter/page/index?switchType=user&menuKey=user");
        String pay = chinaumsCommonService.createRequest(JSONUtil.toJsonStr(requestBody), url);
        Map<String, String> map = new HashMap<>();
        map.put("payUrl", pay);
        map.put("orderNo", orderSubDetail.getOrderNo());
        return AjaxResult.success(map);
    }

    public Map<String,Object> uploadPic(com.ruoyi.sop.dto.ChinaumsMerchantPicUploadDto picUploadDto,String documentType,String documentName,String remark) {
        String s = merchantSignBySelfService.uploadPic(picUploadDto);
        PicUploadResultDto resultDto = JSONUtil.toBean(s, PicUploadResultDto.class);
        ChinaumsMerchantPicDto chinaumsMerchantPicDto = new ChinaumsMerchantPicDto()
                .setDocumentType(documentType)
                .setDocumentName(documentName)
                .setFilePath(resultDto.getFilePath())
                .setFileSize(resultDto.getFileSize())
                .setRemark(remark);
        Map<String,Object> map = new HashMap<>();
        map.put("data",s);
        map.put("dto",chinaumsMerchantPicDto);
        return map;
    }

    //删除重复信息
    public void deleteDuplicate(Long shopId, Long unionId,List<String> collect){
        baseMapper.delete(Wrappers.<Union>lambdaQuery().eq(Union::getShopId, shopId));
        unionPicUploadMapper.delete(Wrappers.<UnionPicUpload>lambdaQuery().eq(UnionPicUpload::getShopId, shopId).notIn(UnionPicUpload::getFilePath, collect));
        unionBnfMapper.delete(Wrappers.<UnionBnf>lambdaQuery().eq(UnionBnf::getUnionId, unionId));
    }

    @Override
    public R requestAccountVerify(Long userId) {
        Union union = baseMapper.selectOne(Wrappers.<Union>lambdaQuery().eq(Union::getCreateStaff, userId));
        if (!union.getStatusCd().equals(OrderConstants.UNION_AUTH_WAIT)){
            return R.fail("当前状态不可发起对公账户验证");
        }
        String jsonObject = merchantSignBySelfService.requestAccountVerify(union.getBankAcctNo(),union.getUmsRegId());
        union.setVerifyStatus(OrderConstants.UNION_VERIFY_SEND);
        union.setStatusCd(OrderConstants.UNION_AUTH_WAIT_ACCOUNT);
        baseMapper.updateById(union);
        shopInfoService.update(Wrappers.<ShopInfo>lambdaUpdate().eq(ShopInfo::getShopId, union.getShopId()).set(ShopInfo::getUnionStatus, union.getStatusCd()));
        return R.ok(jsonObject);
    }

    @Override
    public R companyAccountVerify(Long userId, String transAmt) {
        Union union = baseMapper.selectOne(Wrappers.<Union>lambdaQuery().eq(Union::getCreateStaff, userId));
        if (!union.getStatusCd().equals(OrderConstants.UNION_AUTH_WAIT_ACCOUNT)){
            return R.fail("当前状态不可发起对公账户验证");
        }
        String jsonObject = merchantSignBySelfService.companyAccountVerify(union.getBankAcctNo(),transAmt,union.getUmsRegId());
        union.setVerifyStatus(OrderConstants.UNION_VERIFY_SUCCESS);
        union.setStatusCd(OrderConstants.UNION_AUTH_WAIT_SIGN);
        baseMapper.updateById(union);
        shopInfoService.update(Wrappers.<ShopInfo>lambdaUpdate().eq(ShopInfo::getShopId, union.getShopId()).set(ShopInfo::getUnionStatus, union.getStatusCd()));
        return R.ok(jsonObject);
    }

    @Override
    public R complexAgreementSign(Long userId) {
        Union union = baseMapper.selectOne(Wrappers.<Union>lambdaQuery().eq(Union::getCreateStaff, userId));
        if (!union.getStatusCd().equals(OrderConstants.UNION_AUTH_WAIT_SIGN)){
            return R.fail("当前状态不可发起签约");
        }
        String url = merchantSignBySelfService.agreementSign(union.getUmsRegId());
        baseMapper.update(null, Wrappers.<Union>lambdaUpdate()
                .eq(Union::getId, union.getId())
                .set(Union::getApplyStatus, null)
                .set(Union::getApplyStatusMsg, null));
        return R.ok(url);
    }

    @Override
    public R getUnionMccList() {
        List<UnionMcc> unionMccs = unionMccMapper.selectList(Wrappers.lambdaQuery());
        return R.ok(unionMccs);
    }

    @Override
    public R getUnionRegionList(String parCode) {
        List<UnionRegion> unionRegions = unionRegionMapper.selectList(Wrappers.<UnionRegion>lambdaQuery().eq(UnionRegion::getParCode, parCode));
        return R.ok(unionRegions);
    }

    @Override
//    @Async
    public String complexApplyQry(Union union) {
        String jsonData = merchantSignBySelfService.complexApplyQry(union.getUmsRegId());
        JSONObject jsonObject = JSONUtil.parseObj(jsonData);
        log.info("银联入网状态查询：{}", jsonData);
        String applyStatus = jsonObject.getStr("apply_status");
        if (StringUtils.equals(applyStatus, "03") && StringUtils.isNotEmpty(jsonObject.getStr("mer_no"))){
            union.setStatusCd(OrderConstants.UNION_AUTH_SUCCESS);
            union.setApplyStatus(applyStatus);
            union.setApplyStatusMsg(jsonObject.getStr("apply_status_msg"));
            union.setMerNo(jsonObject.getStr("mer_no"));
            JSONArray mapp_info_list = jsonObject.getJSONArray("mapp_info_list");
            String secondMappNo = mapp_info_list.getJSONObject(1).getStr("mapp_no");
            union.setWithdrawalMerNo(secondMappNo);
            union.setStatusDate(LocalDateTime.now());
            union.setUpdateDate(LocalDateTime.now());
            JSONObject terminalsQry = terminalsQry(union);
            if (ObjectUtil.isNotEmpty(terminalsQry)){
                String[] terminals = terminalsQry.getStr("terminals").split(",");
                union.setTermNo(terminals[0]);
                if (terminals.length > 1){
                    union.setWithdrawalTermNo(terminals[1]);
                }else {
                    union.setWithdrawalTermNo("12345678");
                }
            }
            baseMapper.updateById(union);
        }else if (StringUtils.equalsAny(applyStatus, "32","33") || StringUtils.equals(applyStatus, "03") && StringUtils.isEmpty(jsonObject.getStr("mer_no"))){
            union.setApplyStatus(applyStatus);
            union.setApplyStatusMsg(jsonObject.getStr("apply_status_msg"));
            union.setUpdateDate(LocalDateTime.now());
            baseMapper.updateById(union);
        }else if (StringUtils.equalsAny(applyStatus, "04","28")){
            union.setApplyStatus(applyStatus);
            union.setApplyStatusMsg(jsonObject.getStr("apply_status_msg"));
            union.setStatusCd(OrderConstants.UNION_AUTH_FAILURE);
            union.setUpdateDate(LocalDateTime.now());
            baseMapper.updateById(union);
        }
        return jsonObject.getStr("apply_status_msg");
    }

    @Override
    public void toUnion(ChinaumsMerchantComplexUploadDto dto) {
//        Union union = baseMapper.selectOne(Wrappers.<Union>lambdaQuery().eq(Union::getBankAcctNo, dto.getBankAcctNo()));
//        dto.setRequestSeq(createSeq(Long.valueOf(union.getCreateStaff())));
//        JSONObject complexUpload = chinaumsMerchantController.complexUpload(dto);
//        System.out.println(complexUpload);
//        if (complexUpload.getStr("res_code").equals("0000")) {
//            union.setUmsRegId(complexUpload.getStr("ums_reg_id"));
//            if (union.getBankAcctType().equals(OrderConstants.UNION_BANK_ENTERPRISE)) {
//                union.setStatusCd(OrderConstants.UNION_AUTH_WAIT);
//            }else {
//                union.setStatusCd(OrderConstants.UNION_AUTH_WAIT_SIGN);
//            }
//            baseMapper.updateById(union);
//            shopInfo.setUnionStatus(union.getStatusCd());
//            shopInfoService.updateById(shopInfo);
//            return R.ok("进件成功");
//        }
//
    }

    @Override
    public Union getUnionInfoEcho(Long userId) {
        Union union = baseMapper.selectOne(Wrappers.<Union>lambdaQuery().eq(Union::getCreateStaff, userId));
        if (ObjectUtil.isNotEmpty(union)){
            List<UnionPicUpload> unionPicUploads = unionPicUploadMapper.selectList(Wrappers.<UnionPicUpload>lambdaQuery().eq(UnionPicUpload::getShopId, union.getShopId()).isNotNull(UnionPicUpload::getDocumentName).isNotNull(UnionPicUpload::getDocumentType));
            List<ChinaumsMerchantPicDto> chinaumsMerchantPicDtos = new ArrayList<>();
            for (UnionPicUpload unionPicUpload : unionPicUploads) {
                ChinaumsMerchantPicDto chinaumsMerchantPicDto = new ChinaumsMerchantPicDto();
                BeanUtil.copyProperties(unionPicUpload, chinaumsMerchantPicDto);
                chinaumsMerchantPicDtos.add(chinaumsMerchantPicDto);
            }
            union.setPicList(chinaumsMerchantPicDtos);
            List<UnionBnf> unionBnfs = unionBnfMapper.selectList(Wrappers.<UnionBnf>lambdaQuery().eq(UnionBnf::getUnionId, union.getId()));
            List<ChinaumsMerchantBnfDto> chinaumsMerchantBnfDtos = new ArrayList<>();
            for (UnionBnf unionBnf : unionBnfs) {
                ChinaumsMerchantBnfDto chinaumsMerchantBnfDto = new ChinaumsMerchantBnfDto();
                BeanUtil.copyProperties(unionBnf, chinaumsMerchantBnfDto);
                chinaumsMerchantBnfDtos.add(chinaumsMerchantBnfDto);
            }
            union.setBnfList(chinaumsMerchantBnfDtos);
            UnionRegion province = unionRegionMapper.selectOne(Wrappers.<UnionRegion>lambdaQuery().eq(UnionRegion::getCode, union.getShopProvinceId()));
            UnionRegion city = unionRegionMapper.selectOne(Wrappers.<UnionRegion>lambdaQuery().eq(UnionRegion::getCode, union.getShopCityId()));
            UnionRegion country = unionRegionMapper.selectOne(Wrappers.<UnionRegion>lambdaQuery().eq(UnionRegion::getCode, union.getShopCountryId()));
            union.setShopAddr(province.getName()+"/"+city.getName()+"/"+country.getName());
        }
        return union;
    }

    @Override
    public R getUnionFileEcho(String filePath) {
        UnionPicUpload unionPicUpload = unionPicUploadMapper.selectOne(Wrappers.<UnionPicUpload>lambdaQuery().eq(UnionPicUpload::getFilePath, filePath));
        return R.ok(unionPicUpload);
    }

    public AjaxResult pay(OrderSubDetail orderSubDetail) {
//        String merId="";
//        Union union = baseMapper.selectOne(Wrappers.<Union>lambdaQuery().eq(Union::getCreateStaff, orderSubDetail.getSellerId()));
//        if (orderSubDetail.getTradeType().equals(OrderConstants.ORDER_TYPE_MARKET)) {
//            if (ObjectUtil.isEmpty(union)) {
//                return AjaxResult.error("商户未开通结算认证，不可交易");
//            } else if (StrUtil.isEmpty(union.getMerNo())) {
//                return AjaxResult.error("商户未渠道商户号，不可交易");
//            } else {
//                merId=union.getMerNo();
//            }
//        }else {
//            merId=chinaumsPayProperties.getMid();
//        }
        String url = chinaumsPayProperties.getCtbBaseUrl() + "/v1/inip/upsp/shop/csb/get-qrcode";
        Map<String, Object> requestBody = new HashMap<>();
//        JSONObject vaFld = new JSONObject();
//        vaFld.put("newShopFlag", "GWCXS");
//        vaFld.put("satType", "01");
//        int totalAmount = orderSubDetail.getOrderMoney().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).intValue();
//        //平台分账金额
//        int platAmt = union.getRate().multiply(new BigDecimal(totalAmount)).setScale(0, RoundingMode.HALF_UP).intValue();
//        vaFld.put("platAmt", platAmt);
//        List<JSONObject> list = new ArrayList<>();
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("merId", merId);
//        jsonObject.put("satAmt", totalAmount-platAmt);
//        jsonObject.put("orderDesc", orderSubDetail.getWorksName());
//        list.add(jsonObject);
//        vaFld.put("satInfo", list);
//        ChinaumsSharingRuleVO sharingRule = BeanUtil.toBean(orderSubDetail.getSharingRule(), ChinaumsSharingRuleVO.class);
        ObjectMapper mapper = new ObjectMapper();
        String jsonStr = JSONUtil.toJsonStr(orderSubDetail.getSharingRule());
        ChinaumsSharingRuleVO sharingRule = null;
        try {
            sharingRule = mapper.readValue(
                    jsonStr,
                    ChinaumsSharingRuleVO.class
            );
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        requestBody.put("vaFld", sharingRule);
        requestBody.put("sysSource", "OPT");
        requestBody.put("vaMchntNo", chinaumsPayProperties.getMid());
        requestBody.put("vaTermNo", chinaumsPayProperties.getTid());
        requestBody.put("billNo", orderSubDetail.getSopOrderNo());
        requestBody.put("billDate", orderSubDetail.getBillDate());
        requestBody.put("totalAmount", orderSubDetail.getOrderMoney().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).intValue());
        requestBody.put("billDesc", orderSubDetail.getWorksName());
        requestBody.put("notifyUrl", chinaumsPayProperties.getNotifyUrl());
        String pay = chinaumsCommonService.createRequest(JSONUtil.toJsonStr(requestBody), url);
        JSONObject jsonObject = JSONUtil.parseObj(pay);
        Map<String, String> map = new HashMap<>();
        map.put("payUrl", jsonObject.getStr("billQRCode"));
        map.put("orderNo", orderSubDetail.getOrderNo());
        return AjaxResult.success(map);
    }

    /***
     * 查询订单是否支付
     */
    @Override
    public JSONObject queryOrder(OrderSubDetail orderSubDetail) {
        String url = chinaumsPayProperties.getCtbBaseUrl() + "/v1/inip/upsp/shop/csb/query";
        Map<String, Object> requestBody = new HashMap<>();
        ObjectMapper mapper = new ObjectMapper();
        String jsonStr = JSONUtil.toJsonStr(orderSubDetail.getSharingRule());
        ChinaumsSharingRuleVO sharingRule = null;
        try {
            sharingRule = mapper.readValue(
                    jsonStr,
                    ChinaumsSharingRuleVO.class
            );
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        requestBody.put("vaFld", sharingRule);
        requestBody.put("sysSource", "OPT");
        requestBody.put("vaMchntNo", chinaumsPayProperties.getMid());
        requestBody.put("vaTermNo", chinaumsPayProperties.getTid());
        requestBody.put("billNo", orderSubDetail.getSopOrderNo());
        requestBody.put("billDate", orderSubDetail.getBillDate());
        String result = chinaumsCommonService.createRequest(JSONUtil.toJsonStr(requestBody), url);
        JSONObject jsonObject = JSONUtil.parseObj(result);
        return jsonObject;
    }

    /***
     * 查询网关订单是否支付
     */
    @Override
    public JSONObject queryGatewayOrder(OrderSubDetail orderSubDetail) {
        String vaMchntNo=chinaumsPayProperties.getMid();
        String vaTermNo=chinaumsPayProperties.getTid();
//        if (orderSubDetail.getTradeType().equals(OrderConstants.ORDER_TYPE_MARKET)){
//            Union union = baseMapper.selectOne(Wrappers.<Union>lambdaQuery().eq(Union::getCreateStaff, orderSubDetail.getSellerId()));
//            vaMchntNo=union.getMerNo();
//            vaTermNo=union.getTermNo();
//        }
        String url = chinaumsPayProperties.getGatewayBaseUrl() + "/shop/online/query";
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("mchntOrderId", orderSubDetail.getSopOrderNo());
        requestBody.put("vaMchntNo", vaMchntNo);
        requestBody.put("vaTermNo", vaTermNo);
        ObjectMapper mapper = new ObjectMapper();
        String jsonStr = JSONUtil.toJsonStr(orderSubDetail.getSharingRule());
        ChinaumsSharingRuleVO sharingRule = null;
        try {
            sharingRule = mapper.readValue(
                    jsonStr,
                    ChinaumsSharingRuleVO.class
            );
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        requestBody.put("vaFld", sharingRule);
        String result = chinaumsCommonService.createRequest(JSONUtil.toJsonStr(requestBody), url);
        JSONObject jsonObject = JSONUtil.parseObj(result);
        return jsonObject;
    }

    @Override
    public JSONObject terminalsQry(Union union) {
        String jsonData = merchantSignBySelfService.terminalsQry(union.getMerNo());
        JSONObject jsonObject = JSONUtil.parseObj(jsonData);
        log.info("银联入网状态查询：{}", jsonData);
        String resCode = jsonObject.getStr("res_code");
        if (resCode.equals("0000")){
//            JSONArray terminals = jsonObject.getJSONArray("terminals");
            return jsonObject;
        }
        return null;
    }

}
