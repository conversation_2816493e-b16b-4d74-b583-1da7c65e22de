package com.ruoyi.scwt.pay.entify;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @date 2021-10-12 14:27:33
 */
@Data
@TableName("order_info")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "")
public class Order extends Model<Order> {

    private static final long serialVersionUID = 1L;

    @TableId(type= IdType.AUTO)
    @ApiModelProperty(value="")
    private Long orderId;
    @ApiModelProperty(value="订单号")
    private String orderNo;
    @ApiModelProperty(value="订单金额")
    private BigDecimal orderMoney;
    @ApiModelProperty(value = "支付方式")
    private String payType;
    @ApiModelProperty(value="交易类型")
    private String tradeType;
    @ApiModelProperty(value="交易渠道")
    private String payChannel;

    @ApiModelProperty(value="")
    private LocalDateTime createDate;
    @ApiModelProperty(value="")
    private String createStaff;
    @ApiModelProperty(value="")
    private LocalDateTime updateDate;
    @ApiModelProperty(value="")
    private String updateStaff;
    @ApiModelProperty(value="")
    private String statusCd;
    @ApiModelProperty(value="")
    private LocalDateTime statusDate;
    @ApiModelProperty(value="")
    private String remark;
}
