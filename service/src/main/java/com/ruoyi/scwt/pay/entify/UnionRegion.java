package com.ruoyi.scwt.pay.entify;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 
 *
 * <AUTHOR>
 * @date 2021-10-12 14:27:33
 */
@Data
@TableName("union_region")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "")
public class UnionRegion extends Model<UnionRegion> {

    private static final long serialVersionUID = 1L;

//    @TableId(type= IdType.AUTO)
    @ApiModelProperty(value="区域code")
    private String code;
    @ApiModelProperty(value="名称")
    private String name;
    @ApiModelProperty(value="父级code")
    private String parCode;


}
