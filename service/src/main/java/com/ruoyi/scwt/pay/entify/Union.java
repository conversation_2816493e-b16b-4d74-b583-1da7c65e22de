package com.ruoyi.scwt.pay.entify;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ruoyi.scwt.sop.dto.ChinaumsMerchantBnfDto;
import com.ruoyi.scwt.sop.dto.ChinaumsMerchantPicDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.poi.hpsf.Decimal;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @date 2021-10-12 14:27:33
 */
@Data
@TableName("union_info")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "")
public class Union extends Model<Union> {

    private static final long serialVersionUID = 1L;

    @TableId(type= IdType.AUTO)
    @ApiModelProperty(value="", hidden = true)
    private Long id;
    @ApiModelProperty(value="", hidden = true)
    private Long shopId;
    @ApiModelProperty(value="流水号", hidden = true)
    private String requestSeq;
    @ApiModelProperty(value="注册类型")
    @NotNull(message = "注册类型不能为空")
    private String regMerType;
    @ApiModelProperty(value="法人身份证姓名")
    private String legalName;
    @ApiModelProperty(value="法人身份证号")
    private String legalIdcardNo;
    @ApiModelProperty(value="法人手机号")
    private String legalMobile;
    @ApiModelProperty(value = "法人证件开始日期(yyyy-MM-dd)", required = true)
    @NotBlank(message = "法人证件开始日期不能为空")
    @Length(max = 10, message = "法人证件开始日期长度不能超过10")
    private String legalCardBeginDate;
    @ApiModelProperty(value="证件截止日期")
    private String legalCardDeadline;
    @ApiModelProperty(value = "base64格式的人脸图片(图片小于30K)", required = true)
//    @NotBlank(message = "base64格式的人脸图片")
    private String faceImgBase64;
    @ApiModelProperty(value="性别")
    private String legalSex;
    @ApiModelProperty(value="法人职业")
    private String legalOccupation;
    @ApiModelProperty(value="商户营业名称")
    private String shopName;
    @ApiModelProperty(value="开户行行号")
    private String bankNo;
    @ApiModelProperty(value="账户类型")
    private String bankAcctType;
    @ApiModelProperty(value="开户行账号")
    private String bankAcctNo;
    @ApiModelProperty(value="开户账号名称")
    private String bankAcctName;
    @ApiModelProperty(value = "营业省份id(接口查询返回)", required = true)
    @NotBlank(message = "营业省份id不能为空")
    @Length(max = 2, message = "营业省份id长度不能超过2")
    private String shopProvinceId;

    @ApiModelProperty(value = "营业市id(接口查询返回)", required = true)
    @NotBlank(message = "营业市id不能为空")
    @Length(max = 6, message = "营业市id长度不能超过6")
    private String shopCityId;

    @ApiModelProperty(value = "营业区id(接口查询返回)", required = true)
    @NotBlank(message = "营业区id不能为空")
    @Length(max = 6, message = "营业区id长度不能超过6")
    private String shopCountryId;
    @ApiModelProperty(value="行业类别编码")
    private String mccCode;
    @ApiModelProperty(value="开通业务Id")
    private String productId;
    @ApiModelProperty(value="是否有营业场所")
    private String havingFixedBusiAddr;
    @ApiModelProperty(value="自助签约平台流水号")
    private String umsRegId;
    @ApiModelProperty(value="前端接入唯一标识")
    private String accesserAcct;
    @ApiModelProperty(value="申请状态")
    private String applyStatus;
    @ApiModelProperty(value="申请状态对应的描述信息")
    private String applyStatusMsg;
    @ApiModelProperty(value="商户号")
    private String merNo;
    @ApiModelProperty(value="终端号")
    private String termNo;
    @ApiModelProperty(value="代付商户号")
    private String withdrawalMerNo;
    @ApiModelProperty(value="代付终端号")
    private String withdrawalTermNo;
    @ApiModelProperty(value="费率")
    private BigDecimal rate;
    @ApiModelProperty(value="营业地址补充信息")
    private String shopAddrExt;
    @ApiModelProperty(value="所属支行地址")
    private String bankBranchAddress;
    @ApiModelProperty(value="所属支行名称")
    private String bankBranchName;
    @ApiModelProperty(value="商户地址")
    private String shopAddress;
    @ApiModelProperty(value="支行地址编号")
    private String bankBranchAddressCode;
    @ApiModelProperty(value="协议退回原因")
    private String failReason;
    @ApiModelProperty(value="签约状态")
    private String agreementSign;
//    @ApiModelProperty(value="股东信息")
//    private String shareholderJson;
//    @ApiModelProperty(value="受益人信息",hidden = true)
//    private String bnfJson;
//    @ApiModelProperty(value="上传图片列表",hidden = true)
//    private String picJson;
    @ApiModelProperty(value="社会信用统一代码")
    private String shopLic;

    @ApiModelProperty(value = "控股股东姓名(商户类型为非小微，且控股股东非法人时必填)")
    @Length(max = 20, message = "控股股东姓名长度不能超过20")
    private String shareholderName;

    @ApiModelProperty(value = "控股股东证件号(商户类型为非小微，且控股股东非法人时必填)")
    @Length(max = 20, message = "控股股东证件号长度不能超过20")
    private String shareholderCertno;

    @ApiModelProperty(value = "控股股东证件有效期(商户类型为非小微，且控股股东非法人时必填 yyyy-MM-dd)")
    @Length(max = 10, message = "控股股东证件有效期长度不能超过10")
    private String shareholderCertExpire;

    @ApiModelProperty(value = "控股股东证件开始日期(商户类型为非小微，且控股股东非法人时必填 yyyy-MM-dd)")
    @Length(max = 10, message = "控股股东证件有效期长度不能超过10")
    private String shareholderCertBeginDate;

    @ApiModelProperty(value = "控股股东证件证件类型(不填默认为身份证(1):1、身份证2、护照3、军官证4、警官证5、士兵证6、台湾居民来往大陆通行证7、回乡证8、港澳居民来往内地通行证10、港澳台居民居住证11、营业执照12、组织机构代码证13、税务登记证14、商业登记证15、民办非企业登记证书16、批文证明)")
    @Length(max = 3, message = "控股股东证件证件类型长度不能超过3")
    private String shareholderCertType;

    @ApiModelProperty(value = "控股股东家庭地址(商户类型为非小微，且控股股东非法人时必填)")
    @Length(max = 128, message = "控股股东家庭地址长度不能超过128")
    private String shareholderHomeAddr;
    @ApiModelProperty(value="法人家庭地址")
    private String legalmanHomeAddr;
    @ApiModelProperty(value="发送验证请求状态")
    private String verifyStatus;
    @ApiModelProperty(value="请求结果json信息")
    private String result;
    @ApiModelProperty(value="协议上传结果json信息")
    private String agreementResult;

    @ApiModelProperty(value = "是否连锁商户(0是，1否)")
    @NotNull(message = "是否连锁商户不能为空")
    private String isChain;

    @ApiModelProperty(value="")
    private LocalDateTime createDate;
    @ApiModelProperty(value="")
    private String createStaff;
    @ApiModelProperty(value="")
    private LocalDateTime updateDate;
    @ApiModelProperty(value="")
    private String updateStaff;
    @ApiModelProperty(value="")
    private String statusCd;
    @ApiModelProperty(value="")
    private LocalDateTime statusDate;
    @ApiModelProperty(value="")
    private String remark;


    @TableField(exist = false)
    @ApiModelProperty(value="受益人信息")
    private List<ChinaumsMerchantBnfDto> bnfList;

    @TableField(exist = false)
    @ApiModelProperty(value = "上传图片列表(必填)")
    private List<ChinaumsMerchantPicDto> picList;

    @ApiModelProperty(value = "上传图片列表地址")
    private String picPathStr;

    @TableField(exist = false)
    @ApiModelProperty(value="是否暂存")
    private Boolean isTemp;

    @TableField(exist = false)
    @ApiModelProperty(value="营业地址")
    private String shopAddr;

}
