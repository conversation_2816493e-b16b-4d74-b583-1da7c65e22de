package com.ruoyi.scwt.pay.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.util.StringUtil;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.scwt.asset.constant.AssetConstants;
import com.ruoyi.scwt.asset.entity.Asset;
import com.ruoyi.scwt.asset.entity.AssetZoneShop;
import com.ruoyi.scwt.asset.entity.PurchasedAsset;
import com.ruoyi.scwt.asset.mapper.AssetMapper;
import com.ruoyi.scwt.asset.mapper.PurchasedAssetMapper;
import com.ruoyi.scwt.asset.service.AssetZoneShopService;
import com.ruoyi.scwt.common.controller.SmsController;
import com.ruoyi.scwt.pay.AliPayClient;
import com.ruoyi.scwt.pay.WxPayClient;
import com.ruoyi.scwt.pay.constant.OrderConstants;
import com.ruoyi.scwt.pay.dto.OrderDto;
import com.ruoyi.scwt.pay.entify.MerchantWalletDetail;
import com.ruoyi.scwt.pay.entify.Order;
import com.ruoyi.scwt.pay.entify.OrderSubDetail;
import com.ruoyi.scwt.pay.entify.Union;
import com.ruoyi.scwt.pay.mapper.OrderMapper;
import com.ruoyi.scwt.pay.mapper.OrderSubDetailMapper;
import com.ruoyi.scwt.pay.service.MerchantWalletDetailService;
import com.ruoyi.scwt.pay.service.OrderService;
import com.ruoyi.scwt.pay.service.UnionService;
import com.ruoyi.scwt.shop.mapper.ShopInfoMapper;
import com.ruoyi.scwt.works.constant.WorksConstants;
import com.ruoyi.scwt.works.entity.WorksEvidence;
import com.ruoyi.scwt.works.entity.WorksRegister;
import com.ruoyi.scwt.works.mapper.WorksEvidenceMapper;
import com.ruoyi.scwt.works.mapper.WorksRegisterMapper;
import com.ruoyi.sop.dto.AgentPay;
import com.ruoyi.sop.dto.ChinaumsPayProperties;
import com.ruoyi.sop.service.ChinaumsCommonService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements OrderService {

    private final OrderSubDetailMapper orderSubDetailMapper;
    private final AssetMapper assetMapper;

    private final WorksEvidenceMapper worksEvidenceMapper;
    private final WorksRegisterMapper worksRegisterMapper;
    private final UnionService unionService;
    private final PurchasedAssetMapper purchasedAssetMapper;
    private final SmsController smsController;
    private final ChinaumsCommonService chinaumsCommonService;
    private final MerchantWalletDetailService merchantWalletDetailService;
    private final ChinaumsPayProperties chinaumsPayProperties;
    private final WxPayClient wxPayClient;
    private final AliPayClient aliPayClient;
    private final AssetZoneShopService assetZoneShopService;
    private final ShopInfoMapper shopInfoMapper;

    @Override
    public String createOrderNo(String payType, String payChannel, Long userId) {
        String orderNo = "";
        switch (payType) {
            case OrderConstants.ORDER_TYPE_EVIDENCE:
                orderNo = "E";
                break;
            case OrderConstants.ORDER_TYPE_REGISTER:
                orderNo = "R";
                break;
            case OrderConstants.ORDER_TYPE_MARKET:
                orderNo = "M";
                break;
        }
        switch (payChannel) {
            case OrderConstants.PAY_TYPE_ALIPAY:
                orderNo += "A";
                break;
            case OrderConstants.PAY_TYPE_WECHAT:
                orderNo += "W";
                break;
            case OrderConstants.PAY_TYPE_UNIONPAY:
                orderNo += "U";
                break;
        }
        String time = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String traceId = UUID.randomUUID().toString().substring(0, 5).toUpperCase();
        return orderNo + userId + time + traceId;
    }

    @Override
    public OrderSubDetail checkReplaceOrder(Long userId, OrderDto dto) {
        if (ObjectUtil.isEmpty(dto.getWorksId()) && ObjectUtil.isEmpty(dto.getAssetId())) {
            throw new RuntimeException("作品ID和资产ID不能同时为空");
        }
        OrderSubDetail orderSubDetail = orderSubDetailMapper.selectOne(Wrappers.<OrderSubDetail>lambdaQuery()
                .eq(StringUtils.isNotEmpty(dto.getOrderNo()), OrderSubDetail::getOrderNo, dto.getOrderNo())
                .eq(OrderSubDetail::getBuyerId, userId)
//                .eq(OrderSubDetail::getPayChannel, dto.getPayChannel())
                .eq(OrderSubDetail::getTradeType, dto.getTradeType())
                .ne(OrderSubDetail::getStatusCd, OrderConstants.ORDER_STATUS_CLOSE)
                .and(q -> q.eq(OrderSubDetail::getWorksId, dto.getWorksId()).or()
                        .eq(OrderSubDetail::getAssetId, dto.getAssetId())));
        if (ObjectUtil.isNotEmpty(orderSubDetail)) {
            if (orderSubDetail.getStatusCd().equals(OrderConstants.ORDER_STATUS_UNPAID)) {
                Boolean aBoolean = isOrderSuccess(orderSubDetail, userId);
                if (aBoolean) {
                    orderSubDetail.setStatusCd(OrderConstants.ORDER_STATUS_PAID);
                    return orderSubDetail;
                }
                if (orderSubDetail.getTradeType().equals(OrderConstants.ORDER_TYPE_MARKET)) {
                    Asset asset = assetMapper.selectById(dto.getAssetId());
                    if (!asset.getStatusCd().equals(AssetConstants.ASSET_ON_SHELF)) {
                        orderSubDetail.setStatusDate(LocalDateTime.now());
                        orderSubDetail.setStatusCd(OrderConstants.ORDER_STATUS_CLOSE);
                        orderSubDetailMapper.updateById(orderSubDetail);
                        throw new RuntimeException("资产已下架，不可交易！");
                    }

                }
            } else if (orderSubDetail.getStatusCd().equals(OrderConstants.ORDER_STATUS_PAID)) {
                orderSubDetail.setStatusCd(OrderConstants.ORDER_STATUS_PAID);
                return orderSubDetail;
            }
            if (!orderSubDetail.getPayChannel().equals(dto.getPayChannel())) {
                throw new RuntimeException("请勿切换支付方式！");
            }
        }
        return orderSubDetail;
    }


    @Override
    public Boolean isOrderSuccess(OrderSubDetail orderSubDetail, Long userId) {
        Boolean aBoolean = false;
        switch (orderSubDetail.getPayChannel()) {
            case OrderConstants.PAY_TYPE_ALIPAY:
//                    aliPayClient.selectOrder(orderSubDetail);
                break;
            case OrderConstants.PAY_TYPE_WECHAT:
                String data = wxPayClient.selectOrder(orderSubDetail.getOrderNo() + "_" + orderSubDetail.getSubOrderId());
                JSONObject result = JSONUtil.parseObj(data);
                if (result.getStr("trade_state").equals("SUCCESS")) {
                    success(orderSubDetail);
                    aBoolean = true;
                }
                break;
            case OrderConstants.PAY_TYPE_UNIONPAY:
                aBoolean = queryOrder(orderSubDetail);
                if (!aBoolean) {
                    orderSubDetail.setSopOrderNo("14BF" + unionService.createSeq(userId));
                    orderSubDetail.setBillDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    orderSubDetailMapper.updateById(orderSubDetail);
                }
                break;
            case OrderConstants.PAY_TYPE_UNIONGATEWAYPAY:
                aBoolean = queryGatewayOrder(orderSubDetail);
                if (!aBoolean) {
                    orderSubDetail.setSopOrderNo("14BF" + unionService.createSeq(userId));
                    orderSubDetailMapper.updateById(orderSubDetail);
                }
                break;
            case OrderConstants.PAY_TYPE_DYNAMIC_PAY:
                aBoolean = false;
                break;
            case OrderConstants.PAY_TYPE_PUBLIC_PAY:
                aBoolean = false;
                break;
            default:
                throw new RuntimeException("支付渠道错误");
        }
//        if (aBoolean) {
//            success(orderSubDetail);
//            throw new RuntimeException("订单已支付！");
//        }
        return aBoolean;
    }

    @Override
    public OrderSubDetail createOrder(Long userId, OrderDto dto) {
        Order order = new Order();
        BeanUtil.copyProperties(dto, order);
        String orderNo = createOrderNo(dto.getTradeType(), dto.getPayChannel(), userId);
        order.setOrderNo(orderNo);
        order.setCreateStaff(String.valueOf(userId));
        order.setCreateDate(LocalDateTime.now());
        order.setStatusCd(OrderConstants.ORDER_STATUS_UNPAID);
        order.setStatusDate(LocalDateTime.now());
        OrderSubDetail orderSubDetail = new OrderSubDetail();
        BeanUtil.copyProperties(order, orderSubDetail);
        orderSubDetail.setBuyerId(userId);
        Asset asset = null;
        String sellerId = null;
        switch (dto.getTradeType()) {
            case OrderConstants.ORDER_TYPE_EVIDENCE:
                if (ObjectUtil.isEmpty(dto.getWorksId())) {
                    throw new RuntimeException("作品ID不能为空");
                }
                WorksEvidence worksEvidence = worksEvidenceMapper.selectById(dto.getWorksId());
                orderSubDetail.setWorksName(worksEvidence.getWorksName());
                orderSubDetail.setWorksId(dto.getWorksId());
                order.setOrderMoney(new BigDecimal("0.01"));
                orderSubDetail.setOrderMoney(new BigDecimal("0.01"));
                break;
            case OrderConstants.ORDER_TYPE_REGISTER:
                if (ObjectUtil.isEmpty(dto.getWorksId())) {
                    throw new RuntimeException("作品ID不能为空");
                }
                WorksRegister worksRegister = worksRegisterMapper.selectById(dto.getWorksId());
                orderSubDetail.setWorksId(dto.getWorksId());
                orderSubDetail.setWorksName(worksRegister.getWorksName());
                order.setOrderMoney(new BigDecimal("0.01"));
                orderSubDetail.setOrderMoney(new BigDecimal("0.01"));
                break;
            case OrderConstants.ORDER_TYPE_MARKET:
                if (ObjectUtil.isEmpty(dto.getAssetId())) {
                    throw new RuntimeException("资产ID不能为空");
                }
                asset = assetMapper.selectById(dto.getAssetId());
                AssetZoneShop byId = assetZoneShopService.getById(dto.getAssetId());
                sellerId = shopInfoMapper.selectById(byId.getShopId()).getCreateStaff();
                orderSubDetail.setAssetId(dto.getAssetId());
                orderSubDetail.setWorksName(asset.getAssetName());
                orderSubDetail.setSellerId(Long.valueOf(asset.getCreateStaff()));
                orderSubDetail.setTransactionType(dto.getTransactionType());
                orderSubDetail.setRights(asset.getRights());
                orderSubDetail.setDays(asset.getDays());
                orderSubDetail.setRightRemark(asset.getRightRemark());
                orderSubDetail.setAuthorizationType(asset.getAuthorizationType());
                orderSubDetail.setAuthorizationArea(asset.getAuthorizationArea());
                switch (dto.getTransactionType()) {
                    case OrderConstants.MARKET_TYPE_PERSON:
                        order.setOrderMoney(asset.getPersonalPrice());
                        orderSubDetail.setOrderMoney(asset.getPersonalPrice());
                        break;
                    case OrderConstants.MARKET_TYPE_ENTERPRISE:
                        order.setOrderMoney(asset.getEnterprisePrice());
                        orderSubDetail.setOrderMoney(asset.getEnterprisePrice());
                        break;
                    case OrderConstants.MARKET_TYPE_TRANSFER:
                        order.setOrderMoney(asset.getTransferPrice());
                        orderSubDetail.setOrderMoney(asset.getTransferPrice());
                        break;
                    case OrderConstants.MARKET_TYPE_DYNAMIC:
                        order.setOrderMoney(BigDecimal.valueOf(0));
                        orderSubDetail.setOrderMoney(BigDecimal.valueOf(0));
                        order.setStatusCd(OrderConstants.ORDER_STATUS_WAIT);
                        orderSubDetail.setStatusCd(OrderConstants.ORDER_STATUS_WAIT);
                        orderSubDetail.setTransactionPrice(asset.getTransactionPrice());
                        orderSubDetail.setAgreementDoc(asset.getAgreementDoc());
                        break;
                    case OrderConstants.MARKET_TYPE_PUBLIC:
                        order.setOrderMoney(BigDecimal.valueOf(0));
                        orderSubDetail.setOrderMoney(BigDecimal.valueOf(0));
                        order.setStatusCd(OrderConstants.ORDER_STATUS_WAIT);
                        orderSubDetail.setStatusCd(OrderConstants.ORDER_STATUS_WAIT);
                        break;
                    default:
                        throw new RuntimeException("市场交易类型错误");
                }
                break;
            default:
                throw new RuntimeException("交易类型错误");
        }
        if (dto.getPayChannel().equals(OrderConstants.PAY_TYPE_UNIONGATEWAYPAY) || dto.getPayChannel().equals(OrderConstants.PAY_TYPE_UNIONPAY)) {
            JSONObject vaFld = setProfitSharing(orderSubDetail.getTradeType(), orderSubDetail.getOrderMoney(), orderSubDetail.getWorksName(), sellerId);
            orderSubDetail.setSopOrderNo("14BF" + unionService.createSeq(userId));
//            if (orderSubDetail.getPayChannel().equals(OrderConstants.PAY_TYPE_UNIONGATEWAYPAY)) {
//                orderSubDetail.setSopOrderNo("14BF" + unionService.createSeq(userId));
//            } else {
//                orderSubDetail.setSopOrderNo(unionService.createSeq(userId));
//            }
            orderSubDetail.setBillDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            orderSubDetail.setSharingRule(JSONUtil.toJsonStr(vaFld));
        }
        baseMapper.insert(order);
        orderSubDetailMapper.insert(orderSubDetail);
        return orderSubDetail;
    }

    /***
     * 设置分账规则
     * @param
     */
    @Override
    public JSONObject setProfitSharing(String tradeType, BigDecimal orderMoney, String orderDesc, String sellerId) {
        String merId = "";
        BigDecimal rate = BigDecimal.ZERO;
        switch (tradeType) {
            case OrderConstants.ORDER_TYPE_EVIDENCE:
                merId = chinaumsPayProperties.getSubmid();
                break;
            case OrderConstants.ORDER_TYPE_REGISTER:
                break;
            case OrderConstants.ORDER_TYPE_MARKET:
            case OrderConstants.ORDER_TYPE_ISSUE:
                merId = chinaumsPayProperties.getSubmid();
                if (ObjectUtil.isNotEmpty(sellerId) && !sellerId.equals("null")) {
                    Union union = unionService.getOne(Wrappers.<Union>lambdaQuery().eq(Union::getCreateStaff, sellerId));
                    if (ObjectUtil.isEmpty(union) || StrUtil.isEmpty(union.getMerNo())) {
                        throw new RuntimeException("商户未开通结算认证，不可交易");
                    } else {
                        rate = union.getRate();
                        merId = union.getMerNo();
                    }
                }
                break;
            default:
                throw new RuntimeException("交易类型错误");
        }
        //平台分账金额
        int totalAmount = orderMoney.multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).intValue();
        int platAmt = rate.multiply(new BigDecimal(totalAmount)).setScale(0, RoundingMode.HALF_UP).intValue();
        JSONObject vaFld = new JSONObject();
        vaFld.put("newShopFlag", "GWCXS");
        vaFld.put("isSubMchntPay", "01");
        vaFld.put("satType", "01");
        vaFld.put("platAmt", platAmt);
        List<JSONObject> list = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("merId", merId);
        jsonObject.put("satAmt", totalAmount - platAmt);
        jsonObject.put("orderDesc", orderDesc);
        list.add(jsonObject);
        vaFld.put("satInfo", list);
        return vaFld;
    }

    @Override
    public void success(OrderSubDetail orderSubDetail) {
        orderSubDetail.setStatusCd(OrderConstants.ORDER_STATUS_PAID);
        orderSubDetail.setStatusDate(LocalDateTime.now());
        orderSubDetail.setUpdateDate(LocalDateTime.now());
        orderSubDetail.setUpdateStaff(orderSubDetail.getCreateStaff());
        orderSubDetailMapper.updateById(orderSubDetail);
        Order order = baseMapper.selectOne(Wrappers.<Order>lambdaQuery().eq(Order::getOrderNo, orderSubDetail.getOrderNo()));
        order.setStatusCd(OrderConstants.ORDER_STATUS_PAID);
        order.setStatusDate(orderSubDetail.getStatusDate());
        order.setUpdateDate(orderSubDetail.getUpdateDate());
        order.setUpdateStaff(orderSubDetail.getCreateStaff());
        baseMapper.updateById(order);
        log.info("订单支付成功，订单号：{}", orderSubDetail.getOrderNo() + "_" + orderSubDetail.getSubOrderId());
        switch (orderSubDetail.getTradeType()) {
            case OrderConstants.ORDER_TYPE_EVIDENCE:
                worksEvidenceMapper.update(null, Wrappers.<WorksEvidence>lambdaUpdate()
                        .eq(WorksEvidence::getWorksId, orderSubDetail.getWorksId())
                        .set(WorksEvidence::getStatusCd, WorksConstants.WORKS_EVIDENCE)
                        .set(WorksEvidence::getStatusDate, LocalDateTime.now()));
                break;
            case OrderConstants.ORDER_TYPE_REGISTER:
                worksRegisterMapper.update(null, Wrappers.<WorksRegister>lambdaUpdate()
                        .eq(WorksRegister::getWorksId, orderSubDetail.getWorksId())
                        .set(WorksRegister::getStatusCd, WorksConstants.WORKS_REVIEW)
                        .set(WorksRegister::getStatusDate, LocalDateTime.now()));
                break;
            case OrderConstants.ORDER_TYPE_MARKET:
                PurchasedAsset purchasedAsset = new PurchasedAsset();
                purchasedAsset.setAssetId(orderSubDetail.getAssetId());
                purchasedAsset.setUserId(orderSubDetail.getBuyerId());
                purchasedAsset.setTransactionType(orderSubDetail.getTransactionType());
                purchasedAsset.setRights(orderSubDetail.getRights());
                purchasedAsset.setDays(orderSubDetail.getDays());
                purchasedAssetMapper.insert(purchasedAsset);
                smsController.sendTransactionSms(orderSubDetail.getSellerId(), orderSubDetail.getWorksName(), orderSubDetail.getOrderMoney().toString());
                break;
        }
    }

    @Override
    public Boolean queryOrder(OrderSubDetail orderSubDetail) {
        JSONObject jsonObject = unionService.queryOrder(orderSubDetail);
        if (jsonObject.getStr("billStatus").equals("PAID")) {
            success(orderSubDetail);
            return true;
        } else {
            return false;
        }
    }

    @Override
    public Boolean queryGatewayOrder(OrderSubDetail orderSubDetail) {
        JSONObject jsonObject = unionService.queryGatewayOrder(orderSubDetail);
        if (StringUtils.isNotEmpty(jsonObject.getStr("status")) && jsonObject.getStr("status").equals("TRADE_SUCCESS")) {
            success(orderSubDetail);
            return true;
        } else {
            return false;
        }
    }

    @Override
    public Object queryWithdrawBalance(String merNo) {
        return chinaumsCommonService.queryWithdrawBalance(merNo);
    }

    @Override
    public R processWithdraw(AgentPay agentPay) {
        Union one = unionService.getOne(Wrappers.<Union>lambdaQuery().eq(Union::getCreateStaff, SecurityUtils.getUserId()));
        if (ObjectUtil.isEmpty(one) || StringUtil.isEmpty(one.getWithdrawalMerNo())) {
            return R.fail("商户未开通结算");
        }
        OrderSubDetail orderSubDetail = orderSubDetailMapper.selectOne(Wrappers.<OrderSubDetail>lambdaQuery().eq(OrderSubDetail::getOrderNo, agentPay.getOrderNo()));
        if (ObjectUtil.isEmpty(orderSubDetail)) {
            return R.fail("订单不存在");
        } else if (!orderSubDetail.getStatusCd().equals(OrderConstants.ORDER_STATUS_PAID)) {
            return R.fail("订单未支付");
        }
        if (!StringUtils.equalsAny(orderSubDetail.getPayChannel(), OrderConstants.PAY_TYPE_UNIONPAY, OrderConstants.PAY_TYPE_UNIONGATEWAYPAY)) {
            return R.fail("该支付方式不支持银联提现");
        }
        agentPay.setWithdrawAmt(String.valueOf(orderSubDetail.getOrderMoney().multiply(BigDecimal.valueOf(100)).intValue()));
        String withdrawBalance = chinaumsCommonService.queryWithdrawBalance(one.getWithdrawalMerNo());
        JSONObject jsonObject = JSONUtil.parseObj(withdrawBalance);
        if (new BigDecimal(jsonObject.getStr("tzWithdrawAmt")).compareTo(new BigDecimal(agentPay.getWithdrawAmt())) < 0) {
            return R.fail("提现金额超出可提现金额");
        }
        agentPay.setMerchantNo(one.getWithdrawalMerNo());
        String withdraw = chinaumsCommonService.processWithdraw(agentPay);
        try {
            // 创建 ObjectMapper 实例
            ObjectMapper objectMapper = new ObjectMapper();

            // 将 JSON 字符串转换为 Map 对象
            Map<String, Object> withdrawMap = objectMapper.readValue(withdraw, Map.class);

            // 获取 respCode 参数
            String respCode = (String) withdrawMap.get("respCode");
            log.info("银联提现结果：" + withdraw);
            if ("000000".equals(respCode)) {
                BigDecimal bigDecimal = new BigDecimal(agentPay.getWithdrawAmt()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                merchantWalletDetailService.save(agentPay.getOrderNo(), bigDecimal, "withdraw", "提现", agentPay.getMerchantNo(), withdrawBalance, withdraw);
                return R.ok(withdraw);
            }
            return R.fail(withdraw);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public R qurProcessWithdraw(AgentPay agentPay) {
        MerchantWalletDetail merchantWalletDetail = merchantWalletDetailService.getOne(Wrappers.<MerchantWalletDetail>lambdaQuery().eq(MerchantWalletDetail::getRecordNo, agentPay.getOrderNo()));
        agentPay.setMerchantNo(merchantWalletDetail.getMerchantNo());
        LocalDateTime createDate = merchantWalletDetail.getCreateDate();
        //将createDate转换为yyyyMMDD格式的字符串
        String transDate = createDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        agentPay.setTransDate(transDate);
        String data = chinaumsCommonService.qurProcessWithdraw(agentPay);
        log.info("查询提现结果：" + data);
        JSONObject jsonObject = JSONUtil.parseObj(data);
        String respCode = (String) jsonObject.get("respCode");
        if ("000000".equals(respCode)) {
            String status = jsonObject.getStr("status");
            merchantWalletDetailService.update(null, Wrappers.<MerchantWalletDetail>lambdaUpdate()
                    .eq(MerchantWalletDetail::getRecordNo, agentPay.getOrderNo())
                    .set(MerchantWalletDetail::getStatusCd, status)
                    .set(MerchantWalletDetail::getQurStatusData, data));
        }
        return R.ok(data);
    }
}
