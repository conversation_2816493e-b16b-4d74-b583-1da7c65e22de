package com.ruoyi.scwt.pay.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @date 2021-10-12 14:27:33
 */
@Data
public class OrderDto {



    @ApiModelProperty(value="")
    private Long orderId;
    @ApiModelProperty(value="订单号")
    private String orderNo;
//    @ApiModelProperty(value="订单金额")
//    @NotNull(message = "订单金额不能为空")
//    private BigDecimal orderMoney;
//    @ApiModelProperty(value = "支付方式")
//    private String payType;
    @ApiModelProperty(value="交易类型")
    @NotNull(message = "交易类型不能为空")
    private String tradeType;
    @ApiModelProperty(value="交易渠道")
    @NotNull(message = "交易渠道不能为空")
    private String payChannel;
    @ApiModelProperty(value="资产ID")
    private Long assetId;
    @ApiModelProperty(value="作品ID")
    private Long worksId;
    @ApiModelProperty(value = "市场交易类型（person:个人授权、enterprise:企业授权、transfer:转让）")
    private String transactionType;
    @ApiModelProperty(value="状态")
    private String statusCd;
    @ApiModelProperty(value="资产名称")
    private String name;
}
