package com.ruoyi.scwt.pay.vo;

import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class OrderVo {
    @Excel(name = "订单号", prompt = "订单号")
    @ApiModelProperty(value="订单号")
    private String orderNo;
    @Excel(name = "订单金额")
    @ApiModelProperty(value="订单金额")
    private BigDecimal orderMoney;
    @Excel(name = "交易类型", dictType = "orderTradeType")
    @ApiModelProperty(value="交易类型")
    private String tradeType;
    @Excel(name = "交易类型", dictType = "payType")
    @ApiModelProperty(value="交易渠道")
    private String payChannel;
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value="")
    private LocalDateTime createDate;
    @Excel(name = "资产")
    @ApiModelProperty(value="订单标题")
    private String name;
    @Excel(name = "状态", readConverterExp = "6100=已支付,6200=待支付,6900=已关闭")
    @ApiModelProperty(value="")
    private String statusCd;
    @ApiModelProperty(value="资产ID")
    private Long assetId;
    @ApiModelProperty(value="作品ID")
    private Long worksId;


//    @ApiModelProperty(value = "资产交易类型")
//    @Excel(name = "资产交易类型", readConverterExp = "6100=已支付,6200=待支付,6900=已关闭")
//    private String transactionType;
    @ApiModelProperty(value = "权利，多个以逗号隔开")
    private String rights;
    @ApiModelProperty(value = "期限（天）")
    private int days;
    @Excel(name = "买家名称")
    @ApiModelProperty(value = "买家名称")
    private String buyerName;
    @Excel(name = "卖家名称")
    @ApiModelProperty(value = "卖家名称")
    private String sellerName;
    @Excel(name = "卖家名称")
    @ApiModelProperty(value = "备注内容")
    private String rightRemark;
    @ApiModelProperty(value = "授权类型")
    private String authorizationType;
    @ApiModelProperty(value = "授权地区")
    private String authorizationArea;

    @ApiModelProperty(value = "交易价格")
    private String transactionPrice;
    @ApiModelProperty(value = "资产交易类型")
    private String transactionType;
    @ApiModelProperty(value = "协议文件")
    private String agreementDoc;
}
