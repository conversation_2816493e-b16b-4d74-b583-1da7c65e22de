package com.ruoyi.scwt.pay.entify;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

/**
 * 银联入网受益人
 * <AUTHOR>
 * @date 2022/11/18 - 11:11
 */
@Data
@ApiModel(value = "银联入网受益人")
@TableName("union_bnf")
public class UnionBnf {

    private static final long serialVersionUID = 1L;

    @TableId(type= IdType.AUTO)
    @ApiModelProperty(value="", hidden = true)
    private Long id;
    @ApiModelProperty(value="", hidden = true)
    private Long unionId;
    @ApiModelProperty(value = "受益人姓名(商户类型为非小微，且受益人非法人时必填)")
    @Length(max = 20,message = "受益人姓名长度不能超过20")
    private String bnfName;

    @ApiModelProperty(value = "受益人证件号(商户类型为非小微，且受益人非法人时必填)")
    @Length(max = 20,message = "受益人证件号长度不能超过20")
    private String bnfCertno;

    @ApiModelProperty(value = "受益人证件开始日期(商户类型为非小微，且受益人非法人时必填 yyyy-MM-dd)")
    @Length(max = 10,message = "受益人证件开始日期长度不能超过10")
    private String bnfCertBeginDate;

    @ApiModelProperty(value = "受益人证件有效期(商户类型为非小微，且受益人非法人时必填 yyyy-MM-dd)")
    @Length(max = 10,message = "受益人证件有效期长度不能超过10")
    private String bnfCertExpire;

        @ApiModelProperty(value = "受益人证件类型(商户类型为非小微，且受益人非法人时必填;不填默认为身份证;1、身份证2、护照3、军官证4、警官证5、士兵证6、台湾居民来往大陆通行证7、回乡证8、港澳居民来往内地通行证10、港澳台居民居住证11、营业执照12、组织机构代码证13、税务登记证14、商业登记证15、民办非企业登记证书16、批文证明)")
    @Length(max = 3,message = "受益人证件类型长度不能超过3")
    private String bnfCertType;

    @ApiModelProperty(value = "受益人家庭地址(商户类型为非小微，且受益人非法人时必填)")
    @Length(max = 60,message = "受益人家庭地址长度不能超过60")
    private String bnfHomeAddr;

    @ApiModelProperty(value = "法人家庭地址(商户类型为非小微，法人作为唯一受益人必填)")
    @Length(max = 60,message = "法人家庭地址长度不能超过60")
    private String legalmanHomeAddr;
}
