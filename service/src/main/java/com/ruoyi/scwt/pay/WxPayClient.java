package com.ruoyi.scwt.pay;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.domain.AjaxResult;
import com.wechat.pay.java.core.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.scwt.pay.constant.OrderConstants;
import com.ruoyi.scwt.pay.entify.Order;
import com.ruoyi.scwt.pay.entify.OrderSubDetail;
import com.ruoyi.scwt.pay.service.OrderService;
import com.ruoyi.scwt.pay.service.OrderSubDetailService;
import com.wechat.pay.java.core.Config;


import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.exception.ValidationException;
import com.wechat.pay.java.core.notification.NotificationConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.service.partnerpayments.nativepay.model.Transaction;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import com.wechat.pay.java.service.payments.nativepay.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Component
@Slf4j
public class WxPayClient {

    @Value("${wechat.pay.appid}")
    private String appid;
    @Value("${wechat.pay.merchantId}")
    private String merchantId;
    @Value("${wechat.pay.privateKeyPath}")
    private String privateKeyPath;
    @Value("${wechat.pay.merchantSerialNumber}")
    private String merchantSerialNumber;
    @Value("${wechat.pay.apiV3Key}")
    private String apiV3Key;
    @Value("${wechat.pay.notifyUrl}")
    private String notifyUrl;

    private static Config config;

    @Autowired
    private OrderSubDetailService orderSubDetailService;

    @Autowired
    @Lazy
    private OrderService orderService;

    @PostConstruct
    public void initConfig() {
        // 这里把Config作为配置Bean是为了避免多次创建资源，一般项目运行的时候这些东西都确定了
        // 具体的参数改为申请的数据，可以通过读配置文件的形式获取
        config = new RSAAutoCertificateConfig.Builder()
                .merchantId(merchantId)
                .privateKeyFromPath(privateKeyPath)
                .merchantSerialNumber(merchantSerialNumber)
                .apiV3Key(apiV3Key)
                .build();
    }

    public AjaxResult prePay(OrderSubDetail dto) {
        // 构建service
        NativePayService service = new NativePayService.Builder().config(config).build();
        // request.setXxx(val)设置所需参数，具体参数可见Request定义
        PrepayRequest request = new PrepayRequest();
        Amount amount = new Amount();
        amount.setTotal(dto.getOrderMoney().multiply(new BigDecimal(100)).intValue());
        request.setAmount(amount);
        request.setAppid(appid);
        request.setMchid(merchantId);
        request.setDescription(dto.getWorksName());
        request.setNotifyUrl(notifyUrl);
        request.setOutTradeNo(dto.getOrderNo() + "_" + dto.getSubOrderId());
        System.out.println("微信支付下单参数： "+ request);
        // 调用下单方法，得到应答
//        PrepayResponse response = service.prepay(request);
        try {
            // 调用微信支付SDK的预支付接口
            PrepayResponse response = service.prepay(request);
            // 使用微信扫描 code_url 对应的二维码，即可体验Native支付
//            System.out.println(response.getCodeUrl());
            return AjaxResult.success(response.getCodeUrl());
        } catch (ServiceException e) {
//            log.info(e.getMessage());
            // 1. 获取微信返回的错误信息
            String errorCode = e.getErrorCode();    // 例如 "INVALID_REQUEST"
            String errorMessage = e.getErrorMessage(); // 例如 "201 商户订单号重复"

            // 2. 处理订单号重复的特定情况
            if ("INVALID_REQUEST".equals(errorCode) && errorMessage.contains("商户订单号重复")) {
                // 生成新的唯一订单号（示例）
//                String newOutTradeNo = generateUniqueOrderNumber();

                // 返回给前端的友好提示
                return AjaxResult.error("订单号重复，请重新提交订单");
            }

            // 3. 其他错误处理
            log.error("微信支付失败 - 错误码:{} 信息:{}", errorCode, errorMessage);
            return AjaxResult.error("支付失败：" + errorMessage);

        }
//        return AjaxResult.error("调用失败");
    }

    /**
     * 微信支付回调
     *
     * @return 回调结果
     */

    public void wechat(RequestParam requestParam, HttpServletResponse response) throws IOException {
        try {
//                // 构造 RequestParam
//                RequestParam requestParam = new RequestParam.Builder()
//                        // 序列号
//                        .serialNumber(request.getHeader("Wechatpay-Serial"))
//                        // 随机数
//                        .nonce(request.getHeader("Wechatpay-Nonce"))
//                        // 签名
//                        .signature(request.getHeader("Wechatpay-Signature"))
//                        // 时间戳
//                        .timestamp(request.getHeader("Wechatpay-Timestamp"))
//                        .body(body)
//                        .build();
            // 初始化解析器
            NotificationParser parser = new NotificationParser((NotificationConfig) config);
            // 验签、解密并转换成 Transaction
            Transaction transaction = parser.parse(requestParam, Transaction.class);
            // 校验交易状态
            if (Transaction.TradeStateEnum.SUCCESS.equals(transaction.getTradeState())) {
                // 支付成功，根据订单编号查询订单信息
                // 1.查询订单信息
                String[] s = transaction.getOutTradeNo().split("_");
                OrderSubDetail orderOld = orderSubDetailService.getOne(Wrappers.<OrderSubDetail>lambdaQuery().eq(OrderSubDetail::getOrderNo, s[0]).eq(OrderSubDetail::getSubOrderId, s[1]));
                if (orderOld == null){
                    log.error("微信支付回调订单不存在:{}", transaction.getOutTradeNo());
                    return;
                }
                int orderMoney = orderOld.getOrderMoney().multiply(new BigDecimal(100)).intValue();
                // 校验金额
                if (orderOld != null && orderMoney == transaction.getAmount().getTotal()) {
                    // 金额相等 完成支付 更新订单状态
                    if (orderOld.getStatusCd().equals(OrderConstants.ORDER_STATUS_UNPAID)) {
                        orderService.success(orderOld);
                    }
                } else {
                    // 金额异常 执行退款
//                    refunded(new WechatPayRedis(transaction.getOutTradeNo(), transaction.getAmount().getTotal(), null));
                    log.error("支付金额异常");
                }
            }
        } catch (ValidationException e) {
            // 签名验证失败，返回 401 UNAUTHORIZED 状态码
            response.getWriter().write("<xml><return_code><![CDATA[FAIL]]></return_code></xml>");
        }

        // 处理成功，返回 200 OK 状态码
        response.getWriter().write("<xml><return_code><![CDATA[SUCCESS]]></return_code></xml>");
    }



    /**
     * 执行退款
     */
//    public static void refunded(WechatPayRedis wechatPay) {
//        try {
//            // 构建退款Service
//            RefundService service = new RefundService.Builder().config(WechatPayConfig.config).build();
//            // 构建请求对象
//            CreateRequest request = new CreateRequest();
//            request.setOutTradeNo(wechatPay.getOrderNumber());
//            request.setOutRefundNo(wechatPay.getOrderNumber());
//            // 支付总金额（分）
//            long total = wechatPay.getTotal();
//            // 设置退款金额
//            AmountReq amount = new AmountReq();
//            amount.setRefund(total);
//            amount.setTotal(total);
//            amount.setCurrency("CNY");
//            request.setAmount(amount);
//            // 请求API申请退款
//            Refund refund = service.create(request);
//            // 校验退款结果
//            if (refund != null && Status.SUCCESS.equals(refund.getStatus())) {
//                // 退款成功
//                log.info("微信退款成功：{}", wechatPay);
//            }
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//        }
//    }

    /***
     * 查询订单
     * @param orderNo
     * @return
     */
    public String selectOrder(String orderNo) {
        // 构建service
        NativePayService service = new NativePayService.Builder().config(config).build();
        QueryOrderByOutTradeNoRequest request = new QueryOrderByOutTradeNoRequest();
        request.setMchid(merchantId);
        request.setOutTradeNo(orderNo);
        try {
            com.wechat.pay.java.service.payments.model.Transaction transaction = service.queryOrderByOutTradeNo(request);
            return transaction.toString();
        }catch (Exception e){
            return e.getMessage();
        }
    }

    /***
     * 关闭订单
     */
    public void closeOrder(String orderNo) {
        // 构建service
        NativePayService service = new NativePayService.Builder().config(config).build();
        CloseOrderRequest closeOrderRequest= new CloseOrderRequest();
        closeOrderRequest.setMchid(merchantId);
        closeOrderRequest.setOutTradeNo(orderNo);
        service.closeOrder(closeOrderRequest);
    }
}
