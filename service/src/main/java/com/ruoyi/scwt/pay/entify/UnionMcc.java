package com.ruoyi.scwt.pay.entify;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @date 2021-10-12 14:27:33
 */
@Data
@TableName("union_mcc")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "")
public class UnionMcc extends Model<UnionMcc> {

    private static final long serialVersionUID = 1L;

//    @TableId(type= IdType.AUTO)
    @ApiModelProperty(value="")
    private Long id;
    @ApiModelProperty(value="删除标记")
    private String deleteFlag;
    @ApiModelProperty(value="大类")
    private String bigmccname;
    @ApiModelProperty(value = "名称")
    private String mccname;
    @ApiModelProperty(value="编码")
    private String mcccode;


}
