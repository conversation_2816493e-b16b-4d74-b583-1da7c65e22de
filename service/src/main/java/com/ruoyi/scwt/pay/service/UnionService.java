package com.ruoyi.scwt.pay.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.scwt.pay.entify.Order;
import com.ruoyi.scwt.pay.entify.OrderSubDetail;
import com.ruoyi.scwt.pay.entify.Union;
import com.ruoyi.scwt.pay.entify.UnionPicUpload;
import com.ruoyi.scwt.sop.dto.ChinaumsMerchantComplexUploadDto;

import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
public interface UnionService extends IService<Union> {
    String createSeq(Long userId);

    R upload(UnionPicUpload unionPicUpload);

    R autoReg(Union union);

    R requestAccountVerify(Long userId);

    R companyAccountVerify(Long userId, String transAmt);

    R complexAgreementSign(Long userId);

    R getUnionMccList();

    R getUnionRegionList(String parCode);

    AjaxResult pay(OrderSubDetail orderSubDetail);

    JSONObject queryOrder(OrderSubDetail orderSubDetail);

    String complexApplyQry(Union union);

    void toUnion(ChinaumsMerchantComplexUploadDto dto);

    Union getUnionInfoEcho(Long userId);

    R getUnionFileEcho(String filePath);

    Map<String,Object> createAgreement(String legalName, String bankAcctName, String requestSeq);

    AjaxResult gatewayPay(OrderSubDetail orderSubDetail);

    JSONObject queryGatewayOrder(OrderSubDetail orderSubDetail);

    Object terminalsQry(Union union);
}
