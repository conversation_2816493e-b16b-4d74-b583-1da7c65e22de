package com.ruoyi.scwt.pay.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.scwt.pay.entify.Order;
import com.ruoyi.scwt.pay.entify.OrderSubDetail;
import org.apache.ibatis.annotations.Update;

/**
 * 作品与著作权人映射表
 *
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
public interface OrderSubDetailMapper extends BaseMapper<OrderSubDetail> {

    @Update("UPDATE `dig_asset_order` SET STATUS_CD='PAID', PAY_TIME=NOW() WHERE ORDER_NO=#{out_trade_no}")
    int updateDigOrder(String out_trade_no);
}
