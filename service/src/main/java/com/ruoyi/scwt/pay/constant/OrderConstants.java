package com.ruoyi.scwt.pay.constant;

/**
 * <AUTHOR>
 * 通用常量
 */
public interface OrderConstants {


	/***-----------------------------------------交易类型-------------------------------------------------*/
	/**作品存证*/
	String ORDER_TYPE_EVIDENCE= "evidence";
	/**作品登记*/
	String ORDER_TYPE_REGISTER = "register";
	/**市场交易*/
	String ORDER_TYPE_MARKET = "market";
	/**资产发行*/
	String ORDER_TYPE_ISSUE = "issue";
	/***-----------------------------------------支付渠道-------------------------------------------------*/
	/**支付宝支付*/
	String PAY_TYPE_ALIPAY = "aliPay";
	/**微信支付*/
	String PAY_TYPE_WECHAT = "wxPay";
	/**银联支付*/
	String PAY_TYPE_UNIONPAY = "unionPay";
	/**银联网关支付*/
	String PAY_TYPE_UNIONGATEWAYPAY = "unionGatewayPay";
	/**动态交易支付*/
	String PAY_TYPE_DYNAMIC_PAY = "dynamicPay";
	/**公益直领支付*/
	String PAY_TYPE_PUBLIC_PAY = "publicPay";
	/***-----------------------------------------订单状态-------------------------------------------------*/
	/**已支付*/
	String ORDER_STATUS_PAID = "6100";
	/**待支付*/
	String ORDER_STATUS_UNPAID = "6200";
	/**关闭支付*/
	String ORDER_STATUS_CLOSE = "6900";
	/**待退款*/
	String ORDER_STATUS_REFUND = "6300";
	/**已退款*/
	String ORDER_STATUS_REFUNDED = "6400";


	/**待确认*/
	String ORDER_STATUS_WAIT = "6501";
	/**已完成*/
	String ORDER_STATUS_SUCCESS = "6500";

	/***-----------------------------------------银联对公账号交易验证状态-------------------------------------------------*/
	/**已发送*/
	String UNION_VERIFY_SEND = "0";
	/**验证成功*/
	String UNION_VERIFY_SUCCESS = "1";

	/***-----------------------------------------银联认证状态-------------------------------------------------*/
	/**待认证*/
	String UNION_TOBE_CERTIFIED = "0";
	/**认证完成*/
	String UNION_AUTH_SUCCESS = "1";
	/**待发起对公账户验证*/
	String UNION_AUTH_WAIT = "2";
	/**待对公账户认证*/
	String UNION_AUTH_WAIT_ACCOUNT = "3";
	/**待签约*/
	String UNION_AUTH_WAIT_SIGN = "4";
	/**认证失败*/
	String UNION_AUTH_FAILURE = "5";

	/***-----------------------------------------银联账户类型-------------------------------------------------*/
	/**个人账户*/
	String UNION_BANK_PERSONAL = "0";
	/**公司账户*/
	String UNION_BANK_ENTERPRISE = "1";

	/***-----------------------------------------市场交易类型-------------------------------------------------*/
	/**个人授权*/
	String MARKET_TYPE_PERSON = "person";
	/**企业授权*/
	String MARKET_TYPE_ENTERPRISE = "enterprise";
	/**转让*/
	String MARKET_TYPE_TRANSFER = "transfer";
	/**动态交易*/
	String MARKET_TYPE_DYNAMIC = "dynamic";
	/**公益直领*/
	String MARKET_TYPE_PUBLIC = "public";
	/***-----------------------------------------提现订单状态-------------------------------------------------*/
	/**处理中*/
	String WITHDRAW_STATUS_INIT = "0";
	/**已完成*/
	String WITHDRAW_STATUS_SUCCESS = "3";
	/**失败*/
	String WITHDRAW_STATUS_FAILURE = "2";
}
