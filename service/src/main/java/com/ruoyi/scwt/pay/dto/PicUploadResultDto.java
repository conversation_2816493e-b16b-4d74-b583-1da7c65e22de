package com.ruoyi.scwt.pay.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 图片上传返回参数
 * <AUTHOR>
 * @date 2022/11/28 - 15:11
 */
@Data
@ApiModel(value = "图片上传返回参数")
public class PicUploadResultDto {

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "返回消息")
    private String resMsg;

    @ApiModelProperty(value = "文件类型")
    private String fileType;

    @ApiModelProperty(value = "文件大小")
    private String fileSize;

    @ApiModelProperty(value = "请求流水号")
    private String requestSeq;

    @ApiModelProperty(value = "返回编码")
    private String resCode;
}
