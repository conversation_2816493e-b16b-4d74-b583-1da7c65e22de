package com.ruoyi.scwt.pay.entify;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @date 2021-10-12 14:27:33
 */
@Data
@TableName("union_pic_upload")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "")
public class UnionPicUpload extends Model<UnionPicUpload> {

    private static final long serialVersionUID = 1L;

    @TableId(type= IdType.AUTO)
    @ApiModelProperty(value="")
    private Long id;
    @ApiModelProperty(value="店铺ID")
    private Long shopId;
    @ApiModelProperty(value="图片base64")
    @NotNull(message = "图片base64不能为空")
    private String base64;
    @ApiModelProperty(value="流水号")
    private String requestSeq;
    @ApiModelProperty(value = "图片路径")
    private String filePath;
    @ApiModelProperty(value="图片类型")
    private String fileType;
    @ApiModelProperty(value="图片大小")
    private String fileSize;
    @ApiModelProperty(value="文件类型编码")
    private String documentType;
    @ApiModelProperty(value="文件名称")
    private String documentName;

    @ApiModelProperty(value="")
    private LocalDateTime createDate;
    @ApiModelProperty(value="")
    private String createStaff;
}
