package com.ruoyi.scwt.pay.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.scwt.pay.entify.Order;
import com.ruoyi.scwt.pay.entify.OrderSubDetail;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
public interface OrderSubDetailService extends IService<OrderSubDetail> {
    Object pendingPaymentOrder(OrderSubDetail orderSubDetail);

    String thirtyDealNum(Long id);

    String getSalesRevenue(List<Long> assetIds,String day);

    Map<String, String> getSevenSalesRevenue(List<Long> assetIds);

    int updateDigOrder(String out_trade_no);
}
