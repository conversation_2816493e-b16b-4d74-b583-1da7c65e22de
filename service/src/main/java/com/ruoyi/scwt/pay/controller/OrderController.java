package com.ruoyi.scwt.pay.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alipay.v3.ApiException;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.util.StringUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.scwt.asset.entity.Asset;
import com.ruoyi.scwt.asset.service.AssetService;
import com.ruoyi.scwt.file.entity.AttachmentInfo;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import com.ruoyi.scwt.identify.entity.Identify;
import com.ruoyi.scwt.identify.service.IdentifyService;
import com.ruoyi.scwt.pay.AliPayClient;
import com.ruoyi.scwt.pay.WxPayClient;
import com.ruoyi.scwt.pay.constant.OrderConstants;
import com.ruoyi.scwt.pay.dto.OrderDto;
import com.ruoyi.scwt.pay.entify.Order;
import com.ruoyi.scwt.pay.entify.OrderSubDetail;
import com.ruoyi.scwt.pay.entify.Union;
import com.ruoyi.scwt.pay.service.OrderService;
import com.ruoyi.scwt.pay.service.OrderSubDetailService;
import com.ruoyi.scwt.pay.service.UnionService;
import com.ruoyi.scwt.pay.vo.OrderVo;
import com.ruoyi.scwt.works.service.WorksEvidenceService;
import com.ruoyi.scwt.works.service.WorksRegisterService;
import com.ruoyi.sop.dto.AgentPay;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 实名认证
 */
@RestController
@RequestMapping("/order")
@Api(value = "order", tags = "订单")
@Slf4j
public class OrderController extends BaseController {

    @Autowired
    private AliPayClient aliPayClient;
    @Autowired
    private WxPayClient wxPayClient;
    @Autowired
    private UnionService unionService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private WorksEvidenceService worksEvidenceService;
    @Autowired
    private WorksRegisterService worksRegisterService;
    @Autowired
    private AssetService assetService;
    @Autowired
    private OrderSubDetailService orderSubDetailService;
    @Autowired
    private IdentifyService identifyService;
    @Autowired
    private AttachmentInfoService attachmentInfoService;

    @PostMapping("/createOrder")
    @Transactional
    public AjaxResult createOrder(@RequestBody OrderDto dto) {
        Long userId = getUserId();
        if (!identifyService.isIdentify(String.valueOf(userId))) {
            return AjaxResult.error("请先完成实名认证");
        }
        OrderSubDetail orderSubDetail = orderService.checkReplaceOrder(userId, dto);
        if (ObjectUtil.isNotEmpty(orderSubDetail) && orderSubDetail.getStatusCd().equals(OrderConstants.ORDER_STATUS_PAID)) {
            return AjaxResult.error("订单已支付,请勿重复支付！");
        }
        if (ObjectUtil.isEmpty(orderSubDetail)) {
            orderSubDetail = orderService.createOrder(userId, dto);
        }
        switch (dto.getPayChannel()) {
            case OrderConstants.PAY_TYPE_ALIPAY:
                try {
                    return aliPayClient.pay(orderSubDetail);
                } catch (ApiException e) {
                    throw new RuntimeException(e);
                }
            case OrderConstants.PAY_TYPE_WECHAT:
                return wxPayClient.prePay(orderSubDetail);
            case OrderConstants.PAY_TYPE_UNIONPAY:
                return unionService.pay(orderSubDetail);
            case OrderConstants.PAY_TYPE_UNIONGATEWAYPAY:
                return unionService.gatewayPay(orderSubDetail);
            case OrderConstants.PAY_TYPE_DYNAMIC_PAY:
                return AjaxResult.success("交易完成");
            case OrderConstants.PAY_TYPE_PUBLIC_PAY:
                return AjaxResult.success("交易完成");
            default:
                return AjaxResult.error("支付渠道错误");
        }
    }

    @DeleteMapping("/delete/{orderNo}")
    @ApiOperation(value = "取消订单", notes = "取消订单")
    public R deleteOrder(@PathVariable String orderNo) {
        Long userId = getUserId();
        OrderSubDetail orderSubDetail = orderSubDetailService.getOne(Wrappers.<OrderSubDetail>lambdaQuery()
                .eq(OrderSubDetail::getOrderNo, orderNo)
                .eq(OrderSubDetail::getCreateStaff, userId));
        if (ObjectUtil.isEmpty(orderSubDetail)) {
            return R.fail("订单不存在");
        } else {
            Boolean aBoolean = orderService.isOrderSuccess(orderSubDetail, userId);
            if (!aBoolean) {
                orderSubDetailService.update(null, Wrappers.<OrderSubDetail>lambdaUpdate()
                        .eq(OrderSubDetail::getOrderNo, orderNo)
                        .set(OrderSubDetail::getStatusCd, OrderConstants.ORDER_STATUS_CLOSE)
                        .set(OrderSubDetail::getStatusDate, LocalDateTime.now()));
            }
        }
        return R.ok(null, "取消成功");
    }


    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public R getOrderPage(OrderDto dto) {
        Long userId = getUserId();
        startPage();
        List<OrderSubDetail> list = orderSubDetailService.list(Wrappers.<OrderSubDetail>lambdaQuery()
                .eq(OrderSubDetail::getCreateStaff, userId)
                .eq(StringUtil.isNotEmpty(dto.getTradeType()), OrderSubDetail::getTradeType, dto.getTradeType())
                .eq(StringUtil.isNotEmpty(dto.getPayChannel()), OrderSubDetail::getPayChannel, dto.getPayChannel())
                .eq(StringUtil.isNotEmpty(dto.getStatusCd()), OrderSubDetail::getStatusCd, dto.getStatusCd())
                .like(StringUtil.isNotEmpty(dto.getName()), OrderSubDetail::getWorksName, dto.getName())
                .eq(StringUtil.isNotEmpty(dto.getOrderNo()), OrderSubDetail::getOrderNo, dto.getOrderNo())
                .eq(StringUtil.isNotEmpty(dto.getTransactionType()), OrderSubDetail::getTransactionType, dto.getTransactionType())
                .orderByDesc(OrderSubDetail::getCreateDate));
        List<OrderVo> voList = list.stream().map(item -> {
            OrderVo vo = new OrderVo();
            BeanUtil.copyProperties(item, vo);
            vo.setName(item.getWorksName());
            vo.setTransactionPrice(item.getRemark());
            vo.setTransactionPrice(item.getTransactionPrice());
            if (ObjectUtil.isNotEmpty(item.getAgreementDoc())){
                AttachmentInfo att = attachmentInfoService.getOne(Wrappers.<AttachmentInfo>lambdaQuery().eq(AttachmentInfo::getFilePath, item.getAgreementDoc()));
                vo.setAgreementDoc(att.getFileName());
            }
            return vo;
        }).collect(Collectors.toList());
        TableDataInfo dataTable = getDataTable(list);
        dataTable.setRows(voList);
        return R.ok(dataTable);
    }

    @ApiOperation(value = "订单导出", notes = "订单导出")
    @PostMapping("/page/export")
    public void getOrderPage(HttpServletResponse response, OrderDto dto) {
        Long userId = getUserId();
//        startPage();
        List<OrderSubDetail> list = orderSubDetailService.list(Wrappers.<OrderSubDetail>lambdaQuery()
                .eq(OrderSubDetail::getCreateStaff, userId)
                .eq(StringUtil.isNotEmpty(dto.getTradeType()), OrderSubDetail::getTradeType, dto.getTradeType())
                .eq(StringUtil.isNotEmpty(dto.getPayChannel()), OrderSubDetail::getPayChannel, dto.getPayChannel())
                .eq(StringUtil.isNotEmpty(dto.getStatusCd()), OrderSubDetail::getStatusCd, dto.getStatusCd())
                .like(StringUtil.isNotEmpty(dto.getName()), OrderSubDetail::getWorksName, dto.getName())
                .eq(StringUtil.isNotEmpty(dto.getOrderNo()), OrderSubDetail::getOrderNo, dto.getOrderNo())
                .eq(StringUtil.isNotEmpty(dto.getTransactionType()), OrderSubDetail::getTransactionType, dto.getTransactionType())
                .orderByDesc(OrderSubDetail::getCreateDate));
        List<OrderVo> voList = list.stream().map(item -> {
            OrderVo vo = new OrderVo();
            BeanUtil.copyProperties(item, vo);
            vo.setName(item.getWorksName());
            Identify buyer = identifyService.getIdentifyByUserId(item.getBuyerId());
            vo.setBuyerName(buyer.getIdName());
            if (ObjectUtil.isNotEmpty(item.getSellerId())) {
                Identify seller = identifyService.getIdentifyByUserId(item.getSellerId());
                vo.setSellerName(seller.getIdName());
            }
            return vo;
        }).collect(Collectors.toList());
        ExcelUtil<OrderVo> util = new ExcelUtil<OrderVo>(OrderVo.class);
        util.exportExcel(response, voList, "订单数据");
    }

    @ApiOperation(value = "店铺订单分页查询", notes = "分页查询")
    @GetMapping("/page/shop")
    public R getShopOrderPage(OrderDto dto) {
        Long userId = getUserId();
        startPage();
        List<OrderSubDetail> list = orderSubDetailService.list(Wrappers.<OrderSubDetail>lambdaQuery()
                .eq(OrderSubDetail::getSellerId, userId)
                .eq(StringUtil.isNotEmpty(dto.getTradeType()), OrderSubDetail::getTradeType, dto.getTradeType())
                .eq(StringUtil.isNotEmpty(dto.getPayChannel()), OrderSubDetail::getPayChannel, dto.getPayChannel())
                .eq(StringUtil.isNotEmpty(dto.getStatusCd()), OrderSubDetail::getStatusCd, dto.getStatusCd())
                .like(StringUtil.isNotEmpty(dto.getName()), OrderSubDetail::getWorksName, dto.getName())
                .eq(StringUtil.isNotEmpty(dto.getOrderNo()), OrderSubDetail::getOrderNo, dto.getOrderNo())
                .eq(StringUtil.isNotEmpty(dto.getTransactionType()), OrderSubDetail::getTransactionType, dto.getTransactionType())
                .orderByDesc(OrderSubDetail::getCreateDate));
        List<OrderVo> voList = list.stream().map(item -> {
            OrderVo vo = new OrderVo();
            BeanUtil.copyProperties(item, vo);
            vo.setName(item.getWorksName());
            Identify identify = identifyService.getIdentifyByUserId(item.getBuyerId());
            vo.setBuyerName(identify.getIdName());
            if (ObjectUtil.isNotEmpty(item.getSellerId())) {
                Identify seller = identifyService.getIdentifyByUserId(item.getSellerId());
                vo.setSellerName(seller.getIdName());
            }
            vo.setTransactionPrice(item.getTransactionPrice());
            if (ObjectUtil.isNotEmpty(item.getAgreementDoc())){
                AttachmentInfo att = attachmentInfoService.getOne(Wrappers.<AttachmentInfo>lambdaQuery().eq(AttachmentInfo::getFilePath, item.getAgreementDoc()));
                vo.setAgreementDoc(att.getFileName());
            }
            return vo;
        }).collect(Collectors.toList());
        TableDataInfo dataTable = getDataTable(list);
        dataTable.setRows(voList);
        return R.ok(dataTable);
    }

    @ApiOperation(value = "店铺订单导出", notes = "店铺订单导出")
    @PostMapping("/page/shop/export")
    public void getShopOrderExport(HttpServletResponse response, OrderDto dto) {
        Long userId = getUserId();
//        startPage();
        List<OrderSubDetail> list = orderSubDetailService.list(Wrappers.<OrderSubDetail>lambdaQuery()
                .eq(OrderSubDetail::getSellerId, userId)
                .eq(StringUtil.isNotEmpty(dto.getTradeType()), OrderSubDetail::getTradeType, dto.getTradeType())
                .eq(StringUtil.isNotEmpty(dto.getPayChannel()), OrderSubDetail::getPayChannel, dto.getPayChannel())
                .eq(StringUtil.isNotEmpty(dto.getStatusCd()), OrderSubDetail::getStatusCd, dto.getStatusCd())
                .like(StringUtil.isNotEmpty(dto.getName()), OrderSubDetail::getWorksName, dto.getName())
                .eq(StringUtil.isNotEmpty(dto.getOrderNo()), OrderSubDetail::getOrderNo, dto.getOrderNo())
                .eq(StringUtil.isNotEmpty(dto.getTransactionType()), OrderSubDetail::getTransactionType, dto.getTransactionType())
                .orderByDesc(OrderSubDetail::getCreateDate));
        List<OrderVo> voList = list.stream().map(item -> {
            OrderVo vo = new OrderVo();
            BeanUtil.copyProperties(item, vo);
            vo.setName(item.getWorksName());
            Identify buyer = identifyService.getIdentifyByUserId(item.getBuyerId());
            vo.setBuyerName(buyer.getIdName());
            if (ObjectUtil.isNotEmpty(item.getSellerId())) {
                Identify seller = identifyService.getIdentifyByUserId(item.getSellerId());
                vo.setSellerName(seller.getIdName());
            }
            return vo;
        }).collect(Collectors.toList());
        ExcelUtil<OrderVo> util = new ExcelUtil<OrderVo>(OrderVo.class);
        util.exportExcel(response, voList, "店铺订单数据");
    }

    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page/manager")
//    @PreAuthorize("@ss.hasRole('admin')")
    public R getOrderPageManger(OrderDto dto) {
        if (!SecurityUtils.isManager()) {
            return R.fail("没有权限");
        }
        startPage();
        List<OrderSubDetail> list = orderSubDetailService.list(Wrappers.<OrderSubDetail>lambdaQuery()
                .eq(StringUtil.isNotEmpty(dto.getTradeType()), OrderSubDetail::getTradeType, dto.getTradeType())
                .eq(StringUtil.isNotEmpty(dto.getPayChannel()), OrderSubDetail::getPayChannel, dto.getPayChannel())
                .eq(StringUtil.isNotEmpty(dto.getStatusCd()), OrderSubDetail::getStatusCd, dto.getStatusCd())
                .like(StringUtil.isNotEmpty(dto.getName()), OrderSubDetail::getWorksName, dto.getName())
                .eq(StringUtil.isNotEmpty(dto.getOrderNo()), OrderSubDetail::getOrderNo, dto.getOrderNo())
                .eq(StringUtil.isNotEmpty(dto.getTransactionType()), OrderSubDetail::getTransactionType, dto.getTransactionType())
                .orderByDesc(OrderSubDetail::getCreateDate));
        List<OrderVo> voList = list.stream().map(item -> {
            OrderVo vo = new OrderVo();
            BeanUtil.copyProperties(item, vo);
            vo.setName(item.getWorksName());
            Identify buyer = identifyService.getIdentifyByUserId(item.getBuyerId());
            vo.setBuyerName(buyer.getIdName());
            if (ObjectUtil.isNotEmpty(item.getSellerId())) {
                Identify seller = identifyService.getIdentifyByUserId(item.getSellerId());
                vo.setSellerName(seller.getIdName());
            }
            return vo;
        }).collect(Collectors.toList());
        TableDataInfo dataTable = getDataTable(list);
        dataTable.setRows(voList);
        return R.ok(dataTable);
    }

    @ApiOperation(value = "分页查询导出", notes = "分页查询导出")
    @PostMapping("/page/manager/export")
//    @PreAuthorize("@ss.hasRole('admin')")
    public void getOrderPageMangerExport(HttpServletResponse response, OrderDto dto) {
        if (!SecurityUtils.isManager()) {
            throw new RuntimeException("没有权限");
        }
//        startPage();
        List<OrderSubDetail> list = orderSubDetailService.list(Wrappers.<OrderSubDetail>lambdaQuery()
                .eq(StringUtil.isNotEmpty(dto.getTradeType()), OrderSubDetail::getTradeType, dto.getTradeType())
                .eq(StringUtil.isNotEmpty(dto.getPayChannel()), OrderSubDetail::getPayChannel, dto.getPayChannel())
                .eq(StringUtil.isNotEmpty(dto.getStatusCd()), OrderSubDetail::getStatusCd, dto.getStatusCd())
                .like(StringUtil.isNotEmpty(dto.getName()), OrderSubDetail::getWorksName, dto.getName())
                .eq(StringUtil.isNotEmpty(dto.getOrderNo()), OrderSubDetail::getOrderNo, dto.getOrderNo())
                .eq(StringUtil.isNotEmpty(dto.getTransactionType()), OrderSubDetail::getTransactionType, dto.getTransactionType())
                .orderByDesc(OrderSubDetail::getCreateDate));
        List<OrderVo> voList = list.stream().map(item -> {
            OrderVo vo = new OrderVo();
            BeanUtil.copyProperties(item, vo);
            vo.setName(item.getWorksName());
            Identify buyer = identifyService.getIdentifyByUserId(item.getBuyerId());
            vo.setBuyerName(buyer.getIdName());
            if (ObjectUtil.isNotEmpty(item.getSellerId())) {
                Identify seller = identifyService.getIdentifyByUserId(item.getSellerId());
                vo.setSellerName(seller.getIdName());
            }
            return vo;
        }).collect(Collectors.toList());
        ExcelUtil<OrderVo> util = new ExcelUtil<OrderVo>(OrderVo.class);
        util.exportExcel(response, voList, "订单数据");
    }

    /***
     * 模拟支付订单确认
     * @param orderNo
     * @return
     */
    @GetMapping("/confirmOrder")
    public R confirmOrder(String orderNo) {
        Long userId = getUserId();
        // 查询订单详情
        OrderSubDetail orderSubDetail = orderSubDetailService.getOne(Wrappers.<OrderSubDetail>lambdaQuery()
                .eq(OrderSubDetail::getOrderNo, orderNo)
                .eq(OrderSubDetail::getSellerId, userId));
        if (ObjectUtil.isNotEmpty(orderSubDetail) && StringUtils.equalsAny(orderSubDetail.getPayChannel(), OrderConstants.PAY_TYPE_PUBLIC_PAY, OrderConstants.PAY_TYPE_DYNAMIC_PAY)) {
            List<OrderSubDetail> list = orderSubDetailService.list(Wrappers.<OrderSubDetail>lambdaQuery()
                    .eq(OrderSubDetail::getAssetId, orderSubDetail.getAssetId())
                    .eq(OrderSubDetail::getPayChannel, orderSubDetail.getPayChannel())
                    .ne(OrderSubDetail::getSubOrderId, orderSubDetail.getSubOrderId()));
            for (OrderSubDetail subDetail : list) {
                orderSubDetailService.update(null, Wrappers.<OrderSubDetail>lambdaUpdate()
                        .eq(OrderSubDetail::getOrderNo, subDetail.getOrderNo())
                        .set(OrderSubDetail::getStatusCd, OrderConstants.ORDER_STATUS_CLOSE));
                orderService.update(null, Wrappers.<Order>lambdaUpdate()
                        .eq(Order::getOrderNo, subDetail.getOrderNo())
                        .set(Order::getStatusCd, OrderConstants.ORDER_STATUS_CLOSE));
            }
            orderSubDetailService.update(null, Wrappers.<OrderSubDetail>lambdaUpdate()
                    .eq(OrderSubDetail::getOrderNo, orderSubDetail.getOrderNo())
                    .set(OrderSubDetail::getStatusCd, OrderConstants.ORDER_STATUS_SUCCESS));
            return R.ok("操作完成");
        } else {
            return R.fail("订单类型不匹配");
        }
    }

    @GetMapping("/queryOrder")
    public R queryOrder(String orderNo) {
        OrderSubDetail orderSubDetail = orderSubDetailService.getOne(Wrappers.<OrderSubDetail>lambdaQuery().eq(OrderSubDetail::getOrderNo, orderNo));
        if (ObjectUtil.isNotEmpty(orderSubDetail)) {
            OrderVo vo = new OrderVo();
            BeanUtil.copyProperties(orderSubDetail, vo);
            vo.setName(orderSubDetail.getWorksName());
            Identify buyer = identifyService.getIdentifyByUserId(orderSubDetail.getBuyerId());
            vo.setBuyerName(buyer.getIdName());
            if (ObjectUtil.isNotEmpty(orderSubDetail.getSellerId())) {
                Identify seller = identifyService.getIdentifyByUserId(orderSubDetail.getSellerId());
                vo.setSellerName(seller.getIdName());
            }
            if (orderSubDetail.getTransactionType().equals(OrderConstants.PAY_TYPE_DYNAMIC_PAY)){
                Asset asset = assetService.getById(orderSubDetail.getAssetId());
                vo.setTransactionPrice(asset.getTransactionPrice());
            }
            Boolean aBoolean = true;
            if (!StringUtils.equalsAny(orderSubDetail.getStatusCd(), OrderConstants.ORDER_STATUS_PAID, OrderConstants.ORDER_STATUS_SUCCESS)) {
                if (orderSubDetail.getPayChannel().equals(OrderConstants.PAY_TYPE_UNIONGATEWAYPAY)) {
                    aBoolean = orderService.queryGatewayOrder(orderSubDetail);
                } else {
                    aBoolean = orderService.queryOrder(orderSubDetail);
                }
            }
            if (aBoolean) {
                return R.ok(vo, "订单已支付");
            } else {
                return R.ok(vo, "订单未支付");
            }
        } else {
            return R.fail("订单不存在");
        }
    }

    @ApiOperation(value = "查询商户可提现金额", notes = "查询商户可提现金额")
    @PostMapping("/queryWithdrawBalance")
    public R queryWithdrawBalance() {
        Union one = unionService.getOne(Wrappers.<Union>lambdaQuery().eq(Union::getCreateStaff, SecurityUtils.getUserId()));
        if (ObjectUtil.isEmpty(one) || StringUtil.isEmpty(one.getWithdrawalMerNo())) {
            return R.fail("商户未开通结算");
        }
        return R.ok(orderService.queryWithdrawBalance(one.getWithdrawalMerNo()));
    }


    @ApiOperation(value = "提现", notes = "提现")
    @PostMapping("/processWithdraw")
    public R processWithdraw(@RequestBody AgentPay agentPay) {
//        agentPay.setOrderNo(unionService.createSeq(getUserId()));
        return orderService.processWithdraw(agentPay);
    }


    @ApiOperation(value = "提现状态查询", notes = "提现状态查询")
    @PostMapping("/qurProcessWithdraw")
    public R qurProcessWithdraw(@RequestBody AgentPay agentPay) {
        return orderService.qurProcessWithdraw(agentPay);
    }
}
