package com.ruoyi.scwt.pay.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.scwt.pay.entify.MerchantWalletDetail;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022-06-22 11:30
 */
public interface MerchantWalletDetailService extends IService<MerchantWalletDetail> {

    /**
     * 插入明细
     * @return
     */
    boolean save(String orderNo, BigDecimal totalAmount, String recordType, String recordRemark, String merchantNo,String qurWithdrawBalanceData, String processWithdrawData);

}