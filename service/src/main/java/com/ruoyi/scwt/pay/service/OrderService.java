package com.ruoyi.scwt.pay.service;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.scwt.pay.dto.OrderDto;
import com.ruoyi.scwt.pay.entify.Order;
import com.ruoyi.scwt.pay.entify.OrderSubDetail;
import com.ruoyi.sop.dto.AgentPay;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
public interface OrderService extends IService<Order> {
    String createOrderNo(String payType,String payChannel,Long userId);

    OrderSubDetail checkReplaceOrder(Long userId, OrderDto dto);

    Boolean isOrderSuccess(OrderSubDetail orderSubDetail, Long userId);

    OrderSubDetail createOrder(Long userId, OrderDto dto);

    JSONObject setProfitSharing(String tradeType, BigDecimal orderMoney, String orderDesc, String sellerId);

    void success(OrderSubDetail orderSubDetail);

     Boolean queryOrder(OrderSubDetail orderSubDetail);

    Boolean queryGatewayOrder(OrderSubDetail orderSubDetail);

    Object queryWithdrawBalance(String merNo);

    R processWithdraw(AgentPay agentPay);

    R qurProcessWithdraw(AgentPay agentPay);
}
