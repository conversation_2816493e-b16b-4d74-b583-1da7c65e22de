package com.ruoyi.scwt.pay.entify;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @date 2021-10-12 14:27:33
 */
@Data
@TableName("order_sub_detail")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "")
public class OrderSubDetail extends Model<OrderSubDetail> {

    private static final long serialVersionUID = 1L;

    @TableId(type= IdType.AUTO)
    @ApiModelProperty(value="")
    private Long subOrderId;
    @ApiModelProperty(value="订单号")
    private String orderNo;
    @ApiModelProperty(value="订单金额")
    private BigDecimal orderMoney;
    @ApiModelProperty(value = "支付方式")
    private String payType;
    @ApiModelProperty(value="交易类型")
    private String tradeType;
    @ApiModelProperty(value="交易渠道")
    private String payChannel;
    @ApiModelProperty(value="买家ID")
    private Long buyerId;
    @ApiModelProperty(value="卖家ID")
    private Long sellerId;
    @ApiModelProperty(value="资产ID")
    private Long assetId;
    @ApiModelProperty(value="作品ID")
    private Long worksId;
    @ApiModelProperty(value="平台分账金额")
    private BigDecimal platformMoney;
    @ApiModelProperty(value="退款订单号")
    private String refundOrderNo;

    @ApiModelProperty(value = "资产交易类型")
    private String transactionType;
    @ApiModelProperty(value = "权利，多个以逗号隔开")
    private String rights;
    @ApiModelProperty(value = "期限（天）")
    private int days;
    @ApiModelProperty(value = "备注内容")
    private String rightRemark;
    @ApiModelProperty(value = "授权类型")
    private String authorizationType;
    @ApiModelProperty(value = "授权地区")
    private String authorizationArea;
    @ApiModelProperty(value = "分账规则")
    private String sharingRule;

    @ApiModelProperty(value = "交易价格")
    private String transactionPrice;

    @ApiModelProperty(value = "协议文件")
    private String agreementDoc;

    @ApiModelProperty(value="")
    private LocalDateTime createDate;
    @ApiModelProperty(value="")
    private String createStaff;
    @ApiModelProperty(value="")
    private LocalDateTime updateDate;
    @ApiModelProperty(value="")
    private String updateStaff;
    @ApiModelProperty(value="")
    private String statusCd;
    @ApiModelProperty(value="")
    private LocalDateTime statusDate;
    @ApiModelProperty(value="")
    private String remark;

//    @TableField(exist = false)
    @ApiModelProperty(value="订单标题")
    private String worksName;
    @ApiModelProperty(value="sop银联订单号")
    private String sopOrderNo;

    @ApiModelProperty(value = "订单时间(格式：yyyy-MM-dd)")
    private String billDate;
}
