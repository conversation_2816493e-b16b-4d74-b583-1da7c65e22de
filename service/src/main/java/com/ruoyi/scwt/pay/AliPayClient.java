package com.ruoyi.scwt.pay;


import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.v3.ApiClient;
import com.alipay.v3.ApiException;
import com.alipay.v3.Configuration;
import com.alipay.v3.api.AlipayTradeApi;
import com.alipay.v3.model.AlipayTradeQueryDefaultResponse;
import com.alipay.v3.model.AlipayTradeQueryModel;
import com.alipay.v3.model.AlipayTradeQueryResponseModel;
import com.alipay.v3.util.AlipaySignature;
import com.alipay.v3.util.GenericExecuteApi;
import com.alipay.v3.util.model.AlipayConfig;
import com.alipay.v3.util.model.CustomizedParams;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.api.issue;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.scwt.pay.constant.OrderConstants;
import com.ruoyi.scwt.pay.dto.OrderDto;
import com.ruoyi.scwt.pay.entify.OrderSubDetail;
import com.ruoyi.scwt.pay.service.OrderService;
import com.ruoyi.scwt.pay.service.OrderSubDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.ArrayList;

@Component
@Slf4j
public class AliPayClient {

    @Value("${alipay.app_id}")
    private String APP_ID;

    @Value("${alipay.private_key}")
    private String PRIVATE_KEY;

    @Value("${alipay.alipay_public_key}")
    private String ALIPAY_PUBLIC_KEY;

    @Value("${alipay.charset}")
    private String CHARSET;

    @Value("${alipay.notifyUrl}")
    private String notifyUrl;

    @Autowired
    private OrderSubDetailService orderSubDetailService;

    @Autowired
    @Lazy
    private OrderService orderService;

    @Autowired
    @Lazy
    private issue.DigAssetOrderApiService digAssetOrderApiService;

    private AlipayConfig getAlipayConfig() {
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl("https://openapi.alipay.com");
        alipayConfig.setAppId(APP_ID);
        alipayConfig.setPrivateKey(PRIVATE_KEY);
//        alipayConfig.setFormat("json");
//        alipayConfig.setCharset(CHARSET);
        alipayConfig.setAlipayPublicKey(ALIPAY_PUBLIC_KEY);
//        alipayConfig.setSignType("RSA2");
        return alipayConfig;
    }

    public AjaxResult pay(OrderSubDetail dto) throws ApiException {
        ApiClient defaultClient = Configuration.getDefaultApiClient();
        // 初始化alipay参数（全局设置一次）
        defaultClient.setAlipayConfig(getAlipayConfig());
        GenericExecuteApi api = new GenericExecuteApi();


        // 构造请求参数以调用接口
        Map<String, Object> bizParams = new HashMap<>();
        Map<String, Object> bizContent = new HashMap<>();

        // 设置商户订单号
        bizContent.put("out_trade_no", dto.getOrderNo() + "_" + dto.getSubOrderId());

        // 设置订单总金额
        bizContent.put("total_amount", dto.getOrderMoney().toString());

        // 设置订单标题
        bizContent.put("subject", dto.getWorksName());

        // 设置产品码
        bizContent.put("product_code", "FAST_INSTANT_TRADE_PAY");
        // 设置PC扫码支付的方式
        bizContent.put("qr_pay_mode", "1");
        bizContent.put("notify_url", notifyUrl);
        bizParams.put("biz_content", bizContent);
        bizParams.put("notify_url", notifyUrl);

//        System.out.println("bizParams:" + bizParams.toString());
        try {
            // 如果是第三方代调用模式，请设置app_auth_token（应用授权令牌）
            String pageRedirectionData = api.pageExecute("alipay.trade.page.pay", "POST", bizParams);
            // 如果需要返回GET请求，请使用
//             String pageRedirectionData = api.pageExecute("alipay.trade.page.pay", "GET", bizParams);
//            System.out.println(pageRedirectionData);
            return AjaxResult.success(pageRedirectionData);
        } catch (ApiException e) {
            log.error("调用支付宝下单失败, url=" + e.getMessage(), e);
            return AjaxResult.error("调用失败");
        }
    }


    public void aliPayNotify(HttpServletRequest request, HttpServletResponse response) throws Exception {
        //获取支付宝POST过来反馈信息
        Map<String, String> params = new HashMap<String, String>();
        Map requestParams = request.getParameterMap();
        for (Iterator iter = requestParams.keySet().iterator(); iter.hasNext(); ) {
            String name = (String) iter.next();
            String[] values = (String[]) requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i]
                        : valueStr + values[i] + ",";
            }
            //乱码解决，这段代码在出现乱码时使用。
            //valueStr = new String(valueStr.getBytes("ISO-8859-1"), "utf-8");
            params.put(name, valueStr);
        }

        //异步验签：切记alipaypublickey是支付宝的公钥，请去open.alipay.com对应应用下查看。
        //公钥证书模式验签，alipayPublicCertPath是支付宝公钥证书引用路径地址，需在对应应用中下载
        //boolean signVerified= AlipaySignature.rsaCertCheckV1(params, alipayPublicCertPath, charset,sign_type);
        //普通公钥模式验签，切记alipaypublickey是支付宝的公钥，请去open.alipay.com对应应用下查看。
        boolean flag = AlipaySignature.rsaCheckV1(params, ALIPAY_PUBLIC_KEY, CHARSET, "RSA2");

        /* 实际验证过程建议商户务必添加以下校验：
            1、需要验证该通知数据中的out_trade_no是否为商户系统中创建的订单号，
            2、判断total_amount是否确实为该订单的实际金额（即商户订单创建时的金额），
            3、校验通知中的seller_id（或者seller_email) 是否为out_trade_no这笔单据的对应的操作方（有的时候，一个商户可能有多个seller_id/seller_email）
            4、验证app_id是否为该商户本身。
            */
        if (flag) {//验证成功
            //商户订单号
            String out_trade_no = new String(request.getParameter("out_trade_no").getBytes("ISO-8859-1"), "UTF-8");
            //支付宝交易号
            String trade_no = new String(request.getParameter("trade_no").getBytes("ISO-8859-1"), "UTF-8");
            //交易状态
            String trade_status = new String(request.getParameter("trade_status").getBytes("ISO-8859-1"), "UTF-8");
            if (trade_status.equals("TRADE_FINISHED") || trade_status.equals("TRADE_SUCCESS")) {
                if (out_trade_no.startsWith("DIG_")){
                    digAssetOrderApiService.paySuccess(out_trade_no);
                }else {
                    String[] s = out_trade_no.split("_");
                    OrderSubDetail orderOld = orderSubDetailService.getOne(Wrappers.<OrderSubDetail>lambdaQuery().eq(OrderSubDetail::getOrderNo, s[0]).eq(OrderSubDetail::getSubOrderId, s[1]));
                    if (orderOld == null){
                        log.error("支付宝支付回调订单不存在:{}", out_trade_no);
                        return;
                    }
                    orderService.success(orderOld);
                }
            }
            response.getWriter().write("success");
        } else {//验证失败
            response.getWriter().write("failure");
        }
    }

    public String selectOrder(OrderSubDetail dto) {
        try {
        ApiClient defaultClient = Configuration.getDefaultApiClient();
        // 初始化alipay参数（全局设置一次）
            defaultClient.setAlipayConfig(getAlipayConfig());

        // 构造请求参数以调用接口
        AlipayTradeApi api = new AlipayTradeApi();
        AlipayTradeQueryModel data = new AlipayTradeQueryModel();

        // 设置订单支付时传入的商户订单号
        data.setOutTradeNo(dto.getOrderNo() + "_" + dto.getSubOrderId());

        // 设置支付宝交易号
//        data.setTradeNo("2014112611001004680 073956707");

        // 设置银行间联模式下有用
//        data.setOrgPid("2088101117952222");

        // 设置查询选项
        List<String> queryOptions = new ArrayList<String>();
        queryOptions.add("trade_settle_info");
        data.setQueryOptions(queryOptions);


        // 第三方代调用模式下请设置app_auth_token
        CustomizedParams params = new CustomizedParams();
//        params.setAppAuthToken("<-- 请填写应用授权令牌 -->");

        AlipayTradeQueryResponseModel response = api.query(data, params);
        return null;
        } catch (ApiException e) {
            AlipayTradeQueryDefaultResponse errorObject = (AlipayTradeQueryDefaultResponse) e.getErrorObject();
            System.out.println("调用失败:" + errorObject);
            throw new RuntimeException(e);
        }
    }

    public Object checkPendingPayOrder(OrderSubDetail orderSubDetail) {
        String order = selectOrder(orderSubDetail);
        return null;
    }
}
