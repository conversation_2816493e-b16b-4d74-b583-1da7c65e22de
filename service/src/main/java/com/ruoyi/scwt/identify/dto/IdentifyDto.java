package com.ruoyi.scwt.identify.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ruoyi.scwt.identify.entity.LegalPerson;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 实名认证
 *
 * <AUTHOR>
 * @date 2020-10-29 15:48:38
 */
@Data
public class IdentifyDto {


    @ApiModelProperty(value="主键",hidden = true)
    private Long identifyId;

    @ApiModelProperty(value="用户ID",hidden = true)
    private Long userId;

    @ApiModelProperty(value="认证类型,person:个人 enterprise：企业")
    private String identifyType;



//    @NotBlank(message = "证件类型不能为空")
//    @ApiModelProperty(value="证件类型")
//    private String idType;

//    @NotBlank(message = "姓名不能为空")
    @ApiModelProperty(value="姓名")
    private String idName;

//    @NotBlank(message = "证件号不能为空")
    @ApiModelProperty(value="证件号")
    private String idNo;

    @ApiModelProperty(value="手机号")
    private String phone;

//    @NotBlank(message = "证件照片不能为空")
//    @ApiModelProperty(value="证件照片")
//    private String idCard;

    @ApiModelProperty(value="法人姓名")
    private String legalName;

//    @NotBlank(message = "地址不能为空")
//    @ApiModelProperty(value="地址")
//    private String address;

    @ApiModelProperty(value="注册地")
    private String registrationPlace;

    @ApiModelProperty(value="注册区域")
    private String registrationArea;


    @ApiModelProperty(value="企业联系电话")
    private String enterprisePhone;


    @ApiModelProperty(value="状态",hidden = true)
    private String statusCd;

    @ApiModelProperty(value="创建时间",hidden = true)
    private LocalDateTime createDate;


    @ApiModelProperty(value="法人信息")
    private LegalPerson legalPerson;
    }
