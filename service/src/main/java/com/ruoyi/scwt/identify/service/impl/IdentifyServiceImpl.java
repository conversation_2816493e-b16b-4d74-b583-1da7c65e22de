package com.ruoyi.scwt.identify.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.scwt.exception.BusinessException;
import com.ruoyi.scwt.identify.constant.IdentifyConstants;
import com.ruoyi.scwt.identify.entity.Identify;
import com.ruoyi.scwt.identify.entity.WcUser;
import com.ruoyi.scwt.identify.mapper.IdentifyMapper;
import com.ruoyi.scwt.identify.mapper.WcUserMapper;
import com.ruoyi.scwt.identify.service.IdentifyService;
import com.ruoyi.scwt.shop.entity.ShopInfo;
import com.ruoyi.scwt.shop.mapper.ShopInfoMapper;
import com.ruoyi.scwt.shop.service.ShopInfoService;
import com.ruoyi.scwt.sop.DataProcessing.Bmark2Sop;
import com.ruoyi.scwt.sop.controller.EvidenceAndRegisterController;
import com.ruoyi.scwt.sop.dto.RegisterForeignDto;
import com.ruoyi.system.mapper.SysUserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 实名认证
 *
 * <AUTHOR>
 * @date 2020-10-29 15:48:38
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class IdentifyServiceImpl extends ServiceImpl<IdentifyMapper, Identify> implements IdentifyService {

    private final EvidenceAndRegisterController evidenceAndRegisterController;
    private final WcUserMapper wcUserMapper;
    private final ShopInfoService shopInfoService;

    private final SysUserMapper userMapper;
    private final ShopInfoMapper shopInfoMapper;

    @Override
    public Identify getIdentifyByUserId(Long userId) {
        return baseMapper.selectOne(Wrappers.<Identify>query().lambda().eq(Identify::getUserId, userId));
    }

    @Override
    public List<Identify> getIdentifyVo(Long userId) {
        return baseMapper.selectList(Wrappers.<Identify>query().lambda().eq(Identify::getUserId, userId).orderByDesc(Identify::getIdentifyId).eq(Identify::getStatusCd, IdentifyConstants.IDENTIFY_TAKE_EFFECT));
    }

    @Override
    public boolean isIdentify(String userId) {
        return baseMapper.selectCount(Wrappers.<Identify>query().lambda().eq(Identify::getUserId, userId)) > 0;
    }

    @Async
    @Override
    public void sopForeign(Identify identify) {
        String type= Objects.equals(identify.getIdentifyType(), IdentifyConstants.IDENTIFY_TYPE_PERSON) ?"identityCard":"busiLicense";
        RegisterForeignDto event=new RegisterForeignDto(identify.getIdName(),identify.getIdNo(), type, identify.getLegalName(),identify.getCreateStaff());
        log.info("用户注册上链接口参数{}", event);
        JSONObject foreign = evidenceAndRegisterController.foreign(event);
        if (StrUtil.isBlank(foreign.getStr("address"))) {
            throw new BusinessException(foreign.toString());
        }
        WcUser wcUser = new WcUser();
        wcUser.setUserId(identify.getUserId());
        wcUser.setPrivateKey(foreign.getStr("privateKey"));
        wcUser.setPublicKey(foreign.getStr("publicKey"));
        wcUser.setAddress(foreign.getStr("address"));
        wcUser.setCreateDate(LocalDateTime.now());
        wcUserMapper.insert(wcUser);
        identify.setWcUserId(foreign.getStr("id"));
        baseMapper.updateById(identify);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R userLogout(Long userId) {
        ShopInfo shopInfo = shopInfoMapper.selectOne(Wrappers.<ShopInfo>query().lambda().eq(ShopInfo::getCreateStaff, userId));
        shopInfoService.shopLogout(shopInfo.getShopId());
        Identify identify = getIdentifyByUserId(userId);
        if (Objects.nonNull(identify)){
            identify.setStatusCd(IdentifyConstants.IDENTIFY_INVALID);
            identify.setStatusDate(LocalDateTime.now());
            identify.setRemark("用户注销");
            baseMapper.updateById(identify);
        }
        userMapper.deleteUserById(userId);
        return R.ok(null,"注销完成");
    }

    @Override
    public String getUserAddress(Long userId) {
        WcUser wcUser = wcUserMapper.selectOne(Wrappers.<WcUser>query().lambda().eq(WcUser::getUserId, userId));
        if (Objects.nonNull(wcUser)){
            return wcUser.getAddress();
        }
        return null;
    }

}
