package com.ruoyi.scwt.identify.enums;

/**
 * 著作权人证件类型
 *
 * <AUTHOR>
 * @date 2020-10-14
 **/
public enum IdentifyEnum {

    /**-----------实名认证类型------------*/
    PERSON("person","自然人"),
    ENTERPRISE("enterprise","企业法人"),
    /**-----------证件类型------------*/

    ID_CARD("0","身份证"),
    BUSINESS_LICENSE("1","营业执照"),
    ORGANIZATION_CERTIFICATE("2","组织机构代码证书"),
    INSTITUTION_LEGAL_PERSON("3","事业单位法人证书"),
    MASS_ORGANIZATION("4","社团法人证书"),
    ADMINISTRATIVE_LICENSE("5","行政执法主体资格证"),
    OTHER_VALID_DOCUMENTS("6","其他有效证件");

    IdentifyEnum(String code, String name){
        this.code=code;
        this.name=name;
    }

    private String code;

    private String name;

    public String getCode(){
        return this.code;
    }

    public static String getName(String code){
        for (IdentifyEnum ownerType : IdentifyEnum.values()){
            if (ownerType.getCode().equals(code)){
                return ownerType.name;
            }
        }
        return null;
    }


}
