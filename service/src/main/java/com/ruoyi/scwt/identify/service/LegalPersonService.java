package com.ruoyi.scwt.identify.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.scwt.identify.entity.Identify;
import com.ruoyi.scwt.identify.entity.LegalPerson;

import java.util.List;

/**
 * 实名认证
 *
 * <AUTHOR>
 * @date 2020-10-29 15:48:38
 */
public interface LegalPersonService extends IService<LegalPerson> {
    LegalPerson getByIdentifyId(Long identifyId);
}
