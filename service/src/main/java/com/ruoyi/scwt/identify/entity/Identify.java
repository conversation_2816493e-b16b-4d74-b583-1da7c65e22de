package com.ruoyi.scwt.identify.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 实名认证
 *
 * <AUTHOR>
 * @date 2020-10-29 15:48:38
 */
@Data
@TableName("IDENTIFY")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "实名认证")
public class Identify extends Model<Identify> {
private static final long serialVersionUID = 1L;

    @TableId(type= IdType.AUTO)
    @ApiModelProperty(value="主键",hidden = true)
    private Long identifyId;

    @ApiModelProperty(value="用户ID",hidden = true)
    private Long userId;

    @NotBlank(message = "姓名不能为空")
    @ApiModelProperty(value="姓名")
    @Excel(name = "姓名", prompt = "姓名")
    private String idName;

    @NotBlank(message = "认证类型不能为空")
    @ApiModelProperty(value="认证类型,person:个人 enterprise：企业")
    @Excel(name = "认证类型", readConverterExp = "person=个人,enterprise=企业")
    private String identifyType;

    @ApiModelProperty(value="企业类型")
    private String enterpriseType;

    @NotBlank(message = "证件类型不能为空")
    @ApiModelProperty(value="证件类型")
    private String idType;

    @NotBlank(message = "证件号不能为空")
    @ApiModelProperty(value="证件号")
    @Excel(name = "证件号", prompt = "证件号")
    private String idNo;

    @ApiModelProperty(value="手机号")
    @Excel(name = "手机号", prompt = "手机号")
    private String phone;

    @NotBlank(message = "证件照片不能为空")
    @ApiModelProperty(value="证件照片")
    private String idCard;

    @ApiModelProperty(value="法人姓名")
    @Excel(name = "法人姓名", prompt = "法人姓名")
    private String legalName;

    @NotBlank(message = "地址不能为空")
    @ApiModelProperty(value="地址")
    @Excel(name = "地址", prompt = "地址")
    private String address;

    @ApiModelProperty(value="注册地")
    private String registrationPlace;

    @ApiModelProperty(value="注册区域")
    private String registrationArea;

    @ApiModelProperty(value="主营业务/业务范围")
    private String businessScope;
    @ApiModelProperty(value="企业联系电话")
    private String enterprisePhone;
    @ApiModelProperty(value="企业邮箱")
    private String enterpriseEmail;

    @ApiModelProperty(value="状态",hidden = true)
    private String statusCd;

    @ApiModelProperty(value="状态时间",hidden = true)
    private LocalDateTime statusDate;

    @ApiModelProperty(value="创建人",hidden = true)
    private String createStaff;

    @ApiModelProperty(value="创建时间",hidden = true)
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDate;

    @ApiModelProperty(value="修改人",hidden = true)
    private String updateStaff;

    @ApiModelProperty(value="修改时间",hidden = true)
    private LocalDateTime updateDate;

    @ApiModelProperty(value="备注",hidden = true)
    private String remark;

//    @NotBlank(message = "证件信息识别出来的信息不能为空")
    @ApiModelProperty(value="证件信息识别出来的信息",hidden = true)
    private String cardMessage;


//    @NotBlank(message = "国籍不能为空")
    @ApiModelProperty(value="国籍")
    private String country;

    @ApiModelProperty(value="省")
    private String province;

    @ApiModelProperty(value="市")
    private String city;
//
//    @ApiModelProperty(value="区")
//    private String district;

    @ApiModelProperty(value="营业期限")
    private String businessTerm;

    @ApiModelProperty(value="文创链用户ID")
    private String wcUserId;

    @TableField(exist = false)
    @ApiModelProperty(value="短信验证码")
    private String smsCode;

    @TableField(exist = false)
    @ApiModelProperty(value="法人信息")
    private LegalPerson legalPerson;
    }
