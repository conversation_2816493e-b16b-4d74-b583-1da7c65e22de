package com.ruoyi.scwt.identify.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 实名认证
 *
 * <AUTHOR>
 * @date 2020-10-29 15:48:38
 */
@Data
@TableName("legal_person")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "实名认证-法人信息")
public class LegalPerson extends Model<LegalPerson> {
private static final long serialVersionUID = 1L;

    @TableId(type= IdType.AUTO)
    @ApiModelProperty(value="主键",hidden = true)
    private Long id;

    @ApiModelProperty(value="主键",hidden = true)
    private Long identifyId;

    @NotBlank(message = "身份证人像面图片地址不能为空")
    @ApiModelProperty(value="身份证人像面图片地址")
    private String idCardFace;

    @NotBlank(message = "身份证国徽面图片地址不能为空")
    @ApiModelProperty(value="身份证国徽面图片地址")
    private String idCardBack;


    @NotBlank(message = "姓名不能为空")
    @ApiModelProperty(value="姓名")
    private String idName;

    @NotBlank(message = "证件号不能为空")
    @ApiModelProperty(value="证件号")
    private String idNo;

    @ApiModelProperty(value="手机号")
    @NotBlank(message = "手机号不能为空")
    private String phone;

    @NotBlank(message = "地址不能为空")
    @ApiModelProperty(value="地址")
    private String address;

    @NotBlank(message = "邮箱不能为空")
    @ApiModelProperty(value="邮箱")
    private String email;

    @NotBlank(message = "有效期不能为空")
    @ApiModelProperty(value="有效期")
    private String lifespan;

//    @NotBlank(message = "证件信息识别出来的信息不能为空")
    @ApiModelProperty(value="证件信息识别出来的信息")
    private String cardMessage;

    @ApiModelProperty(value="创建时间",hidden = true)
    private LocalDateTime createDate;
    }
