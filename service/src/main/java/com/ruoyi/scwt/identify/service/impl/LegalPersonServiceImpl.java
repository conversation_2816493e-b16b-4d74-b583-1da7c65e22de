package com.ruoyi.scwt.identify.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.scwt.identify.entity.Identify;
import com.ruoyi.scwt.identify.entity.LegalPerson;
import com.ruoyi.scwt.identify.mapper.IdentifyMapper;
import com.ruoyi.scwt.identify.mapper.LegalPersonMapper;
import com.ruoyi.scwt.identify.service.IdentifyService;
import com.ruoyi.scwt.identify.service.LegalPersonService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 实名认证
 *
 * <AUTHOR>
 * @date 2020-10-29 15:48:38
 */
@Service
public class LegalPersonServiceImpl extends ServiceImpl<LegalPersonMapper, LegalPerson> implements LegalPersonService {

    @Override
    public LegalPerson getByIdentifyId(Long identifyId) {
        return baseMapper.selectOne(Wrappers.<LegalPerson>lambdaQuery().eq(LegalPerson::getIdentifyId,identifyId));
    }
}
