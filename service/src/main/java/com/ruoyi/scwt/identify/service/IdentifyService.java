package com.ruoyi.scwt.identify.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.scwt.identify.entity.Identify;

import java.util.List;

/**
 * 实名认证
 *
 * <AUTHOR>
 * @date 2020-10-29 15:48:38
 */
public interface IdentifyService extends IService<Identify> {

    /**
     * 获取用户的实名认证对象，未实名则返回null
     * @param userId 用户id
     * @return
     */
    Identify getIdentifyByUserId(Long userId);

    /**
     * 根据用户id查询实名认证信息
     * @param userId
     * @return
     */
    List<Identify> getIdentifyVo(Long userId);

    /***
     * 判断用户是否实名
     * @param userId
     * @return
     */
    boolean isIdentify(String userId);

    void sopForeign(Identify identify);

    R userLogout(Long userId);

    String getUserAddress(Long userId);
}
