package com.ruoyi.scwt.shop.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @date 2021-10-12 14:27:33
 */
@Data
@TableName("shop_info")
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class ShopInfo extends Model<ShopInfo> {

    private static final long serialVersionUID = 1L;

    @TableId(type= IdType.AUTO)
    @ApiModelProperty(value="店铺ID",hidden = true)
    private Long shopId;
    @NotBlank(message = "店铺类型不能为空")
    @ApiModelProperty(value="店铺类型")
    private String shopType;
    @NotBlank(message = "店铺名称不能为空")
    @Size(max = 30, message = "店铺名称长度不能超过30")
    @ApiModelProperty(value="店铺名称")
    private String shopName;
    @NotBlank(message = "店铺封面不能为空")
    @ApiModelProperty(value="店铺封面")
    private String shopCover;
//    @NotBlank(message = "店铺logo不能为空")
    @ApiModelProperty(value="店铺logo")
    private String shopLogo;
    @NotBlank(message = "店铺简介不能为空")
    @Size(max = 150, message = "店铺简介长度不能超过150")
    @ApiModelProperty(value="店铺简介")
    private String shopAbstract;
    @NotBlank(message = "店铺简属地能为空")
    @ApiModelProperty(value="店铺属地")
    private String belongPlace;
    @ApiModelProperty(value="店铺联系电话")
    private String shopPhone;
    @ApiModelProperty(value="店铺邮箱")
    private String shopEmail;
    @ApiModelProperty(value="店铺二维码")
    private String shopQrcode;
    @ApiModelProperty(value="状态",hidden = true)
    private String statusCd;

    @ApiModelProperty(value="状态时间",hidden = true)
    private LocalDateTime statusDate;

    @ApiModelProperty(value="创建人",hidden = true)
    private String createStaff;

    @ApiModelProperty(value="创建时间",hidden = true)
    private LocalDateTime createDate;

    @ApiModelProperty(value="修改人",hidden = true)
    private String updateStaff;

    @ApiModelProperty(value="修改时间",hidden = true)
    private LocalDateTime updateDate;

    @ApiModelProperty(value="备注",hidden = true)
    private String remark;

//    @TableField(exist = false)
//    @ApiModelProperty(value="标签ID,多个以逗号隔开")
//    @NotBlank(message = "标签ID不能为空")
//    private String labelIds;

    @ApiModelProperty(value="标签，多个以逗号隔开",hidden = true)
    private String labelName;
    @ApiModelProperty(value = "下架原因")
    private String downReason;
    @ApiModelProperty(value = "认证状态")
    private String unionStatus;
}
