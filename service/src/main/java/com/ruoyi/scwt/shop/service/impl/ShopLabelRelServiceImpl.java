package com.ruoyi.scwt.shop.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.scwt.shop.entity.ShopInfo;
import com.ruoyi.scwt.shop.entity.ShopLabelRel;
import com.ruoyi.scwt.shop.mapper.ShopInfoMapper;
import com.ruoyi.scwt.shop.mapper.ShopLabelRelMapper;
import com.ruoyi.scwt.shop.service.ShopInfoService;
import com.ruoyi.scwt.shop.service.ShopLabelRelService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Service
public class ShopLabelRelServiceImpl extends ServiceImpl<ShopLabelRelMapper, ShopLabelRel> implements ShopLabelRelService {

    @Override
    public void insertLabelRel(Long shopId, String labelIds) {
        String[] labelIdList = labelIds.split(",");
        for (String labelId : labelIdList) {
            ShopLabelRel shopLabelRel = new ShopLabelRel();
            shopLabelRel.setShopId(shopId);
            shopLabelRel.setLabelId(Long.valueOf(labelId));
            this.save(shopLabelRel);
        }
    }
}
