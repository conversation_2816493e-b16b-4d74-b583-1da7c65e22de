package com.ruoyi.scwt.shop.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.scwt.shop.dto.ShopInfoDto;
import com.ruoyi.scwt.shop.dto.ShopInfoStatusDto;
import com.ruoyi.scwt.shop.entity.ShopInfo;
import com.ruoyi.scwt.shop.vo.ShopInfoVo;
import com.ruoyi.scwt.works.entity.EvidenceOwner;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
public interface ShopInfoService extends IService<ShopInfo> {

    List<ShopInfoVo> getHomeShop(ShopInfoDto dto);

    ShopInfoVo getShopByAssetId(Long id);

    Long getRepeatName(String shopName);

    R shopLogout(Long shopId);
}
