package com.ruoyi.scwt.shop.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class ShopInfoStatusDto {

  /**
   * 资产ID
   */
  @ApiModelProperty(value = "店铺ID")
  private Long shopId;

  /**
   * 状态
   */
  @ApiModelProperty(value = "状态")
  private String statusCd;
  @ApiModelProperty(value = "下架原因")
  private String downReason;

  public ShopInfoStatusDto(Long shopId, String statusCd,String downReason) {
    this.shopId = shopId;
    this.statusCd = statusCd;
    this.downReason = downReason;
  }

  public ShopInfoStatusDto() {
  }
}
