package com.ruoyi.scwt.shop.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.scwt.asset.entity.AssetZoneShop;
import com.ruoyi.scwt.asset.service.AssetService;
import com.ruoyi.scwt.asset.service.AssetZoneShopService;
import com.ruoyi.scwt.common.util.SmsCodeUtils;
import com.ruoyi.scwt.file.util.OssUtil;
import com.ruoyi.scwt.shop.constant.ShopConstants;
import com.ruoyi.scwt.shop.dto.ShopInfoDto;
import com.ruoyi.scwt.shop.entity.ShopInfo;
import com.ruoyi.scwt.shop.mapper.ShopInfoMapper;
import com.ruoyi.scwt.shop.service.ShopInfoService;
import com.ruoyi.scwt.shop.vo.ShopInfoVo;
import com.ruoyi.system.mapper.SysUserMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Service
public class ShopInfoServiceImpl extends ServiceImpl<ShopInfoMapper, ShopInfo> implements ShopInfoService {

    @Autowired
    private AssetZoneShopService assetZoneShopService;
    @Autowired
    private AssetService assetService;
    @Autowired
    private OssUtil ossUtil;
    @Override
    public List<ShopInfoVo> getHomeShop(ShopInfoDto dto) {
        return null;
    }

    @Override
    public ShopInfoVo getShopByAssetId(Long assetId) {
        AssetZoneShop assetZoneShop = assetZoneShopService.getById(assetId);
        ShopInfo shopInfo = baseMapper.selectById(assetZoneShop.getShopId());
        ShopInfoVo shopInfoVo=new ShopInfoVo();
        BeanUtils.copyProperties(shopInfo,shopInfoVo);
        shopInfoVo.setShopLogo(ossUtil.getFileUrl(shopInfoVo.getShopLogo()));
        shopInfoVo.setShopCover(ossUtil.getFileUrl(shopInfoVo.getShopCover()));
        shopInfoVo.setForSaleNum(Math.toIntExact(assetService.getForSaleNumByShopId(assetZoneShop.getShopId())));
        return shopInfoVo;
    }

    @Override
    public Long getRepeatName(String shopName) {
        long count = baseMapper.selectCount(Wrappers.<ShopInfo>lambdaQuery()
                .eq(ShopInfo::getShopName, shopName)
                .ne(ShopInfo::getStatusCd, ShopConstants.SHOP_DELETE));
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R shopLogout(Long shopId) {
        ShopInfo shopInfo = baseMapper.selectById(shopId);
        if (ObjectUtil.isEmpty(shopInfo)){
            return R.fail("店铺不存在");
        }
        shopInfo.setStatusCd(ShopConstants.SHOP_DELETE);
        shopInfo.setStatusDate(LocalDateTime.now());
        shopInfo.setRemark("店铺注销");
        baseMapper.updateById(shopInfo);
        assetService.setShopStatusCd(shopInfo.getShopId(), ShopConstants.SHOP_DELETE,"店铺注销");
        return R.ok(null,"注销完成");
    }


}
