package com.ruoyi.scwt.zone.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.scwt.shop.entity.ShopInfo;
import com.ruoyi.scwt.shop.mapper.ShopInfoMapper;
import com.ruoyi.scwt.shop.service.ShopInfoService;
import com.ruoyi.scwt.zone.dto.ZoneDto;
import com.ruoyi.scwt.zone.entity.Zone;
import com.ruoyi.scwt.zone.mapper.ZoneMapper;
import com.ruoyi.scwt.zone.service.ZoneService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Service
public class ZoneServiceImpl extends ServiceImpl<ZoneMapper, Zone> implements ZoneService {

    @Override
    public List<Zone> getHomeZone(ZoneDto dto) {
        return null;
    }
}
