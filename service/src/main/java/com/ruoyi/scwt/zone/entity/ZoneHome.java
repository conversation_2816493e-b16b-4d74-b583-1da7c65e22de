package com.ruoyi.scwt.zone.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


@Data
@ApiModel
@TableName("zone_home")
@EqualsAndHashCode(callSuper = true)
public class ZoneHome extends Model<ZoneHome> {
private static final long serialVersionUID = 1L;

  /**
   * 专区ID
   */
  @ApiModelProperty(value = "专区ID")
  private Long ZoneId;

  /**
   * 标签ID
   */
  @ApiModelProperty(value = "排序")
  private Long sort;
  @ApiModelProperty(hidden = true)
  private LocalDateTime createDate;

}
