package com.ruoyi.scwt.zone.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @date 2021-10-12 14:27:33
 */
@Data
@TableName("zone")
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class Zone extends Model<Zone> {

    private static final long serialVersionUID = 1L;

    @TableId(type= IdType.AUTO)
    @ApiModelProperty(value="专区ID",hidden = true)
    private Long zoneId;
    @NotBlank(message = "专区类型不能为空")
    @ApiModelProperty(value="专区类型")
    private String zoneType;
//    @NotBlank(message = "专区主题名称不能为空")
    @ApiModelProperty(value="专区主题名称")
    private String zoneThemeName;
    @NotBlank(message = "专区名称不能为空")
    @Size(max = 30,message = "专区名称不能超过30个字符")
    @ApiModelProperty(value="专区名称")
    private String zoneName;
    @NotBlank(message = "专区封面不能为空")
    @ApiModelProperty(value="专区封面")
    private String zoneCover;
    @NotBlank(message = "专区属地能为空")
    @ApiModelProperty(value="专区属地")
    private String belongPlace;
    @NotBlank(message = "专区简介不能为空")
    @Size(max = 150,message = "专区简介不能超过150个字符")
    @ApiModelProperty(value="专区简介")
    private String zoneAbstract;
    @NotBlank(message = "专区故事不能为空")
    @ApiModelProperty(value="专区故事")
    private String zoneStory;
//    @NotBlank(message = "店铺ID"不能为空")
    @ApiModelProperty(value="店铺ID")
    private String shopId;

    @ApiModelProperty(hidden = true)
    private LocalDateTime createDate;
    @ApiModelProperty(hidden = true)
    private String createStaff;
    @ApiModelProperty(hidden = true)
    private LocalDateTime updateDate;
    @ApiModelProperty(hidden = true)
    private String updateStaff;
    @ApiModelProperty(hidden = true)
    private String statusCd;
    @ApiModelProperty(hidden = true)
    private LocalDateTime statusDate;
    @ApiModelProperty(hidden = true)
    private String remark;

    @ApiModelProperty(value="标签,多个以逗号隔开")
    @NotBlank(message = "标签不能为空")
    private String labelName;
}
