package com.ruoyi.scwt.zone.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.scwt.common.entity.SysLabel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @date 2021-10-12 14:27:33
 */
@Data
public class ZoneVo{

    @ApiModelProperty(value="专区ID")
//    @Excel(name = "专区编号", prompt = "专区编号")
    private Long zoneId;
    @ApiModelProperty(value="专区名称")
    @Excel(name = "专区名称", prompt = "专区名称")
    private String zoneName;
    @Excel(name = "专区类型", readConverterExp = "theme=主题专区,shop=店铺专区,public=公共专区")
    @ApiModelProperty(value="专区类型")
    private String zoneType;
    @ApiModelProperty(value="专区主题名称")
    private String zoneThemeName;
    @ApiModelProperty(value="专区封面")
    private String zoneCover;
    @ApiModelProperty(value="专区属地")
    private String belongPlace;
    @ApiModelProperty(value="专区简介")
    @Excel(name = "专区简介", prompt = "专区简介")
    private String zoneAbstract;
    @ApiModelProperty(value="专区故事")
    private String zoneStory;
    @ApiModelProperty(value="标签列表")
    private String labelName;
    @ApiModelProperty(value="店铺ID")
    private String shopId;

    @ApiModelProperty(hidden = true)
    private LocalDateTime createDate;
    @Excel(name = "专区状态", readConverterExp = "4100=启用,4200=停用,4900=删除")
    private String statusCd;

    @TableField(exist = false)
    @ApiModelProperty(value="资产数量")
    @Excel(name = "资产数量", prompt = "资产数量")
    private Long assetNum;

}
