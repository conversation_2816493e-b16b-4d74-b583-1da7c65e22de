package com.ruoyi.scwt.zone.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @date 2021-10-12 14:27:33
 */
@Data
public class ZoneDto  {

    @ApiModelProperty(value="专区ID",hidden = true)
    private Long zoneId;
    @ApiModelProperty(value="专区名称")
    private String zoneName;

    @ApiModelProperty(value="标签")
    private String labelName;

    @ApiModelProperty(value="地区")
    private String area;

    @ApiModelProperty(value="状态")
    private String statusCd;

    @ApiModelProperty(value="是否是首页展示")
    private Boolean isHome;
}
