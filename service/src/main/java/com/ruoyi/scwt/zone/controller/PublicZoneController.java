package com.ruoyi.scwt.zone.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.scwt.asset.constant.AssetConstants;
import com.ruoyi.scwt.asset.entity.Asset;
import com.ruoyi.scwt.asset.entity.AssetZoneShop;
import com.ruoyi.scwt.asset.service.AssetService;
import com.ruoyi.scwt.asset.service.AssetZoneShopService;
import com.ruoyi.scwt.asset.vo.AssetVo;
import com.ruoyi.scwt.common.service.SysLabelService;
import com.ruoyi.scwt.file.util.OssUtil;
import com.ruoyi.scwt.identify.service.IdentifyService;
import com.ruoyi.scwt.shop.entity.ShopInfo;
import com.ruoyi.scwt.shop.service.ShopInfoService;
import com.ruoyi.scwt.shop.vo.ShopInfoVo;
import com.ruoyi.scwt.zone.constant.ZoneConstants;
import com.ruoyi.scwt.zone.dto.ZoneDto;
import com.ruoyi.scwt.zone.dto.ZoneStatusDto;
import com.ruoyi.scwt.zone.entity.Zone;
import com.ruoyi.scwt.zone.entity.ZoneHome;
import com.ruoyi.scwt.zone.service.ZoneHomeService;
import com.ruoyi.scwt.zone.service.ZoneService;
import com.ruoyi.scwt.zone.vo.ZoneVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 实名认证
 */
@RestController
@AllArgsConstructor
@RequestMapping("/publicZone")
@Api(value = "publicZone", tags = "公共专区")
@Slf4j
public class PublicZoneController extends BaseController {

    private final ZoneService zoneService;

    private final AssetService assetService;

    private final AssetZoneShopService assetZoneShopService;

    private final ZoneHomeService zoneHomeService;

    private final SysLabelService sysLabelService;

    private final ShopInfoService shopInfoService;

    private final IdentifyService identifyService;

    private final OssUtil ossUtil;

    /**
     * 分页查询
     *
     * @param dto 分页对象
     * @return
     */
    @ApiOperation(value = "分页查询-不需要登录", notes = "分页查询-不需要登录")
    @GetMapping("/home")
    @Anonymous
    public R getZoneHome(ZoneDto dto) {
        List<Zone> list=new ArrayList<>();
        if (dto.getIsHome()){
            List<Long> zoneIds = zoneHomeService.list(Wrappers.<ZoneHome>lambdaQuery().orderByDesc(ZoneHome::getCreateDate)).stream().map(ZoneHome::getZoneId).collect(Collectors.toList());
            for (Long zoneId : zoneIds) {
                Zone one = zoneService.getOne(Wrappers.<Zone>lambdaQuery().eq(Zone::getZoneId, zoneId).eq(Zone::getStatusCd, ZoneConstants.ZONE_ENABLE));
                list.add(one);
            }
        }else {
//            List<Long> zoneIds = null;
//            if (StringUtils.isNotEmpty(dto.getLabelIds())) {
//                List<ZoneLabelRel> shopLabelRels = zoneLabelRelService.list(Wrappers.<ZoneLabelRel>query().lambda().in(ZoneLabelRel::getLabelId, StrUtil.split(dto.getLabelIds(), ",")));
//                zoneIds = shopLabelRels.stream().map(ZoneLabelRel::getZoneId).collect(Collectors.toList());
//            }
            LambdaQueryWrapper<Zone> lambda = Wrappers.<Zone>query().lambda()
                    .like(StringUtils.isNotEmpty(dto.getZoneName()), Zone::getZoneName, dto.getZoneName())
                    .eq(Zone::getStatusCd, ZoneConstants.ZONE_ENABLE)
                    .like(StringUtils.isNotEmpty(dto.getLabelName()), Zone::getLabelName, dto.getLabelName())
                    .eq(StringUtils.isNotEmpty(dto.getArea()), Zone::getBelongPlace, dto.getArea())
                    .eq(Zone::getZoneType, ZoneConstants.ZONE_TYPE_PUBLIC)
                    .orderByDesc(Zone::getCreateDate);
            startPage();
            list = zoneService.list(lambda);
        }
        List<ZoneVo> voList = list.stream().map(item -> {
            ZoneVo zoneVo = new ZoneVo();
            BeanUtils.copyProperties(item, zoneVo);
//            zoneVo.setLabels(sysLabelService.getLabelListByZoneId(item.getZoneId()));
            zoneVo.setZoneCover(ossUtil.getFileUrl(zoneVo.getZoneCover()));
            return zoneVo;
        }).collect(Collectors.toList());
        TableDataInfo dataTable = getDataTable(list);
        dataTable.setRows(voList);
        return R.ok(dataTable);

    }
    /**
     * 分页查询
     *
     * @param zoneId
     * @return
     */
    @ApiOperation(value = "专区资产分页查询", notes = "专区资产分页查询")
    @GetMapping("/asset/list")
    @Anonymous
    public R getZoneAssetList(Long zoneId,String assetName,String assetType,String businessCategory,String transactionType) {
        List<AssetZoneShop> listByShopId = assetZoneShopService.getListByZoneId(zoneId);
        if (listByShopId.size()==0){
            return R.fail("该专区暂无资产！");
        }
        List<Long> assetIds = listByShopId.stream().map(AssetZoneShop::getAssetId).collect(Collectors.toList());
        LambdaQueryWrapper<Asset> lambda = Wrappers.<Asset>query().lambda()
                .eq( Asset::getStatusCd, AssetConstants.ASSET_ON_SHELF)
                .like(StringUtils.isNotEmpty(assetName), Asset::getAssetName, assetName)
                .eq(StringUtils.isNotEmpty(assetType), Asset::getAssetType, assetType)
                .eq(StringUtils.isNotEmpty(businessCategory), Asset::getBusinessCategory, businessCategory)
                .eq(StringUtils.isNotEmpty(transactionType), Asset::getTransactionType, transactionType)
                .in( Asset::getAssetId, assetIds)
                .orderByDesc(Asset::getCreateDate);
        startPage();
        List<Asset> list = assetService.list(lambda);
        List<AssetVo> voList = list.stream().map(item -> {
            AssetVo assetVo = new AssetVo();
            BeanUtils.copyProperties(item, assetVo);
//            assetVo.setLabels(sysLabelService.getLabelListByAssetId(assetVo.getAssetId()));
            assetVo.setAssetCover(ossUtil.getFileUrl(assetVo.getAssetCover()));
            ShopInfoVo shopInfoVo= shopInfoService.getShopByAssetId(assetVo.getAssetId());
            assetVo.setBelongUser(identifyService.getIdentifyByUserId(Long.valueOf(item.getCreateStaff())).getIdName());
            assetVo.setShopInfoVo(shopInfoVo);
            return assetVo;
        }).collect(Collectors.toList());
        TableDataInfo dataTable = getDataTable(list);
        dataTable.setRows(voList);
        return R.ok(dataTable);
    }
 /**
     * 分页查询
     *
     * @param dto 分页对象
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public R getZonePage(ZoneDto dto) {
        LambdaQueryWrapper<Zone> lambda = Wrappers.<Zone>query().lambda()
                .like(StringUtils.isNotEmpty(dto.getZoneName()), Zone::getZoneName, dto.getZoneName())
                .eq(StringUtils.isNotEmpty(dto.getStatusCd()), Zone::getStatusCd, dto.getStatusCd())
                .ne(StringUtils.isEmpty(dto.getStatusCd()), Zone::getStatusCd, ZoneConstants.ZONE_DELETE)
                .eq(!SecurityUtils.isManager(), Zone::getCreateStaff, getUserId())
                .eq(Zone::getZoneType, ZoneConstants.ZONE_TYPE_PUBLIC)
                .orderByDesc(Zone::getCreateDate);
        startPage();
        List<Zone> list = zoneService.list(lambda);
        List<ZoneVo> voList = list.stream().map(item -> {
            ZoneVo zoneVo = new ZoneVo();
            BeanUtils.copyProperties(item, zoneVo);
            List<AssetZoneShop> listByZoneId = assetZoneShopService.getListByZoneId(item.getZoneId());
            Long assetNum = ObjectUtil.isNotEmpty(listByZoneId)?listByZoneId.size():0L;
            zoneVo.setZoneCover(ossUtil.getFileUrl(zoneVo.getZoneCover()));
            zoneVo.setZoneStory(ossUtil.getFileUrl(zoneVo.getZoneStory()));
            zoneVo.setAssetNum(assetNum);
            return zoneVo;
        }).collect(Collectors.toList());
        TableDataInfo dataTable = getDataTable(list);
        dataTable.setRows(voList);
        return R.ok(dataTable);
    }

    @ApiOperation(value = "excel导出", notes = "excel导出")
    @PostMapping("/page/export")
    public void getZonePage(HttpServletResponse response, ZoneDto dto) {
        LambdaQueryWrapper<Zone> lambda = Wrappers.<Zone>query().lambda()
                .like(StringUtils.isNotEmpty(dto.getZoneName()), Zone::getZoneName, dto.getZoneName())
                .eq(StringUtils.isNotEmpty(dto.getStatusCd()), Zone::getStatusCd, dto.getStatusCd())
                .ne(StringUtils.isEmpty(dto.getStatusCd()), Zone::getStatusCd, ZoneConstants.ZONE_DELETE)
                .eq(!SecurityUtils.isManager(), Zone::getCreateStaff, getUserId())
                .eq(Zone::getZoneType, ZoneConstants.ZONE_TYPE_PUBLIC)
                .orderByDesc(Zone::getCreateDate);
//        startPage();
        List<Zone> list = zoneService.list(lambda);
        List<ZoneVo> voList = list.stream().map(item -> {
            ZoneVo zoneVo = new ZoneVo();
            BeanUtils.copyProperties(item, zoneVo);
            List<AssetZoneShop> listByZoneId = assetZoneShopService.getListByZoneId(item.getZoneId());
            Long assetNum = ObjectUtil.isNotEmpty(listByZoneId)?listByZoneId.size():0L;
            zoneVo.setZoneCover(ossUtil.getFileUrl(zoneVo.getZoneCover()));
            zoneVo.setZoneStory(ossUtil.getFileUrl(zoneVo.getZoneStory()));
            zoneVo.setAssetNum(assetNum);
            return zoneVo;
        }).collect(Collectors.toList());
        ExcelUtil<ZoneVo> util = new ExcelUtil<ZoneVo>(ZoneVo.class);
        util.exportExcel(response, voList, "订单数据");
    }

    /**
     * 新增
     *
     * @param zone
     * @return R
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/basicInfo")
    public R save(@Validated @RequestBody Zone zone) {
        String userId = String.valueOf(SecurityUtils.getUserId());
        if (!identifyService.isIdentify(userId)){
            return R.fail("请先完成实名认证");
        }
        zone.setCreateStaff(userId);
        zone.setStatusCd(ZoneConstants.ZONE_ENABLE);
        zone.setCreateDate(LocalDateTime.now());
        zone.setStatusDate(LocalDateTime.now());
        zoneService.save(zone);
//        zoneLabelRelService.insertLabelRel(zone.getZoneId(), zone.getLabelIds());
        return R.ok(zone,"专区信息新增成功！");
    }

    /**
     * 通过id查询
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @GetMapping
    @Anonymous
    public R getById(Long id) {
        Zone zone = zoneService.getById(id);
        ZoneVo zoneVo=new ZoneVo();
        BeanUtils.copyProperties(zone,zoneVo);
//        zoneVo.setLabels(sysLabelService.getLabelListByZoneId(zone.getZoneId()));
        zoneVo.setZoneCover(ossUtil.getFileUrl(zoneVo.getZoneCover()));
        zoneVo.setZoneStory(ossUtil.getFileUrl(zoneVo.getZoneStory()));
        return R.ok(zoneVo);
    }

    @ApiOperation(value = "新增/取消推荐专区", notes = "新增/取消推荐专区")
    @PutMapping("/zoneHome")
    public R zoneHome(Long zoneId) {
        ZoneHome zoneHome = zoneHomeService.getOne(Wrappers.<ZoneHome>query().lambda().eq(ZoneHome::getZoneId, zoneId));
        if (ObjectUtil.isEmpty(zoneHome)){
            zoneHome = new ZoneHome();
            zoneHome.setZoneId(zoneId);
            zoneHome.setCreateDate(LocalDateTime.now());
            zoneHome.setSort(1L);
            zoneHomeService.save(zoneHome);
        }else {
            zoneHomeService.remove(Wrappers.<ZoneHome>query().lambda().eq(ZoneHome::getZoneId, zoneId));
        }
        return R.ok("操作成功");
    }

    /**
     * 修改
     * @param zone
     * @return R
     */
    @ApiOperation(value = "修改", notes = "修改")
    @PutMapping
    public R updateById(@Validated @RequestBody Zone zone) {
        String userId = String.valueOf(SecurityUtils.getUserId());
        zone.setUpdateStaff(userId);
        zone.setUpdateDate(LocalDateTime.now());
        zoneService.updateById(zone);
//        zoneLabelRelService.remove(Wrappers.<ZoneLabelRel>query().lambda().eq(ZoneLabelRel::getZoneId, zone.getZoneId()));
//        zoneLabelRelService.insertLabelRel(zone.getZoneId(), zone.getLabelIds());
        return R.ok("专区信息新增成功！");
    }

    /**
     *
     * 修改状态
     * @param zone
     * @return R
     */
    @ApiOperation(value = "状态修改", notes = "状态修改")
    @PutMapping("/status")
    public R updateStatusById(@Validated @RequestBody ZoneStatusDto zone) {
        Zone byId = zoneService.getById(zone.getZoneId());
        String userId = String.valueOf(SecurityUtils.getUserId());
        if (SecurityUtils.isManager() || userId.equals(byId.getCreateStaff())){
            byId.setStatusCd(zone.getStatusCd());
            byId.setStatusDate(LocalDateTime.now());
            byId.updateById();
        }else {
            return R.fail("无权限操作");
        }
        return R.ok("专区状态修改成功！");
    }


    @GetMapping("/waitUpList")
    @ApiOperation(value = "查询待上架资产", notes = "查询待上架资产")
    public R getWaitUpList(Long zoneId, String assetName,Long shopId,String shopName,String labelName,String assetType) {
        List<Long> assetIds=new ArrayList<>();
        List<String> shopIds =new ArrayList<>();
        if (ObjectUtil.isNotEmpty(shopName)){
            List<ShopInfo> infoList = shopInfoService.list(Wrappers.<ShopInfo>query().lambda().like(ShopInfo::getShopName, shopName));
            if (ObjectUtil.isNotEmpty(infoList)) {
                shopIds = infoList.stream().map(ShopInfo::getShopId).map(String::valueOf).collect(Collectors.toList());
            }else if (ObjectUtil.isNotEmpty(shopId)){
                shopIds.add(String.valueOf(shopId));
            }else {
                return R.ok(getDataTable(new ArrayList<>()));
            }
        }
//        if (ObjectUtil.isNotEmpty(shopId)){
//            shopIds.add(String.valueOf(shopId));
//        }
        if (ObjectUtil.isNotEmpty(shopIds)){
            List<AssetZoneShop> listByShopId = assetZoneShopService.list(Wrappers.<AssetZoneShop>lambdaQuery().in(AssetZoneShop::getShopId, shopIds));
            assetIds = listByShopId.stream().map(AssetZoneShop::getAssetId).collect(Collectors.toList());
        }
        List<AssetZoneShop> assetZoneShops = assetZoneShopService.getListByZoneId(zoneId);
        List<Long> assetId = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(assetZoneShops)){
            assetId = assetZoneShops.stream().map(AssetZoneShop::getAssetId).collect(Collectors.toList());
        }
        startPage();
        List<Asset> list = assetService.list(Wrappers.<Asset>query().lambda()
                .select(Asset::getAssetId, Asset::getAssetName,Asset::getAssetType, Asset::getAssetFileIds,Asset::getLabelName,Asset::getCreateStaff)
                .in(assetIds.size()>0, Asset::getAssetId, assetIds)
                .notIn(assetId.size()>0, Asset::getAssetId, assetId)
                .like(StringUtils.isNotEmpty(assetName), Asset::getAssetName, assetName)
                .eq(StringUtils.isNotEmpty(assetType), Asset::getAssetType, assetType)
//                .eq(Asset::getTransactionType, AssetConstants.ASSET_PUBLIC_SALE)
                .like(StringUtils.isNotEmpty(labelName), Asset::getLabelName, labelName)
                .eq(Asset::getStatusCd, AssetConstants.ASSET_ON_SHELF));
        list.forEach(item->item.setBelongUser(identifyService.getIdentifyByUserId(Long.valueOf(item.getCreateStaff())).getIdName()));
        return R.ok(getDataTable(list));
    }

    @ApiOperation(value = "专区资产上下架", notes = "专区资产上下架")
    @PutMapping("/asset/upDown")
    public R updateAssetStatusById(String zoneId,String assetIds,String statusCd) {
        if (!SecurityUtils.isManager()){
            return R.fail("无权限操作");
        }
        if (statusCd.equals(ZoneConstants.ZONE_ASSET_UP)){
//            assetZoneShopService.update(Wrappers.<AssetZoneShop>update().set("zone_id", null).eq("zone_id", zoneId));
            assetZoneShopService.update(Wrappers.<AssetZoneShop>update().set("zone_id", zoneId).set("zone_up_time", LocalDateTime.now()).in("asset_id", assetIds.split(",")));
        }else if (statusCd.equals(ZoneConstants.ZONE_ASSET_DOWN)){
            assetZoneShopService.update(Wrappers.<AssetZoneShop>update().set("zone_id", null).in("asset_id", assetIds.split(",")));
        }else {
            return R.fail("参数错误");
        }
        return R.ok("操作成功");
    }
}
