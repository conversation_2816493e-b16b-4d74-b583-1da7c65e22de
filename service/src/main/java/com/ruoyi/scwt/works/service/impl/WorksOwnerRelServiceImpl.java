package com.ruoyi.scwt.works.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.scwt.works.constant.WorksConstants;
import com.ruoyi.scwt.works.entity.*;
import com.ruoyi.scwt.works.mapper.*;
import com.ruoyi.scwt.works.service.*;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Service
@RequiredArgsConstructor
public class WorksOwnerRelServiceImpl extends ServiceImpl<WorksOwnerRelMapper, WorksOwnerRel> implements WorksOwnerRelService {

//    @Autowired
//    private WorksEvidenceService worksEvidenceService;

//    @Autowired
//    private WorksRegisterService worksRegisterService;


    private final RegisterOwnerService registerOwnerService;

    private final EvidenceOwnerService evidenceOwnerService;

    private final WorksEvidenceMapper worksEvidenceMapper;
    private final WorksRegisterMapper worksRegisterMapper;

    @Override
    public Long selectCountByOwnerId(String type, Long ownerId) {
        List<WorksOwnerRel> worksOwnerRels = baseMapper.selectList(Wrappers.<WorksOwnerRel>lambdaQuery()
                .eq(WorksOwnerRel::getType, type)
                .eq(WorksOwnerRel::getOwnerId, ownerId));
        List<Long> ids = worksOwnerRels.stream().map(WorksOwnerRel::getWorksId).collect(Collectors.toList());
        if (ids.size() == 0){
            return 0L;
        }else if (type.equals(WorksConstants.WORKS_TYPE_EVIDENCE)){
            Long count = worksEvidenceMapper.selectCount(Wrappers.<WorksEvidence>lambdaQuery()
                    .in(WorksEvidence::getWorksId, ids)
                    .and(i -> i
                            .eq(WorksEvidence::getStatusCd, WorksConstants.WORKS_DONE).or()
                            .eq(WorksEvidence::getStatusCd, WorksConstants.WORKS_REVIEW).or()
                            .eq(WorksEvidence::getStatusCd, WorksConstants.WORKS_EVIDENCE)));
            return count;
        }else if (type.equals(WorksConstants.WORKS_TYPE_REGISTER)){
            Long count = worksRegisterMapper.selectCount(Wrappers.<WorksRegister>lambdaQuery()
                    .in(WorksRegister::getWorksId, ids)
                    .and(i -> i
                            .eq(WorksRegister::getStatusCd, WorksConstants.WORKS_DONE).or()
                            .eq(WorksRegister::getStatusCd, WorksConstants.WORKS_REVIEW).or()
                            .eq(WorksRegister::getStatusCd, WorksConstants.WORKS_EVIDENCE)));
            return count;
        }else {
            throw new RuntimeException("未知作品类型");
        }
    }

    @Override
    public void insertOwnerRel(Long worksId, String ownerIds, String worksTypeRegister) {
        String[] ids = ownerIds.split(",");
//        int size = baseMapper.selectList(Wrappers.<WorksOwnerRel>lambdaQuery().in(WorksOwnerRel::getOwnerId, ids)).size();
//        if (size != ids.length){
//             throw new RuntimeException("著作权人不匹配");
//        }
        for (String id : ids) {
            WorksOwnerRel worksOwnerRel = new WorksOwnerRel();
            worksOwnerRel.setWorksId(worksId);
            worksOwnerRel.setOwnerId(Long.valueOf(id));
            worksOwnerRel.setType(worksTypeRegister);
            this.save(worksOwnerRel);
        }
    }

    @Override
    public List<RegisterOwner> getRegisterOwnerList(Long worksId) {
        List<WorksOwnerRel> worksOwnerRels = baseMapper.selectList(Wrappers.<WorksOwnerRel>lambdaQuery()
                .eq(WorksOwnerRel::getType, WorksConstants.WORKS_TYPE_REGISTER)
                .eq(WorksOwnerRel::getWorksId, worksId));
        List<Long> ids = worksOwnerRels.stream().map(WorksOwnerRel::getOwnerId).collect(Collectors.toList());
        if (ids.size() == 0){
            return null;
        }
        return registerOwnerService.listByIds(ids);
    }

    @Override
    public List<EvidenceOwner> getEvidenceOwnerList(Long worksId) {
        List<WorksOwnerRel> worksOwnerRels = baseMapper.selectList(Wrappers.<WorksOwnerRel>lambdaQuery()
                .eq(WorksOwnerRel::getType, WorksConstants.WORKS_TYPE_EVIDENCE)
                .eq(WorksOwnerRel::getWorksId, worksId));
        List<Long> ids = worksOwnerRels.stream().map(WorksOwnerRel::getOwnerId).collect(Collectors.toList());
        return evidenceOwnerService.listByIds(ids);
    }


}
