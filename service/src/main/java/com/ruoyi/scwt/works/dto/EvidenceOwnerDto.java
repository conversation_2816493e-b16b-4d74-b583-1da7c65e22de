package com.ruoyi.scwt.works.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @date 2021-10-12 14:27:33
 */
@Data
public class EvidenceOwnerDto {

    @ApiModelProperty(value="著作权人ID")
    private Long ownerId;
    @ApiModelProperty(value="著作权人类型")
    private String ownerType;
    @ApiModelProperty(value="证件类型")
    private String idType;
    @ApiModelProperty(value="名称")
    private String ownerName;

    @ApiModelProperty(value="开始时间")
    private LocalDateTime startDate;
    @ApiModelProperty(value="结束时间")
    private LocalDateTime endDate;
    @ApiModelProperty(value="状态")
    private String statusCd;
}
