package com.ruoyi.scwt.works.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.PageDomain;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.scwt.common.controller.AliIdentifyController;
import com.ruoyi.scwt.common.dto.AliIdentifyDto;
import com.ruoyi.scwt.identify.service.IdentifyService;
import com.ruoyi.scwt.sop.DataProcessing.Bmark2Sop;
import com.ruoyi.scwt.works.constant.OwnerConstants;
import com.ruoyi.scwt.works.constant.WorksConstants;
import com.ruoyi.scwt.works.dto.EvidenceOwnerDto;
import com.ruoyi.scwt.works.entity.EvidenceOwner;
import com.ruoyi.scwt.works.entity.RegisterOwner;
import com.ruoyi.scwt.works.entity.WorksOwnerRel;
import com.ruoyi.scwt.works.service.EvidenceOwnerService;
import com.ruoyi.scwt.works.service.WorksOwnerRelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.domain.R;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 
 *
 * <AUTHOR>
 * @date 2021-10-12 14:27:33
 */
@RestController
@AllArgsConstructor
@RequestMapping("/evidenceOwner")
@Api(value = "evidenceOwner", tags = "存证著作权人管理")
public class EvidenceOwnerController extends BaseController {

    private final EvidenceOwnerService evidenceOwnerService;

    private final WorksOwnerRelService worksOwnerRelService;

    /**
     * 分页查询
     * @param dto 分页对象
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public R getFileEvidenceOwnerPage(EvidenceOwnerDto dto) {
        LambdaQueryWrapper<EvidenceOwner> lambda = Wrappers.<EvidenceOwner>query().lambda()
                .eq(StringUtils.isNotEmpty(dto.getOwnerType()),EvidenceOwner::getOwnerType,dto.getOwnerType())
                .eq(StringUtils.isNotEmpty(dto.getIdType()),EvidenceOwner::getIdType,dto.getIdType())
                .eq(EvidenceOwner::getStatusCd,OwnerConstants.OWNER_TAKE_EFFECT)
                .like(StringUtils.isNotEmpty(dto.getOwnerName()),EvidenceOwner::getOwnerName,dto.getOwnerName())
                .ge(ObjectUtil.isNotEmpty(dto.getStartDate()), EvidenceOwner::getCreateDate,dto.getStartDate())
                .le(ObjectUtil.isNotEmpty(dto.getEndDate()), EvidenceOwner::getCreateDate,dto.getEndDate())
                .eq(!SecurityUtils.isAdmin(getUserId()),EvidenceOwner::getCreateStaff, getUserId())
                .orderByAsc(EvidenceOwner::getCreateDate);
        startPage();
        List<EvidenceOwner> list = evidenceOwnerService.list(lambda);
        return R.ok(getDataTable(list));
    }


    /**
     * 通过id查询
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @GetMapping
    public R getById(Long id) {
        return R.ok(evidenceOwnerService.getById(id));
    }

    /**
     * 新增
     * @param evidenceOwner
     * @return R
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping
    public R save(@Validated @RequestBody EvidenceOwner evidenceOwner) {
        String userId=String.valueOf(SecurityUtils.getUserId());
        evidenceOwner.setCreateStaff(userId);
        return evidenceOwnerService.insert(evidenceOwner);
    }

    /**
     * 修改
     * @param evidenceOwner
     * @return R
     */
    @ApiOperation(value = "修改", notes = "修改")
    @PutMapping
    public R updateById(@Validated @RequestBody EvidenceOwner evidenceOwner) {
        Long count = worksOwnerRelService.selectCountByOwnerId(WorksConstants.WORKS_TYPE_EVIDENCE, evidenceOwner.getOwnerId());
        if (count > 0){
            return R.fail("该存证著作权人正在使用中，无法修改");
        }
        evidenceOwner.setUpdateStaff(String.valueOf(SecurityUtils.getUserId()));
        evidenceOwner.setUpdateDate(LocalDateTime.now());
        return R.ok(evidenceOwnerService.updateById(evidenceOwner));
    }

    /**
     * 状态修改
     * @return R
     */
    @ApiOperation(value = "状态修改", notes = "状态修改")
    @DeleteMapping
    public R updateStatusCd(Long ownerId) {
        EvidenceOwner one = evidenceOwnerService.getOne(Wrappers.<EvidenceOwner>lambdaQuery()
                .eq(EvidenceOwner::getOwnerId, ownerId)
                .eq(!SecurityUtils.isAdmin(getUserId()), EvidenceOwner::getCreateStaff, getUserId()));
        if (ObjectUtil.isEmpty(one)){
            return R.fail("无权限删除");
        }
        Long count = worksOwnerRelService.selectCountByOwnerId(WorksConstants.WORKS_TYPE_EVIDENCE,ownerId);
        if (count > 0){
            return R.fail("该存证著作权人正在使用中，无法删除");
        }
        one.setStatusCd(OwnerConstants.OWNER_INVALID);
        one.setStatusDate(LocalDateTime.now());
        one.setUpdateStaff(String.valueOf(SecurityUtils.getUserId()));
        one.setUpdateDate(LocalDateTime.now());
        return R.ok(evidenceOwnerService.updateById(one));
    }

}
