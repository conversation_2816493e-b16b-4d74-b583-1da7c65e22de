package com.ruoyi.scwt.works.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.scwt.common.controller.AliIdentifyController;
import com.ruoyi.scwt.common.dto.AliIdentifyDto;
import com.ruoyi.scwt.common.util.DataDesensitizationUtil;
import com.ruoyi.scwt.identify.constant.IdentifyConstants;
import com.ruoyi.scwt.identify.entity.Identify;
import com.ruoyi.scwt.identify.service.IdentifyService;
import com.ruoyi.scwt.sop.DataProcessing.Bmark2Sop;
import com.ruoyi.scwt.sop.dto.RegisterForeignDto;
import com.ruoyi.scwt.works.constant.OwnerConstants;
import com.ruoyi.scwt.works.entity.EvidenceOwner;
import com.ruoyi.scwt.works.entity.RegisterOwner;
import com.ruoyi.scwt.works.mapper.EvidenceOwnerMapper;
import com.ruoyi.scwt.works.mapper.WorksOwnerRelMapper;
import com.ruoyi.scwt.works.service.EvidenceOwnerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class EvidenceOwnerServiceImpl extends ServiceImpl<EvidenceOwnerMapper, EvidenceOwner> implements EvidenceOwnerService {

    private final Bmark2Sop bmark2Sop;
    private final IdentifyService identifyService;
    private final AliIdentifyController aliIdentifyController;

    @Override
    public EvidenceOwner getDesensitizationById(Long id) {
        EvidenceOwner evidenceOwner = baseMapper.selectById(id);
        evidenceOwner.setOwnerName(DataDesensitizationUtil.replaceLeft(evidenceOwner.getOwnerName(), 1));
        evidenceOwner.setIdNo(DataDesensitizationUtil.replaceLeftRight(evidenceOwner.getIdNo(), 6, 4));
        if (StrUtil.isNotEmpty(evidenceOwner.getLegalName())) {
            evidenceOwner.setLegalName(DataDesensitizationUtil.replaceLeft(evidenceOwner.getLegalName(), 1));
        }
        return evidenceOwner;
    }

    @Override
    @Async
    public EvidenceOwner addSopOwner(EvidenceOwner evidenceOwner) {
        RegisterOwner registerOwner=new RegisterOwner();
        BeanUtil.copyProperties(evidenceOwner,registerOwner);
        JSONObject jsonObject = bmark2Sop.addOwner(registerOwner, "evidence", evidenceOwner.getCreateStaff());
        evidenceOwner.setSopOwnerId(jsonObject.getStr("id"));
        baseMapper.updateById(evidenceOwner);
        return evidenceOwner;
    }

    @Override
    public R insert(EvidenceOwner evidenceOwner) {
        if (!identifyService.isIdentify(evidenceOwner.getCreateStaff())){
            return R.fail("请先完成实名认证");
        }
        EvidenceOwner one = baseMapper.selectOne(Wrappers.<EvidenceOwner>lambdaQuery()
                .eq(EvidenceOwner::getOwnerName, evidenceOwner.getOwnerName())
                .eq(EvidenceOwner::getIdNo, evidenceOwner.getIdNo())
                .eq(EvidenceOwner::getStatusCd, OwnerConstants.OWNER_TAKE_EFFECT)
                .eq(StringUtils.isNotEmpty(evidenceOwner.getLegalName()), EvidenceOwner::getLegalName,evidenceOwner.getLegalName())
                .eq(EvidenceOwner::getCreateStaff,evidenceOwner.getCreateStaff()));
        if(ObjectUtil.isNotNull(one)){
            return R.fail("该著作权人已存在");
        }
        AliIdentifyDto identify=new AliIdentifyDto(evidenceOwner.getOwnerType(),evidenceOwner.getOwnerName(),evidenceOwner.getIdNo(),evidenceOwner.getLegalName());
        R r = aliIdentifyController.aliIdentify(identify);
        log.info("要素认证返回结果：{},{}",r.getData(),r.getMsg());
        if (ObjectUtil.isEmpty(r.getData()) || !(Boolean) r.getData()) {
            return R.fail(r.getMsg());
        }
        evidenceOwner.setCreateStaff(evidenceOwner.getCreateStaff());
        evidenceOwner.setStatusCd(OwnerConstants.OWNER_TAKE_EFFECT);
        evidenceOwner.setCreateDate(LocalDateTime.now());
        evidenceOwner.setStatusDate(LocalDateTime.now());
        baseMapper.insert(evidenceOwner);
        addSopOwner(evidenceOwner);
        return R.ok("存证著作权人新增成功");
    }

}
