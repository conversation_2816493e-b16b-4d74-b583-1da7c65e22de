
package com.ruoyi.scwt.works.util;


import cn.hutool.extra.template.TemplateException;
import com.lowagie.text.pdf.BaseFont;
import com.ruoyi.common.config.RuoYiConfig;
import freemarker.cache.ClassTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;
import org.xhtmlrenderer.pdf.ITextFontResolver;
import org.xhtmlrenderer.pdf.ITextRenderer;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.*;


/**
 * 功能：pdf处理工具类
 * 简化 PdfUtils
 * <AUTHOR>
 * @version 1.0 2018/2/23 17:21
 */
@Slf4j
public class PdfUtil {
    private PdfUtil() {
    }

//    static FreeMarkerConfigurer configurer = SpringUtils.getBean(FreeMarkerConfigurer.class);
static Configuration configurer = new Configuration(Configuration.VERSION_2_3_30);
    static ClassTemplateLoader loader = new ClassTemplateLoader(RuoYiConfig.class, "/templates");
    public static void generate(OutputStream out, String template, Object dataModel,String fontFile) throws Exception {

        ITextRenderer renderer = new ITextRenderer();
        //设置字符集(宋体),此处必须与模板中的<body style="font-family: SimSun">一致,区分大小写,不能写成汉字"宋体"
        ITextFontResolver fontResolver = renderer.getFontResolver();
        fontResolver.addFont(fontFile, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);

        Document doc = generateDoc(template, dataModel);
        renderer.setDocument(doc, null);
        renderer.layout();
        renderer.createPDF(out, false);

        renderer.finishPDF();
    }
    private static Document generateDoc(String templateName, Object dataModel) {
        Template tp  = getTemplate(templateName);
        StringWriter stringWriter = new StringWriter();
        try(BufferedWriter writer = new BufferedWriter(stringWriter)) {
            try {
                assert tp != null;
                tp.process(dataModel, writer);
                writer.flush();
            } catch (TemplateException e) {
                log.error("模板不存在或者路径错误", e);
            } catch (IOException e) {
                log.error("IO异常", e);
            }
            DocumentBuilder builder = DocumentBuilderFactory.newInstance().newDocumentBuilder();
            return builder.parse(new ByteArrayInputStream(stringWriter.toString().getBytes()));
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return null;
        }
    }
    private static Template  getTemplate(String templateName){
        Template tp;
        try {
            configurer.setTemplateLoader(loader);
            tp = configurer.getTemplate(templateName);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return null;
        }
        return tp;
    }

}
