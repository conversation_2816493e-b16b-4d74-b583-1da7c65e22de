package com.ruoyi.scwt.works.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @date 2021-10-12 14:27:33
 */
@Data
@TableName("evidence_owner")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "")
public class EvidenceOwner extends Model<EvidenceOwner> {

    private static final long serialVersionUID = 1L;

    @TableId(type= IdType.AUTO)
    @ApiModelProperty(value="")
    private Long ownerId;
    @NotBlank(message = "著作权人类型不能为空")
    @ApiModelProperty(value="著作权人类型")
    private String ownerType;
    @NotBlank(message = "证件类型不能为空")
    @ApiModelProperty(value="证件类型")
    private String idType;
    @NotBlank(message = "著作权人名称不能为空")
    @ApiModelProperty(value="名称")
    private String ownerName;
    @NotBlank(message = "著作权人证件号不能为空")
    @ApiModelProperty(value="证件号")
    private String idNo;
    @ApiModelProperty(value="法人名称")
    private String legalName;
    @ApiModelProperty(value="")
    private LocalDateTime createDate;
    @ApiModelProperty(value="")
    private String createStaff;
    @ApiModelProperty(value="")
    private LocalDateTime updateDate;
    @ApiModelProperty(value="")
    private String updateStaff;
    @ApiModelProperty(value="")
    private String statusCd;
    @ApiModelProperty(value="")
    private LocalDateTime statusDate;
    @ApiModelProperty(value="SOP著作权人ID",hidden = true)
    private String SopOwnerId;
}
