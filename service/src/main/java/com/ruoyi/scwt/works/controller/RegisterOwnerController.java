package com.ruoyi.scwt.works.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.scwt.common.controller.AliIdentifyController;
import com.ruoyi.scwt.common.dto.AliIdentifyDto;
import com.ruoyi.scwt.file.entity.AttachmentInfo;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import com.ruoyi.scwt.identify.service.IdentifyService;
import com.ruoyi.scwt.works.constant.OwnerConstants;
import com.ruoyi.scwt.works.constant.WorksConstants;
import com.ruoyi.scwt.works.dto.EvidenceOwnerDto;
import com.ruoyi.scwt.works.dto.RegisterOwnerDto;
import com.ruoyi.scwt.works.entity.EvidenceOwner;
import com.ruoyi.scwt.works.entity.RegisterOwner;
import com.ruoyi.scwt.works.service.EvidenceOwnerService;
import com.ruoyi.scwt.works.service.RegisterOwnerService;
import com.ruoyi.scwt.works.service.WorksOwnerRelService;
import com.ruoyi.scwt.works.vo.RegisterOwnerVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.security.acl.Owner;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


/**
 * 
 *
 * <AUTHOR>
 * @date 2021-10-12 14:27:33
 */
@RestController
@AllArgsConstructor
@RequestMapping("/registerOwner")
@Api(value = "registerOwner", tags = "登记著作权人管理")
public class RegisterOwnerController extends BaseController {

    private final RegisterOwnerService registerOwnerService;

    private final AliIdentifyController aliIdentifyController;

    private final WorksOwnerRelService worksOwnerRelService;

    private final AttachmentInfoService attachmentInfoService;

    private final ApplicationEventPublisher eventPublisher;

    private final IdentifyService identifyService;

    /**
     * 分页查询
     * @param dto 分页对象
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public R getFileEvidenceOwnerPage(RegisterOwnerDto dto) {
        LambdaQueryWrapper<RegisterOwner> lambda = Wrappers.<RegisterOwner>query().lambda()
                .eq(StringUtils.isNotEmpty(dto.getOwnerType()),RegisterOwner::getOwnerType,dto.getOwnerType())
                .eq(StringUtils.isNotEmpty(dto.getIdType()),RegisterOwner::getIdType,dto.getIdType())
                .eq(RegisterOwner::getStatusCd, OwnerConstants.OWNER_TAKE_EFFECT)
                .like(StringUtils.isNotEmpty(dto.getOwnerName()),RegisterOwner::getOwnerName,dto.getOwnerName())
                .ge(ObjectUtil.isNotEmpty(dto.getStartDate()), RegisterOwner::getCreateDate,dto.getStartDate())
                .le(ObjectUtil.isNotEmpty(dto.getEndDate()), RegisterOwner::getCreateDate,dto.getEndDate())
                .eq(!SecurityUtils.isAdmin(getUserId()),RegisterOwner::getCreateStaff, getUserId())
                .orderByAsc(RegisterOwner::getCreateDate);
        startPage();
        List<RegisterOwner> list = registerOwnerService.list(lambda);
        return R.ok(getDataTable(list));
    }


    /**
     * 通过id查询
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @GetMapping
    public R getById(Long id) {
        RegisterOwner registerOwner = registerOwnerService.getById(id);
        String[] idCardIds = registerOwner.getIdCard().split(",");
        RegisterOwnerVo vo=new RegisterOwnerVo();
        BeanUtils.copyProperties(registerOwner,vo);
        List<AttachmentInfo> idCardList = new ArrayList<>();
        for (String idCardId : idCardIds) {
            idCardList.add(attachmentInfoService.getAtt(Long.valueOf(idCardId)));
        }
        vo.setIdCardList(idCardList);
        if (StringUtils.isNotEmpty(registerOwner.getOtherFiles())){
            String[] otherFiles = registerOwner.getOtherFiles().split(",");
            List<AttachmentInfo> otherFileList = new ArrayList<>();
            for (String otherFile : otherFiles) {
                otherFileList.add(attachmentInfoService.getAtt(Long.valueOf(otherFile)));
            }
            vo.setOtherFileList(otherFileList);
        }
        return R.ok(vo);
    }

    /**
     * 新增
     * @param registerOwner
     * @return R
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping
    public R save(@Validated @RequestBody RegisterOwner registerOwner) {
        registerOwner.setCreateStaff(String.valueOf(getUserId()));
        return registerOwnerService.insert(registerOwner);
    }

    /**
     * 修改
     * @param registerOwner
     * @return R
     */
    @ApiOperation(value = "修改", notes = "修改")
    @PutMapping
    public R updateById(@Validated @RequestBody RegisterOwner registerOwner) {
        Long count = worksOwnerRelService.selectCountByOwnerId(WorksConstants.WORKS_TYPE_REGISTER, registerOwner.getOwnerId());
        if (count > 0){
            return R.fail("该存证著作权人正在使用中，无法修改");
        }
        registerOwner.setUpdateStaff(String.valueOf(SecurityUtils.getUserId()));
        registerOwner.setUpdateDate(LocalDateTime.now());
        return R.ok(registerOwnerService.updateById(registerOwner));
    }
    /**
     * 状态修改
     * @return R
     */
    @ApiOperation(value = "状态修改", notes = "状态修改")
    @DeleteMapping
    public R updateStatusCd(Long ownerId) {
        RegisterOwner one = registerOwnerService.getOne(Wrappers.<RegisterOwner>lambdaQuery()
                .eq(RegisterOwner::getOwnerId, ownerId)
                .eq(!SecurityUtils.isAdmin(getUserId()), RegisterOwner::getCreateStaff, getUserId()));
        if (ObjectUtil.isEmpty(one)){
            return R.fail("无权限删除");
        }
        Long count = worksOwnerRelService.selectCountByOwnerId(WorksConstants.WORKS_TYPE_REGISTER,ownerId);
        if (count > 0){
            return R.fail("该存证著作权人正在使用中，无法删除");
        }
        one.setStatusCd(OwnerConstants.OWNER_INVALID);
        one.setStatusDate(LocalDateTime.now());
        one.setUpdateStaff(String.valueOf(SecurityUtils.getUserId()));
        one.setUpdateDate(LocalDateTime.now());
        return R.ok(registerOwnerService.updateById(one));
    }

}
