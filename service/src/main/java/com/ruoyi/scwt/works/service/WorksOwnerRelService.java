package com.ruoyi.scwt.works.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.scwt.works.entity.EvidenceOwner;
import com.ruoyi.scwt.works.entity.RegisterOwner;
import com.ruoyi.scwt.works.entity.WorksOwnerRel;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
public interface WorksOwnerRelService extends IService<WorksOwnerRel> {

    Long selectCountByOwnerId(String worksTypeEvidence, Long ownerId);

    void insertOwnerRel(Long worksId, String ownerIds, String worksTypeRegister);

    List<RegisterOwner> getRegisterOwnerList(Long worksId);

    List<EvidenceOwner> getEvidenceOwnerList(Long worksId);
}
