package com.ruoyi.scwt.works.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.scwt.works.entity.WorksAuthor;
import com.ruoyi.scwt.works.entity.WorksEvidence;
import com.ruoyi.scwt.works.mapper.WorksAuthorMapper;
import com.ruoyi.scwt.works.mapper.WorksEvidenceMapper;
import com.ruoyi.scwt.works.service.WorksAuthorService;
import com.ruoyi.scwt.works.service.WorksEvidenceService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Service
public class WorksAuthorServiceImpl extends ServiceImpl<WorksAuthorMapper, WorksAuthor> implements WorksAuthorService {

}
