package com.ruoyi.scwt.works.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.scwt.file.entity.AttachmentInfo;
import com.ruoyi.scwt.works.entity.EvidenceOwner;
import com.ruoyi.scwt.works.entity.RegisterOwner;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 作品与著作权人映射表
 *
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Data
public class WorksEvidenceVo {

    @ApiModelProperty(value="ID")
    private Long worksId;

    @ApiModelProperty(value="受理号")
    @Excel(name = "受理号", prompt = "受理号")
    private String acceptanceNumber;

    @ApiModelProperty(value="作品名称")
    @Excel(name = "作品名称", prompt = "作品名称")
    private String worksName;

    @ApiModelProperty(value="作品HASH")
    private String worksHash;

    @ApiModelProperty(value="作品样本文件")
    private AttachmentInfo worksSampleFile;

    /**
     * 上链ID
     */
    @ApiModelProperty(value="上链ID")
    private String blockchainId;
    /**
     * 上链时间
     */
    @ApiModelProperty(value="上链时间")
    private String blockchainTime;
    /**
     * 上链HASH
     */
    @ApiModelProperty(value="上链HASH")
    private String blockchainHash;
    /**
     * 存证证书地址
     */
    @ApiModelProperty(value="存证证书地址")
    private String certificateUrl;


    @ApiModelProperty(value="状态",hidden = true)
    @Excel(name = "状态", readConverterExp = "1000=存证完成,1104=待支付,1200=存证中")
    private String statusCd;

    @ApiModelProperty(value="创建时间",hidden = true)
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDate;

    @ApiModelProperty(value="著作权人名称")
    @Excel(name = "著作权人名称", prompt = "著作权人名称")
    @TableField(exist = false)
    private String ownerNames;

    @ApiModelProperty(value="著作权人列表")
    private List<EvidenceOwner> ownerList;

    @ApiModelProperty(value="授时时间")
    private String tsaTime;
    }
