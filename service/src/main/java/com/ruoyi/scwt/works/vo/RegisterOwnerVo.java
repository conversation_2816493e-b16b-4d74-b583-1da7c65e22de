package com.ruoyi.scwt.works.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ruoyi.scwt.file.entity.AttachmentInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @date 2021-10-12 14:27:33
 */
@Data
public class RegisterOwnerVo {

    @ApiModelProperty(value="")
    private Long ownerId;

    @ApiModelProperty(value="著作权人类型")
    private String ownerType;

    @ApiModelProperty(value="证件类型")
    private String idType;

    @ApiModelProperty(value="名称")
    private String ownerName;

    @ApiModelProperty(value="证件号")
    private String idNo;

    @ApiModelProperty(value="著作权人证件照")
    private List<AttachmentInfo> idCardList;

    @ApiModelProperty(value="著作权人其他证明材料")
    private List<AttachmentInfo> otherFileList;

    @ApiModelProperty(value="法人名称")
    private String legalName;

    @NotBlank(message = "国籍不能为空")
    @ApiModelProperty(value="国籍")
    private String country;

    @NotBlank(message = "省不能为空")
    @ApiModelProperty(value="省")
    private String province;

    @NotBlank(message = "市不能为空")
    @ApiModelProperty(value="市")
    private String city;

    @NotBlank(message = "地址不能为空")
    @ApiModelProperty(value="地址")
    private String address;


    @ApiModelProperty(value="")
    private String createStaff;
    @ApiModelProperty(value="")
    private String statusCd;
}
