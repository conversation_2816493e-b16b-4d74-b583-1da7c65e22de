package com.ruoyi.scwt.works.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.scwt.works.entity.EvidenceOwner;
import com.ruoyi.scwt.works.entity.RegisterOwner;

/**
 *
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
public interface RegisterOwnerService extends IService<RegisterOwner> {

    RegisterOwner addSopOwner(RegisterOwner registerOwner);

    R insert(RegisterOwner registerOwner);
}
