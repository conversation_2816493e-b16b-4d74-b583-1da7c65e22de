package com.ruoyi.scwt.works.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 作品与著作权人映射表
 *
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Data
public class WorksRegisterDto {

    @ApiModelProperty(value="ID")
    private Long worksId;

    @ApiModelProperty(value="受理号")
    private String acceptanceNumber;

    @ApiModelProperty(value="作品名称")
    private String worksName;

    @ApiModelProperty(value="作品类型")
    private String worksType;

    @ApiModelProperty(value="著作权人名称")
    private String ownerName;

    @ApiModelProperty(value="创作性质")
    private String inditeNature;

    @ApiModelProperty(value="状态")
    private String statusCd;

    @ApiModelProperty(value="作品归属情况")
    private String worksBelongType;

    @ApiModelProperty(value="权利取得方式")
    private String copyrightObtainChannel;

    @ApiModelProperty(value="开始时间")
    private LocalDateTime startDate;

    @ApiModelProperty(value="结束时间")
    private LocalDateTime endDate;


    }
