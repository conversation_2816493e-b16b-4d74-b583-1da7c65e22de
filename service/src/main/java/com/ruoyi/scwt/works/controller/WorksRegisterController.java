package com.ruoyi.scwt.works.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.scwt.common.constant.CommonConstants;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import com.ruoyi.scwt.works.constant.OwnerConstants;
import com.ruoyi.scwt.works.constant.WorksConstants;
import com.ruoyi.scwt.works.dto.WorksEvidenceDto;
import com.ruoyi.scwt.works.dto.WorksRegisterDto;
import com.ruoyi.scwt.works.entity.*;
import com.ruoyi.scwt.works.mapper.WorksNTSCMapper;
import com.ruoyi.scwt.works.service.*;
import com.ruoyi.scwt.works.vo.WorksEvidenceVo;
import com.ruoyi.scwt.works.vo.WorksRegisterVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2021-10-12 14:27:33
 */
@RestController
@AllArgsConstructor
@RequestMapping("/worksRegister")
@Api(value = "worksRegister", tags = "登记作品管理")
public class WorksRegisterController extends BaseController {

    private final WorksNTSCMapper worksNTSCMapper;

    private final WorksRegisterService worksRegisterService;

    private final WorksOwnerRelService worksOwnerRelService;

    private final RegisterOwnerService registerOwnerService;

    private final WorksAuthorService worksAuthorService;

    /**
     * 分页查询
     *
     * @param dto 分页对象
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public R getFileEvidenceOwnerPage(WorksRegisterDto dto) {
        List<Long> worksIds=new ArrayList<>();
        if (StringUtils.isNotEmpty(dto.getOwnerName())){
            List<Long> ownerIds = registerOwnerService.list(Wrappers.<RegisterOwner>query().lambda().like(RegisterOwner::getOwnerName, dto.getOwnerName()).eq(RegisterOwner::getStatusCd, OwnerConstants.OWNER_TAKE_EFFECT)).stream().map(RegisterOwner::getOwnerId).collect(Collectors.toList());
            worksIds = worksOwnerRelService.list(Wrappers.<WorksOwnerRel>query().lambda().in(WorksOwnerRel::getOwnerId, ownerIds)).stream().map(WorksOwnerRel::getWorksId).collect(Collectors.toList());
        }
        LambdaQueryWrapper<WorksRegister> lambda = Wrappers.<WorksRegister>query().lambda()
                .like(StringUtils.isNotEmpty(dto.getWorksName()), WorksRegister::getWorksName, dto.getWorksName())
                .ge(ObjectUtil.isNotEmpty(dto.getStartDate()), WorksRegister::getCreateDate, dto.getStartDate())
                .le(ObjectUtil.isNotEmpty(dto.getEndDate()), WorksRegister::getCreateDate, dto.getEndDate())
                .eq(!SecurityUtils.isAdmin(getUserId()), WorksRegister::getCreateStaff, getUserId())
                .eq(StringUtils.isNotEmpty(dto.getStatusCd()), WorksRegister::getStatusCd, dto.getStatusCd())
                .ne(StringUtils.isEmpty(dto.getStatusCd()),WorksRegister::getStatusCd, WorksConstants.WORKS_DELETE)
                .eq(StringUtils.isNotEmpty(dto.getWorksType()), WorksRegister::getWorksType, dto.getWorksType())
                .eq(StringUtils.isNotEmpty(dto.getInditeNature()), WorksRegister::getInditeNature, dto.getInditeNature())
                .eq(StringUtils.isNotEmpty(dto.getWorksBelongType()), WorksRegister::getWorksBelongType, dto.getWorksBelongType())
                .eq(StringUtils.isNotEmpty(dto.getCopyrightObtainChannel()), WorksRegister::getCopyrightObtainChannel, dto.getCopyrightObtainChannel())
                .in(worksIds.size()>0,WorksRegister::getWorksId,worksIds)
                .orderByDesc(WorksRegister::getCreateDate);
        startPage();
        List<WorksRegister> list = worksRegisterService.list(lambda);
        List<WorksRegisterVo> voList =  list.stream().map(item -> worksRegisterService.setVoData(item, CommonConstants.VO_DATA_PAGE)).collect(Collectors.toList());
        TableDataInfo dataTable = getDataTable(list);
        dataTable.setRows(voList);
        return R.ok(dataTable);
    }


    @ApiOperation(value = "excel导出", notes = "excel导出")
    @PostMapping("/page/export")
    public void getRegisterExcel(HttpServletResponse response, WorksRegisterDto dto) {
        List<Long> worksIds=new ArrayList<>();
        if (StringUtils.isNotEmpty(dto.getOwnerName())){
            List<Long> ownerIds = registerOwnerService.list(Wrappers.<RegisterOwner>query().lambda().like(RegisterOwner::getOwnerName, dto.getOwnerName()).eq(RegisterOwner::getStatusCd, OwnerConstants.OWNER_TAKE_EFFECT)).stream().map(RegisterOwner::getOwnerId).collect(Collectors.toList());
            worksIds = worksOwnerRelService.list(Wrappers.<WorksOwnerRel>query().lambda().in(WorksOwnerRel::getOwnerId, ownerIds)).stream().map(WorksOwnerRel::getWorksId).collect(Collectors.toList());
        }
        LambdaQueryWrapper<WorksRegister> lambda = Wrappers.<WorksRegister>query().lambda()
                .like(StringUtils.isNotEmpty(dto.getWorksName()), WorksRegister::getWorksName, dto.getWorksName())
                .ge(ObjectUtil.isNotEmpty(dto.getStartDate()), WorksRegister::getCreateDate, dto.getStartDate())
                .le(ObjectUtil.isNotEmpty(dto.getEndDate()), WorksRegister::getCreateDate, dto.getEndDate())
                .eq(!SecurityUtils.isAdmin(getUserId()), WorksRegister::getCreateStaff, getUserId())
                .eq(StringUtils.isNotEmpty(dto.getStatusCd()), WorksRegister::getStatusCd, dto.getStatusCd())
                .ne(StringUtils.isEmpty(dto.getStatusCd()),WorksRegister::getStatusCd, WorksConstants.WORKS_DELETE)
                .eq(StringUtils.isNotEmpty(dto.getWorksType()), WorksRegister::getWorksType, dto.getWorksType())
                .eq(StringUtils.isNotEmpty(dto.getInditeNature()), WorksRegister::getInditeNature, dto.getInditeNature())
                .eq(StringUtils.isNotEmpty(dto.getWorksBelongType()), WorksRegister::getWorksBelongType, dto.getWorksBelongType())
                .eq(StringUtils.isNotEmpty(dto.getCopyrightObtainChannel()), WorksRegister::getCopyrightObtainChannel, dto.getCopyrightObtainChannel())
                .in(worksIds.size()>0,WorksRegister::getWorksId,worksIds)
                .orderByDesc(WorksRegister::getCreateDate);
//        startPage();
        List<WorksRegister> list = worksRegisterService.list(lambda);
        List<WorksRegisterVo> voList =  list.stream().map(item -> worksRegisterService.setVoData(item, CommonConstants.VO_DATA_PAGE)).collect(Collectors.toList());
        ExcelUtil<WorksRegisterVo> util = new ExcelUtil<WorksRegisterVo>(WorksRegisterVo.class);
        util.exportExcel(response, voList, "订单数据");
    }



    /**
     * 通过id查询
     *
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @GetMapping
    public R getById(Long id) {
        WorksRegister worksRegister = worksRegisterService.getById(id);
        WorksRegisterVo worksRegisterVo = worksRegisterService.setVoData(worksRegister, CommonConstants.VO_DATA_DETAIL);
        return R.ok(worksRegisterVo);
    }

    /**
     * 新增
     *
     * @param worksRegister
     * @return R
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping
    public R save(@Validated @RequestBody WorksRegister worksRegister) {
        worksRegister.setCreateStaff(String.valueOf(getUserId()));
        return R.ok(worksRegisterService.saveWorksRegister(worksRegister));
    }

    /**
     * 修改
     *
     * @param worksRegister
     * @return R
     */
    @ApiOperation(value = "修改", notes = "修改")
    @PutMapping
    public R updateById(@Validated @RequestBody WorksRegister worksRegister) {
        String userId = String.valueOf(SecurityUtils.getUserId());
        Long count=worksRegisterService.getRepeatName(worksRegister.getWorksName(),userId);
        if (count>0) {
            return R.fail("作品名称重复");
        }
        worksRegister.setUpdateStaff(String.valueOf(SecurityUtils.getUserId()));
        worksRegister.setUpdateDate(LocalDateTime.now());
        worksRegister.setStatusCd(WorksConstants.WORKS_REVIEW);
        worksRegister.setStatusDate(LocalDateTime.now());
        worksOwnerRelService.remove(Wrappers.<WorksOwnerRel>query().lambda().eq(WorksOwnerRel::getWorksId, worksRegister.getWorksId()));
        worksOwnerRelService.insertOwnerRel(worksRegister.getWorksId(),worksRegister.getOwnerIds(),WorksConstants.WORKS_TYPE_REGISTER);
        worksAuthorService.remove(Wrappers.<WorksAuthor>query().lambda().eq(WorksAuthor::getWorksId, worksRegister.getWorksId()));
        worksRegister.getAuthorList().forEach(item-> {
            item.setWorksId(worksRegister.getWorksId());
            worksAuthorService.save(item);
        });
        worksRegisterService.updateById(worksRegister);
        worksRegisterService.addWorksRegister(worksRegister);
        return R.ok("修改成功");
    }

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping
    public R updateStatusCd(Long id) {
        Long userId = SecurityUtils.getUserId();
        WorksRegister worksRegister=worksRegisterService.isUpdate(id,userId);
        if (ObjectUtil.isEmpty(worksRegister)) {
            return R.fail("无权限删除");
        }
        worksRegister.setUpdateStaff(String.valueOf(userId));
        worksRegister.setUpdateDate(LocalDateTime.now());
        worksRegister.setStatusCd(WorksConstants.WORKS_DELETE);
        worksRegister.setStatusDate(LocalDateTime.now());
        worksRegisterService.updateById(worksRegister);
        return R.ok("删除成功");
    }
    @ApiOperation(value = "获取存证证书地址", notes = "获取存证证书地址")
    @GetMapping("/getCertificateById")
    public R getCertificateById(Long worksId) {
        return worksRegisterService.getCertificateById(worksId);
    }
    @ApiOperation(value = "获取登记证书地址", notes = "获取登记证书地址")
    @GetMapping("/getRegisterCertificateById")
    public R getRegisterCertificateById(Long worksId) {
        return worksRegisterService.getRegisterCertificateById(worksId);
    }
    @GetMapping("/downloadCert")
    @ApiOperation(value = "下载时间戳证书", notes = "下载时间戳证书")
    public void downloadCert(Long worksId, HttpServletResponse response) {
        OutputStream os = null;
        try {
            response.setHeader(HttpHeaders.CONTENT_TYPE,"application/json;charset=UTF-8");
            os = response.getOutputStream();
            os.write(worksNTSCMapper.selectOne(Wrappers.<WorksNTSC>lambdaQuery().eq(WorksNTSC::getWorksId, worksId).eq(WorksNTSC::getType, WorksConstants.WORKS_TYPE_EVIDENCE)).getRes());
            os.flush();
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            try {
                assert os != null;
                os.close();
            } catch (IOException ioException) {
                ioException.printStackTrace();
            }
        }
    }

    @ApiOperation(value = "手动推送编辑后的登记作品", notes = "手动推送编辑后的登记作品")
    @GetMapping("/updateToSop")
    public void updateToSop(Long worksId) {
        WorksRegister worksRegister = worksRegisterService.getById(worksId);
        worksRegisterService.addWorksRegister(worksRegister);
    }

}
