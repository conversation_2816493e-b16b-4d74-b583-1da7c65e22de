package com.ruoyi.scwt.works.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 作品与著作权人映射表
 *
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Data
public class WorksEvidenceDto  {

    @ApiModelProperty(value="ID")
    private Long worksId;

    @ApiModelProperty(value="受理号")
    private String acceptanceNumber;

    @ApiModelProperty(value="作品名称")
    private String worksName;

    @ApiModelProperty(value="著作权人名称")
    private String ownerName;

    @ApiModelProperty(value="状态")
    private String statusCd;

    @ApiModelProperty(value="开始时间")
    private LocalDateTime startDate;

    @ApiModelProperty(value="结束时间")
    private LocalDateTime endDate;


    }
