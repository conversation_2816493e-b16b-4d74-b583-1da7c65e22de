package com.ruoyi.scwt.works.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.scwt.common.constant.CommonConstants;
import com.ruoyi.scwt.common.util.AESUtils;
import com.ruoyi.scwt.file.entity.AttachmentInfo;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import com.ruoyi.scwt.identify.entity.Identify;
import com.ruoyi.scwt.identify.mapper.IdentifyMapper;
import com.ruoyi.scwt.sop.DataProcessing.Bmark2Sop;
import com.ruoyi.scwt.sop.controller.EvidenceAndRegisterController;
import com.ruoyi.scwt.sop.dto.WorkByAcceptNoDto;
import com.ruoyi.scwt.sop.dto.WorksInstDto;
import com.ruoyi.scwt.works.constant.WorksConstants;
import com.ruoyi.scwt.works.dto.EvidenceCertificateDto;
import com.ruoyi.scwt.works.entity.*;
import com.ruoyi.scwt.works.mapper.WorksNTSCMapper;
import com.ruoyi.scwt.works.mapper.WorksRegisterMapper;
import com.ruoyi.scwt.works.service.*;
import com.ruoyi.scwt.works.util.PdfUtil;
import com.ruoyi.scwt.works.vo.WorksRegisterVo;
import com.ruoyi.system.mapper.SysUserMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import java.time.temporal.ChronoUnit;
/**
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Service
@Slf4j
//@AllArgsConstructor
public class WorksRegisterServiceImpl extends ServiceImpl<WorksRegisterMapper, WorksRegister> implements WorksRegisterService {

    @Autowired
    private Bmark2Sop bmark2Sop;

    @Autowired
    private EvidenceAndRegisterController evidenceAndRegisterController;
    @Resource
    private  WorksNTSCMapper worksNTSCMapper;

    @Autowired
    private AttachmentInfoService attachmentInfoService;

    @Autowired
    private WorksAuthorService worksAuthorService;

    @Autowired
    private WorksOwnerRelService worksOwnerRelService;

    @Autowired
    private RegisterOwnerService registerOwnerService;
    @Resource
    private  IdentifyMapper identifyMapper;
    @Resource
    private  SysUserMapper sysUserMapper;
    @Value("${font.file}")
    private String fontFile;


    @Override
    public Long getRepeatName(String worksName, String userId) {
        long count = baseMapper.selectCount(Wrappers.<WorksRegister>lambdaQuery()
                .eq(WorksRegister::getWorksName, worksName)
                .eq(WorksRegister::getCreateStaff, userId)
                .and(i -> i.eq(WorksRegister::getStatusCd, WorksConstants.WORKS_DONE).or()
                        .eq(WorksRegister::getStatusCd, WorksConstants.WORKS_REVIEW).or()
                        .eq(WorksRegister::getStatusCd, WorksConstants.WORKS_EVIDENCE).or()
                        .eq(WorksRegister::getStatusCd, WorksConstants.WORKS_REGISTER_PROOF).or()
                        .eq(WorksRegister::getStatusCd, WorksConstants.WORKS_REGISTER_INFO).or()
                        .eq(WorksRegister::getStatusCd, WorksConstants.WORKS_REGISTER_PAY)));
        return count;
    }

    @Override
    public WorksRegisterVo setVoData(WorksRegister worksRegister, String voDataType) {
        WorksRegisterVo worksRegisterVo = new WorksRegisterVo();
        BeanUtils.copyProperties(worksRegister, worksRegisterVo);
        List<WorksOwnerRel> list = worksOwnerRelService.list(Wrappers.<WorksOwnerRel>query().lambda().eq(WorksOwnerRel::getWorksId, worksRegister.getWorksId()).eq(WorksOwnerRel::getType, WorksConstants.WORKS_TYPE_REGISTER));
        List<RegisterOwner> ownerList=new ArrayList<>();
        if (ObjectUtil.isNotEmpty(list)){
            for (WorksOwnerRel s : list) {
                RegisterOwner byId = registerOwnerService.getById(s.getOwnerId());
                ownerList.add(byId);
            }
        }
        worksRegisterVo.setOwnerNames(ownerList.stream().map(RegisterOwner::getOwnerName).collect(Collectors.joining(",")));
        if (ObjectUtil.isNotEmpty(worksRegister.getSopAcceptanceNumber()))worksRegisterVo.setAcceptanceNumber(worksRegister.getSopAcceptanceNumber().replace("SOP", "R"));
        worksRegisterVo.setOwnerList(ownerList);
        if (voDataType.equals(CommonConstants.VO_DATA_DETAIL)) {
            worksRegisterVo.setWorksSampleFile(attachmentInfoService.getAtt(worksRegister.getWorksFileId()));
            if (ObjectUtil.isNotEmpty(worksRegister.getPowerGuaranteeId())) {
                worksRegisterVo.setPowerGuarantee(attachmentInfoService.getAtt(worksRegister.getPowerGuaranteeId()));
            }
            worksRegisterVo.setAuthorList(worksAuthorService.list(Wrappers.<WorksAuthor>query().lambda().eq(WorksAuthor::getWorksId, worksRegister.getWorksId())));
            if (StringUtils.isNotEmpty(worksRegisterVo.getOtherMaterials())){
                for (String s : worksRegister.getOtherMaterialsId().split(",")) {
                    worksRegisterVo.getOtherMaterials().add(attachmentInfoService.getAtt(Long.parseLong(s)));
                }
            }
        }
        return worksRegisterVo;
    }

    @Override
    public WorksRegister saveWorksRegister(WorksRegister worksRegister) {
        WorksRegister inst=new WorksRegister();
        switch (worksRegister.getStep()) {
            case WorksConstants.WORKS_REGISTER_UPLOAD:
                if (ObjectUtil.isEmpty(worksRegister.getWorksFileId())) {
                    throw new RuntimeException("作品样本文件不能为空");
                }
                if (ObjectUtil.isEmpty(worksRegister.getWorksType())) {
                    throw new RuntimeException("作品类型不能为空");
                }
                inst.setWorksFileId(worksRegister.getWorksFileId());
                inst.setWorksType(worksRegister.getWorksType());
                inst.setWorksHash(attachmentInfoService.getById(worksRegister.getWorksFileId()).getHashCode());
                inst.setStatusCd(WorksConstants.WORKS_REGISTER_INFO);
                if (ObjectUtil.isNotEmpty(worksRegister.getWorksId())) {
                    inst.setWorksId(worksRegister.getWorksId());
                    baseMapper.updateById(inst);
                    return inst;
                }
                break;
            case WorksConstants.WORKS_REGISTER_INFO:
                if (ObjectUtil.isEmpty(worksRegister.getWorksId())) {
                    throw new RuntimeException("作品ID不能为空");
                }
                if (StringUtils.isEmpty(worksRegister.getWorksName())) {
                    throw new RuntimeException("作品名称不能为空");
                }
                if (StringUtils.isEmpty(worksRegister.getWorksType())) {
                    throw new RuntimeException("作品类型不能为空");
                }
                if (StringUtils.isEmpty(worksRegister.getInditeNature())) {
                    throw new RuntimeException("创作性质不能为空");
                }
                if (StringUtils.isEmpty(worksRegister.getWorksBelongType())) {
                    throw new RuntimeException("作品归属情况不能为空");
                }
                if (StringUtils.isEmpty(worksRegister.getCopyrightObtainChannel())) {
                    throw new RuntimeException("权利取得方式不能为空");
                }
                if (ObjectUtil.isEmpty(worksRegister.getInditeDoneTime())) {
                    throw new RuntimeException("创作完成时间不能为空");
                }
                if (StringUtils.isEmpty(worksRegister.getInditeDonePlace())) {
                    throw new RuntimeException("创作完成地点不能为空");
                }
                if (StringUtils.isEmpty(worksRegister.getWorksAbstract())) {
                    throw new RuntimeException("作品简介不能为空");
                }
                if (StringUtils.isEmpty(worksRegister.getCopyrightOwnRange())) {
                    throw new RuntimeException("权利拥有方式不能为空");
                }
                if (StringUtils.isEmpty(worksRegister.getOwnerIds())) {
                    throw new RuntimeException("著作权人不能为空");
                }
                if (ObjectUtil.isEmpty(worksRegister.getAuthorList())) {
                    throw new RuntimeException("作者不能为空");
                }
//                if (StringUtils.isEmpty(worksRegister.getWorksSignature())) {
//                    throw new RuntimeException("作品署名不能为空");
//                }
                if (ObjectUtil.isEmpty(worksRegister.getDistributeRight())) {
                    throw new RuntimeException("作品分发权不能为空");
                }
                if (ObjectUtil.isNotEmpty(worksRegister.getPublishDate())) {
                    inst.setPublishDate(worksRegister.getPublishDate().plus(1, ChronoUnit.DAYS));
                }
                if (StringUtils.isNotEmpty(worksRegister.getPublishPlace())) {
                    inst.setPublishPlace(worksRegister.getPublishPlace());
                }
                inst.setWorksId(worksRegister.getWorksId());
                inst.setWorksName(worksRegister.getWorksName());
                inst.setWorksType(worksRegister.getWorksType());
                inst.setInditeNature(worksRegister.getInditeNature());
                inst.setWorksBelongType(worksRegister.getWorksBelongType());
                inst.setCopyrightObtainChannel(worksRegister.getCopyrightObtainChannel());
                inst.setInditeDoneTime(worksRegister.getInditeDoneTime().plus(1, ChronoUnit.DAYS));
                inst.setInditeDonePlace(worksRegister.getInditeDonePlace());
                inst.setWorksAbstract(worksRegister.getWorksAbstract());
                inst.setCopyrightOwnRange(worksRegister.getCopyrightOwnRange());
                inst.setWorksSignature(worksRegister.getAuthorList().stream().map(WorksAuthor::getSignature).collect(Collectors.joining(",")));
                inst.setDistributeRight(worksRegister.getDistributeRight());
                inst.setOwnerIds(worksRegister.getOwnerIds());
                inst.setAuthorList(worksRegister.getAuthorList());
                inst.setStatusCd(WorksConstants.WORKS_REGISTER_PROOF);

                worksOwnerRelService.remove(Wrappers.<WorksOwnerRel>query().lambda().eq(WorksOwnerRel::getWorksId, worksRegister.getWorksId()).eq(WorksOwnerRel::getType, WorksConstants.WORKS_TYPE_REGISTER));
                worksOwnerRelService.insertOwnerRel(worksRegister.getWorksId(), worksRegister.getOwnerIds(), WorksConstants.WORKS_TYPE_REGISTER);

                worksAuthorService.remove(Wrappers.<WorksAuthor>query().lambda().eq(WorksAuthor::getWorksId, worksRegister.getWorksId()));
                worksRegister.getAuthorList().forEach(item -> {
                    item.setWorksId(worksRegister.getWorksId());
                    worksAuthorService.save(item);
                });
                break;
            case WorksConstants.WORKS_REGISTER_PROOF:
                if (ObjectUtil.isEmpty(worksRegister.getWorksId())) {
                    throw new RuntimeException("作品ID不能为空");
                }
                if (ObjectUtil.isEmpty(worksRegister.getPowerGuaranteeId())) {
                    throw new RuntimeException("权利保证书不能为空");
                }
//                if (StringUtils.isEmpty(worksRegister.getAgentRegister())) {
//                    throw new RuntimeException("是否代理登记不能为空");
//                }
                if (StringUtils.isNotEmpty(worksRegister.getOtherMaterialsId())) {
                    inst.setOtherMaterialsId(worksRegister.getOtherMaterialsId());
                }
                inst.setWorksId(worksRegister.getWorksId());
                inst.setPowerGuaranteeId(worksRegister.getPowerGuaranteeId());
//                inst.setAgentRegister(worksRegister.getAgentRegister());
                if (!worksRegister.getIsTemp()) {
                    inst.setStatusCd(WorksConstants.WORKS_REVIEW);
                }
                break;
            default:
                throw new RuntimeException("未知步骤");
        }
        if (ObjectUtil.isNotEmpty(worksRegister.getWorksId())){
            inst.setUpdateStaff(worksRegister.getCreateStaff());
            inst.setUpdateDate(LocalDateTime.now());
            inst.setStatusDate(LocalDateTime.now());
            baseMapper.updateById(inst);
            if (worksRegister.getStep().equals(WorksConstants.WORKS_REGISTER_PROOF)){
                WorksRegister register = baseMapper.selectById(worksRegister.getWorksId());
                if (!worksRegister.getIsTemp()) {
                    addWorksRegister(register);
                }
            }
        }else {
            inst.setCreateStaff(worksRegister.getCreateStaff());
            inst.setCreateDate(LocalDateTime.now());
            inst.setStatusDate(LocalDateTime.now());
            baseMapper.insert(inst);
        }
        return inst;
    }

    @Override
    public WorksRegister isUpdate(Long worksId, Long userId) {
        WorksRegister worksRegister = baseMapper.selectOne(Wrappers.<WorksRegister>lambdaQuery()
                .eq(WorksRegister::getWorksId, worksId)
                .eq(!SecurityUtils.isAdmin(userId), WorksRegister::getCreateStaff, userId)
                .and(i -> i.ne(WorksRegister::getStatusCd, WorksConstants.WORKS_DONE).or()
                        .ne(WorksRegister::getStatusCd,WorksConstants.WORKS_REVIEW).or()
                        .ne(WorksRegister::getStatusCd,WorksConstants.WORKS_EVIDENCE).or()
                        .ne(WorksRegister::getStatusCd,WorksConstants.WORKS_REGISTER_UPLOAD).or()
                        .ne(WorksRegister::getStatusCd,WorksConstants.WORKS_REGISTER_INFO).or()
                        .ne(WorksRegister::getStatusCd, WorksConstants.WORKS_REGISTER_PROOF)));
        return worksRegister;
    }

    @Override
    @Async
    public void addWorksRegister(WorksRegister worksRegister) {
//        WorksRegister inst = worksRegisterService.getById(worksId);
//        if (StrUtil.isEmpty(userId)) {
//            userId = inst.getCreateStaff();
//        }
        String userId=worksRegister.getCreateStaff();
        //获取著作权人信息
        List<RegisterOwner> ownersList = worksOwnerRelService.getRegisterOwnerList(worksRegister.getWorksId());
        for (RegisterOwner owner : ownersList) {
            if (StrUtil.isEmpty(owner.getSopOwnerId())) {
                RegisterOwner registerOwner = registerOwnerService.addSopOwner(owner);
                owner.setSopOwnerId(registerOwner.getSopOwnerId());
            }
        }
        // 作者信息
        List<WorksAuthor> authors = worksAuthorService.list(Wrappers.<WorksAuthor>query().lambda().eq(WorksAuthor::getWorksId, worksRegister.getWorksId()));
        // 样本文件
        List<String> sampleFileId = new ArrayList<>();
        String ybwj = bmark2Sop.fileUploadDataJoin(String.valueOf(worksRegister.getWorksFileId()), com.ruoyi.scwt.sop.constant.CommonConstants.FILE_TYPE_YBWJ, userId, "样本文件");
        sampleFileId.add(ybwj);
        // 权力保证书
        List<String> powerGuaranteeId = new ArrayList<>();
        String qlbzs = bmark2Sop.fileUploadDataJoin(String.valueOf(worksRegister.getPowerGuaranteeId()), com.ruoyi.scwt.sop.constant.CommonConstants.FILE_TYPE_QLBZS, userId, "权利保证书");
        powerGuaranteeId.add(qlbzs);
        // 其他文件
        List<String> otherMaterialsId = new ArrayList<>();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(worksRegister.getOtherMaterialsId())) {
            String[] datumPaths = worksRegister.getOtherMaterialsId().split(",");
            for (int i = 0; i < datumPaths.length; i++) {
                String s = bmark2Sop.fileUploadDataJoin(String.valueOf(datumPaths[i]), com.ruoyi.scwt.sop.constant.CommonConstants.FILE_TYPE_QTCL, userId, "其他文件");
                otherMaterialsId.add(s);
            }
        }
        WorksInstDto dto = new WorksInstDto();
        dto.setWorksName(worksRegister.getWorksName());
        dto.setAuthorName(authors.stream().map(WorksAuthor::getAuthorName).collect(Collectors.joining(",")));
        dto.setAuthorSign(authors.stream().map(WorksAuthor::getSignature).collect(Collectors.joining(",")));
        dto.setCopyrightObtainChannel(worksRegister.getCopyrightObtainChannel());
        dto.setCopyrightOwnRange(worksRegister.getCopyrightOwnRange());
        dto.setCreatePlace(StrUtil.isNotEmpty(worksRegister.getInditeDonePlace()) ? worksRegister.getInditeDonePlace() : "成都市");
        dto.setDescription(worksRegister.getWorksAbstract());
        dto.setInditeDoneTime(worksRegister.getInditeDoneTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        dto.setInditeNature(worksRegister.getInditeNature());
        dto.setOtherMaterialsId(String.join(",", otherMaterialsId));
        dto.setOwnersIds(ownersList.stream().map(RegisterOwner::getSopOwnerId).collect(Collectors.joining(",")));
        dto.setPlatformUserId(userId);
        dto.setPowerGuaranteeId(String.join(",", powerGuaranteeId));
        if (ObjectUtil.isNotEmpty(worksRegister.getPublishDate())) {
            dto.setPublishDate(worksRegister.getPublishDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (ObjectUtil.isNotEmpty(worksRegister.getPublishPlace())) {
            dto.setPublishPlace(worksRegister.getPublishPlace());
        }
        dto.setSampleFileId(String.join(",", sampleFileId));
        dto.setWorksBelongType(worksRegister.getWorksBelongType());
//        dto.setWorksCover();
        dto.setWorksType(worksRegister.getWorksType());
        JSONObject jsonObject =new JSONObject();
        if (StrUtil.isEmpty(worksRegister.getSopAcceptanceNumber())) {
            jsonObject = evidenceAndRegisterController.workSave(dto);
        } else {
            jsonObject = evidenceAndRegisterController.workUpdate(dto);
        }
//        System.out.println(jsonObject.toString());
        worksRegister.setSopWorksId(jsonObject.getStr("worksId"));
        worksRegister.setSopAcceptanceNumber(jsonObject.getStr("acceptanceNumber"));
        worksRegister.setWorksHash(jsonObject.getStr("worksHash"));
        baseMapper.updateById(worksRegister);
    }

    @Override
    public void getWorkProgressByAcceptNo(WorksRegister worksRegister) {
//        WorksInst one = worksInstMapper.selectOne(Wrappers.<WorksInst>lambdaQuery().eq(WorksInst::getAcceptanceNumber, dto.getAcceptNo()));
//        if (ObjectUtil.isEmpty(one)) return;
        WorkByAcceptNoDto dto = new WorkByAcceptNoDto(worksRegister.getSopAcceptanceNumber(), worksRegister.getCreateStaff());
        JSONObject jsonObject = evidenceAndRegisterController.getWorkProgressByAcceptNo(dto);
        log.info("SOP进度查询返回数据：{}",jsonObject);
        String statusCd = String.valueOf(jsonObject.get("statusCd"));
        if (StrUtil.equalsAny(statusCd,com.ruoyi.scwt.sop.constant.CommonConstants.FIRST_TRIAL_REJECT,com.ruoyi.scwt.sop.constant.CommonConstants.REEXAMINE_REJECT,com.ruoyi.scwt.sop.constant.CommonConstants.COPYRIGHT_OFFICE_EXAMINE_REJECT)) {
            worksRegister.setStatusCd(WorksConstants.WORKS_REJECT);
            worksRegister.setAuditOpinion(jsonObject.getStr("auditOpinion"));
        }else if (statusCd.equals(com.ruoyi.scwt.sop.constant.CommonConstants.EVIDENCE_FAIL)){
            worksRegister.setStatusCd(WorksConstants.WORKS_FAIL);
        }else if (statusCd.equals(com.ruoyi.scwt.sop.constant.CommonConstants.ENTRY_SUCCESS)){
            worksRegister.setStatusCd(WorksConstants.WORKS_DONE);
            worksRegister.setStatusDate(LocalDateTime.now());
            JSONObject data = evidenceAndRegisterController.WorksRegistrationData(dto);
            worksRegister.setBlockchainHash(data.getStr("blockchainHash"));
            worksRegister.setBlockchainTime(data.getStr("blockchainTime"));
//            worksRegister.setCertificateUrl(data.getStr("certificateUrl"));
            worksRegister.setRegisterCertificateTime(data.getStr("obtainedTime"));
            worksRegister.setRegisterCertificateNo(data.getStr("cerNo"));
            worksRegister.setRegisterCertificateUrl(data.getStr("registrationCertificateUrl"));
            if (StrUtil.isEmpty(worksRegister.getRegisterCertificateUrl())){
                JSONObject jsonObject1 = evidenceAndRegisterController.registrationWorksCertificate(dto);
                worksRegister.setRegisterCertificateUrl(jsonObject1.getStr("certificateUrl"));
            }
            baseMapper.updateById(worksRegister);
            WorksNTSC worksNTSC=new WorksNTSC();
            worksNTSC.setWorksId(worksRegister.getWorksId());
            worksNTSC.setNtscTime(data.getStr("ntscTime"));
            worksNTSC.setRes( data.getStr("ntscRes").getBytes());
            worksNTSC.setType(WorksConstants.WORKS_TYPE_REGISTER);
            worksNTSCMapper.insert(worksNTSC);
        }else if (StrUtil.equalsAny(statusCd, com.ruoyi.scwt.sop.constant.CommonConstants.FIRST_TRIAL,com.ruoyi.scwt.sop.constant.CommonConstants.REEXAMINE,com.ruoyi.scwt.sop.constant.CommonConstants.EVIDENCE,com.ruoyi.scwt.sop.constant.CommonConstants.COPYRIGHT_OFFICE_EXAMINE)) {
            return;
        }
        worksRegister.setStatusDate(LocalDateTime.now());
        baseMapper.updateById(worksRegister);
    }

    @Override
    public R getCertificateById(Long worksId) {
        WorksRegister worksRegister = baseMapper.selectById(worksId);
        if (worksRegister.getStatusCd().equals(WorksConstants.WORKS_DONE)){
            if (StringUtils.isEmpty(worksRegister.getCertificateUrl())){
                String worksCertificate = rigesterWorksCertificate(worksRegister);
                return R.ok(attachmentInfoService.getObjectUrl(worksCertificate));
            }else {
                return R.ok(attachmentInfoService.getObjectUrl(worksRegister.getCertificateUrl()));
            }
        }
        return R.fail("暂未获得存证证书");
    }

    @Override
    public R getRegisterCertificateById(Long worksId) {
        WorksRegister worksRegister = baseMapper.selectById(worksId);
        if (StringUtils.isEmpty(worksRegister.getRegisterCertificateUrl())){
            return R.fail("暂未获得登记证书");
        }
        return R.ok(worksRegister.getRegisterCertificateUrl());
    }

    public String rigesterWorksCertificate(WorksRegister worksRegister) {
        EvidenceCertificateDto dto = new EvidenceCertificateDto();
        dto.setAcceptanceNumber(worksRegister.getAcceptanceNumber());
        dto.setWorksName(worksRegister.getWorksName());
        dto.setWorksHash(worksRegister.getWorksHash().substring(0,49));
        dto.setWorksHashLater(worksRegister.getWorksHash().substring(49));
        dto.setFileNum("1");
        dto.setBlockchainHash(worksRegister.getBlockchainHash().substring(0,49));
        dto.setBlockchainHashLater(worksRegister.getBlockchainHash().substring(49));
        Identify identify = identifyMapper.selectOne(Wrappers.<Identify>lambdaQuery().eq(Identify::getUserId, worksRegister.getCreateStaff()));
        dto.setIdName(identify.getIdName());
//        dto.setIdNo(AESUtils.decrypt(identify.getIdNo()));
        dto.setIdNo(identify.getIdNo());
        WorksNTSC worksNTSC = worksNTSCMapper.selectOne(Wrappers.<WorksNTSC>lambdaQuery().eq(WorksNTSC::getWorksId, worksRegister.getWorksId()).eq(WorksNTSC::getType,WorksConstants.WORKS_TYPE_REGISTER));
        String date = DateUtil.date(Long.parseLong(worksNTSC.getNtscTime())).toString();
        dto.setTsaTime(date + "（UTC/GMT+08:00）");
        SysUser sysUser = sysUserMapper.selectUserById(Long.valueOf(worksRegister.getCreateStaff()));
        dto.setEvidenceAccount(sysUser.getUserName());
        try {
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            PdfUtil.generate(out, "evidence_certificate.ftl", dto, fontFile);
            MultipartFile multipartFile = new MockMultipartFile(worksRegister.getWorksName() + "-存证证书.pdf", worksRegister.getWorksName() + "-存证证书.pdf",
                    "application/pdf", out.toByteArray());
            AttachmentInfo infoVo = attachmentInfoService.upload(multipartFile, "evidenceCertificate", worksRegister.getWorksName() + "-存证证书.pdf", worksRegister.getCreateStaff());
            worksRegister.setCertificateUrl(infoVo.getFilePath());
            baseMapper.updateById(worksRegister);
            return infoVo.getFilePath();
        } catch (Exception e) {
            log.error("下载证书文件失败:{}", e);
            return e.getMessage();
        }
    }
}
