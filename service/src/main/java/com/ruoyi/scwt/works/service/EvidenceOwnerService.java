package com.ruoyi.scwt.works.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.scwt.works.entity.EvidenceOwner;

/**
 *
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
public interface EvidenceOwnerService extends IService<EvidenceOwner> {

    EvidenceOwner getDesensitizationById(Long id);

    EvidenceOwner addSopOwner(EvidenceOwner evidenceOwner);

    R insert(EvidenceOwner evidenceOwner);
}
