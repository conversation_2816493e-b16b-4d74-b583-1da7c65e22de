package com.ruoyi.scwt.works.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 作品指丛书类每本，视频集每集，漫画丛书每本、影集每本、图片、单本小说等，其著作权、出版权都可能有所不同
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Data
@TableName("WORKS_REGISTER")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "作品表")
public class WorksRegister extends Model<WorksRegister> {
private static final long serialVersionUID = 1L;

    /**
     * 作品ID，主键
     */
    @TableId(type= IdType.AUTO)
    @ApiModelProperty(value="作品ID")
//    @NotNull(message = "作品id不能为空")
    private Long worksId;
    /**
     * 受理号
     */
    @ApiModelProperty(value="受理号",hidden = true)
    private String acceptanceNumber;
    /**
     * sop受理号
     */
    @ApiModelProperty(value="sop受理号",hidden = true)
    private String sopAcceptanceNumber;
    /**
     * sop存证作品ID
     */
    @ApiModelProperty(value="sop存证作品ID",hidden = true)
    private String sopWorksId;
    /**
     * 作品名称
     */
    @ApiModelProperty(value="作品名称")
//    @NotBlank(message = "作品名称不能为空")
    private String worksName;
    /**
     * 作品类型
     */
    @ApiModelProperty(value="作品类型")
    private String worksType;
    /**
     * 作品样本文件ID
     */
    @ApiModelProperty(value="作品样本文件ID")
//    @NotBlank(message = "作品样本文件ID不能为空")
    private Long worksFileId;
    /**
     * 权利保证书文件ID
     */
    @ApiModelProperty(value="权利保证书文件ID")
//    @NotBlank(message = "权利保证书文件ID不能为空")
    private Long powerGuaranteeId;
    /**
     * 其他证明材料文件ID
     */
    @ApiModelProperty(value="其他证明材料文件ID，多个以逗号隔开")
    private String otherMaterialsId;
    /**
     * 作品简介
     */
    @ApiModelProperty(value="作品简介")
//    @NotBlank(message = "作品简介不能为空")
    private String worksAbstract;
    /**
     * 创作性质
     */
    @ApiModelProperty(value="创作性质")
//    @NotBlank(message = "创作性质不能为空")
    private String inditeNature;
    /**
     * 作品归属情况
     */
    @ApiModelProperty(value="作品归属情况")
//    @NotBlank(message = "作品归属情况不能为空")
    private String worksBelongType;
    /**
     * 权利取得方式
     */
    @ApiModelProperty(value="权利取得方式")
//    @NotBlank(message = "权利取得方式不能为空")
    private String copyrightObtainChannel;
    /**
     * 权利拥有方式
     */
    @ApiModelProperty(value="权利拥有方式,多个以逗号隔开")
//    @NotBlank(message = "权利取得方式不能为空")
    private String copyrightOwnRange;
    /**
     * 作品HASH
     */
    @ApiModelProperty(value="作品HASH",hidden = true)
    private String worksHash;
    /**
     * 时间戳ID
     */
    @ApiModelProperty(value="时间戳ID",hidden = true)
    private Long tsaId;
    /**
     * 上链ID
     */
    @ApiModelProperty(value="上链ID",hidden = true)
    private String blockchainId;
    /**
     * 上链时间
     */
    @ApiModelProperty(value="上链时间",hidden = true)
    private String blockchainTime;
    /**
     * 上链HASH
     */
    @ApiModelProperty(value="上链HASH",hidden = true)
    private String blockchainHash;
    /**
     * 存证证书地址
     */
    @ApiModelProperty(value="存证证书地址",hidden = true)
    private String certificateUrl;
    /**
     * 省版权局证书编号
     */
    @ApiModelProperty(value="省版权局证书编号",hidden = true)
    private String registerCertificateNo;
    /**
     * 省版权局证书地址
     */
    @ApiModelProperty(value="省版权局证书地址",hidden = true)
    private String registerCertificateUrl;
    /**
     * 省版权局证书获取时间
     */
    @ApiModelProperty(value="省版权局证书获取时间",hidden = true)
    private String registerCertificateTime;
    /**
     * 创作完成时间
     */
    @ApiModelProperty(value="创作完成时间")
//    @NotNull(message = "创作完成时间不能为空")
    private LocalDateTime inditeDoneTime;
    /**
     * 创作完成地点
     */
    @ApiModelProperty(value="创作完成地点")
//    @NotNull(message = "创作完成地点不能为空")
    private String inditeDonePlace;

    /**
     * 首次发表日期
     */
    @ApiModelProperty(value="首次发表日期")
    private LocalDateTime publishDate;

    /**
     * 发表地点
     */
    @ApiModelProperty(value="发表地点")
    private String publishPlace;
    /**
     * 审核意见
     */
    @ApiModelProperty(value="审核意见")
    private String auditOpinion;
    /**
     * 作品署名
     */
    @ApiModelProperty(value="作品署名")
    private String worksSignature;
    /**
     * 分发权利
     */
    @ApiModelProperty(value="分发权利")
    private String distributeRight;
    /**
     * 代理登记
     */
    @ApiModelProperty(value="代理登记")
    private String agentRegister;
    @ApiModelProperty(value="状态",hidden = true)
    private String statusCd;

    @ApiModelProperty(value="状态时间",hidden = true)
    private LocalDateTime statusDate;

    @ApiModelProperty(value="创建人",hidden = true)
    private String createStaff;

    @ApiModelProperty(value="创建时间",hidden = true)
    private LocalDateTime createDate;

    @ApiModelProperty(value="修改人",hidden = true)
    private String updateStaff;

    @ApiModelProperty(value="修改时间",hidden = true)
    private LocalDateTime updateDate;

    @ApiModelProperty(value="备注",hidden = true)
    private String remark;

    @TableField(exist = false)
    @ApiModelProperty(value="著作权人ID,多个以逗号隔开")
//    @NotBlank(message = "著作权人ID不能为空")
    private String ownerIds;

    @TableField(exist = false)
    @ApiModelProperty(value="作者列表")
//    @NotBlank(message = "作者列表不能为空")
    private List<WorksAuthor> authorList;

    @TableField(exist = false)
    @ApiModelProperty(value="步骤状态")
//    @NotBlank(message = "步骤状态不能为空")
    private String step;

    @TableField(exist = false)
    @ApiModelProperty(value="暂存")
    private Boolean isTemp;

    }
