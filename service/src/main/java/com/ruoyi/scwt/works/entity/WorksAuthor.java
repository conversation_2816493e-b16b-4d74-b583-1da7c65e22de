package com.ruoyi.scwt.works.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 作者信息库
 *
 * <AUTHOR>
 * @date 2020-07-09 17:13:41
 */
@Data
@TableName("WORKS_AUTHOR")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "作者信息")
public class WorksAuthor extends Model<WorksAuthor> {
private static final long serialVersionUID = 1L;

    /**
     * 作者ID
     */
    @TableId(type= IdType.AUTO)
    @ApiModelProperty(value="作者ID")
    private Long authorId;
    /**
     * 作者名称
     */
    @ApiModelProperty(value="作者名称")
    private String authorName;
    /**
     * 作者署名
     */
    @ApiModelProperty(value="作者署名")
    private String signature;
    /**
     * 作品id
     */
    @ApiModelProperty(value="作品id")
    private Long worksId;
    }
