package com.ruoyi.scwt.works.util;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.scwt.sop.DataProcessing.Bmark2Sop;
import com.ruoyi.scwt.works.entity.EvidenceOwner;
import com.ruoyi.scwt.works.entity.RegisterOwner;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Slf4j
public class EventUtil {
    private final Bmark2Sop bmark2Sop;

    @Transactional(rollbackFor = Exception.class)
    @Async
    @EventListener(EvidenceOwner.class)
    public void evidenceOwnerEventHandle(EvidenceOwner evidenceOwner) {
        RegisterOwner registerOwner = new RegisterOwner();
        BeanUtil.copyProperties(evidenceOwner, registerOwner);
        bmark2Sop.addOwner(registerOwner,"evidence",evidenceOwner.getCreateStaff());
    }

    @Transactional(rollbackFor = Exception.class)
    @Async
    @EventListener(RegisterOwner.class)
    public void registerOwnerEventHandle(RegisterOwner registerOwner) {
        bmark2Sop.addOwner(registerOwner, "registration", registerOwner.getCreateStaff());
    }
}
