package com.ruoyi.scwt.works.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 作品与著作权人映射表
 *
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Data
@TableName("WORKS_EVIDENCE")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "存证作品")
public class WorksEvidence extends Model<WorksEvidence> {
private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @TableId(type= IdType.AUTO)
    @ApiModelProperty(value="ID",hidden = true)
    private Long worksId;
    /**
     * 受理号
     */
    @ApiModelProperty(value="受理号",hidden = true)
    private String acceptanceNumber;
    /**
     * sop受理号
     */
    @ApiModelProperty(value="sop受理号",hidden = true)
    private String sopAcceptanceNumber;
    /**
     * 作品名称
     */
    @ApiModelProperty(value="作品名称")
    @NotBlank(message = "作品名称不能为空")
    @Size(max = 50,message = "作品名称不能超过50个字符")
    private String worksName;
    /**
     * 作品HASH
     */
    @ApiModelProperty(value="作品HASH",hidden = true)
    private String worksHash;
    /**
     * 作品样本文件ID
     */
    @ApiModelProperty(value="作品样本文件ID")
    @NotNull(message = "作品样本文件ID不能为空")
    private Long worksFileId;
    /**
     * 时间戳ID
     */
    @ApiModelProperty(value="时间戳ID",hidden = true)
    private Long tsaId;
    /**
     * 上链ID
     */
    @ApiModelProperty(value="上链ID",hidden = true)
    private String blockchainId;
    /**
     * 上链时间
     */
    @ApiModelProperty(value="上链时间",hidden = true)
    private String blockchainTime;
    /**
     * 上链HASH
     */
    @ApiModelProperty(value="上链HASH",hidden = true)
    private String blockchainHash;
    /**
     * 存证证书地址
     */
    @ApiModelProperty(value="存证证书地址",hidden = true)
    private String certificateUrl;
    /**
     * sop存证作品ID
     */
    @ApiModelProperty(value="sop存证作品ID",hidden = true)
    private String sopWorksId;


    @ApiModelProperty(value="状态",hidden = true)
    private String statusCd;

    @ApiModelProperty(value="状态时间",hidden = true)
    private LocalDateTime statusDate;

    @ApiModelProperty(value="创建人",hidden = true)
    private String createStaff;

    @ApiModelProperty(value="创建时间",hidden = true)
    private LocalDateTime createDate;

    @ApiModelProperty(value="修改人",hidden = true)
    private String updateStaff;

    @ApiModelProperty(value="修改时间",hidden = true)
    private LocalDateTime updateDate;

    @ApiModelProperty(value="备注",hidden = true)
    private String remark;

    @TableField(exist = false)
    @ApiModelProperty(value="著作权人ID,多个以逗号隔开")
    @NotBlank(message = "著作权人ID不能为空")
    private String ownerIds;
    }
