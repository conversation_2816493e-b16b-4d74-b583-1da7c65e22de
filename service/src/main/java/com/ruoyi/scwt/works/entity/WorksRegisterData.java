package com.ruoyi.scwt.works.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2020/7/30 9:15
 */
@Data
@TableName("WORKS_REGISTER_DATA")
@EqualsAndHashCode()
@ApiModel(value = "省版权局登记作品返回的字段")
public class WorksRegisterData extends Model<WorksRegisterData> {


    @TableId(type = IdType.INPUT)
    @ApiModelProperty(value="受理号")
    private String acceptanceNumber;

    @ApiModelProperty(value="作品ID")
    private Long worksId;

    @NotBlank(message = "作品名称不能为空")
    private String worksName;

    @ApiModelProperty(value="作品HASH值")
    private String worksHash;

    @ApiModelProperty(value="文创链上链hash")
    private String blockchainHash;

    @ApiModelProperty(value="作品上链时间")
    private LocalDateTime blockchainTime;

    @ApiModelProperty(value="天平链存证hash值")
    private String bcHash;

    @ApiModelProperty(value="时间戳返回时间")
    private String ntscTime;

    @ApiModelProperty(value="时间戳返回信息")
    private byte[] ntscRes;

    @ApiModelProperty(value="存证证书地址")
    private String certificateUrl;

    @ApiModelProperty(value="获证时间")
    private LocalDateTime obtainedTime;

    @ApiModelProperty(value="省版权局证书编号")
    private String cerNo;

    @ApiModelProperty(value="登记证书地址")
    private String RegistrationCertificateUrl;
}
