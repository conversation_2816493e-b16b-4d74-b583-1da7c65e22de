package com.ruoyi.scwt.works.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.scwt.works.entity.WorksEvidence;
import com.ruoyi.scwt.works.entity.WorksOwnerRel;
import com.ruoyi.scwt.works.vo.WorksEvidenceVo;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
public interface WorksEvidenceService extends IService<WorksEvidence> {
    Long getRepeatName(String worksName, String userId);

    WorksEvidenceVo setVoData(WorksEvidence worksEvidence, String voDataType);

    WorksEvidence isUpdate(Long worksId,Long userId);

    void addWorksEvidence(WorksEvidence worksEvidence);

    String createAcceptanceNumber(String userId);

    String getCertificateById(Long worksId);
}
