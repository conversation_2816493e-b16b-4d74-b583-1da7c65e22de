package com.ruoyi.scwt.common.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.scwt.common.constant.CommonConstants;
import com.ruoyi.scwt.common.dto.OcrDto;
import com.ruoyi.scwt.common.service.SysRegionService;
import com.ruoyi.scwt.common.util.AliHttpUtils;
import com.ruoyi.scwt.common.util.DataDesensitizationUtil;
import com.ruoyi.scwt.identify.constant.IdentifyConstants;
import com.ruoyi.scwt.identify.entity.Identify;
import com.ruoyi.scwt.identify.enums.IdentifyEnum;
import com.ruoyi.scwt.identify.service.IdentifyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 实名认证
 */
@RestController
@RequestMapping("/ali/cor")
@Api(value = "aliOcr", tags = "阿里OCR识别")
@Slf4j
public class AliOcrController extends BaseController {

    @Value("${ali.market.AppCode}")
    public String appCode;

    @Value("${ali.ocr.domain}")
    public String domain;

    @ApiOperation("证件OCR识别")
    @PostMapping
    public R aliOcr(@RequestBody OcrDto dto) {
        String path = "";
        String bodys="";
        switch (dto.getIdType()) {
            case CommonConstants.ID_TYPE_ID:
                path = "/rest/160601/ocr/ocr_idcard.json";
                if (StrUtil.isNotBlank(dto.getSide())) {
                    bodys = "{\"image\":\""+dto.getBase64()+"\",\"configure\":{\"side\":\""+dto.getSide()+"\",\"quality_info\":false}}";
                } else {
                    return R.fail("缺少身份证正反面参数");
                }
                break;
            case CommonConstants.ID_TYPE_BUSINESS:
                path = "/rest/160601/ocr/ocr_business_license.json";
                bodys = "{\"image\":\""+dto.getBase64()+"\"}";
                break;
        }
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Authorization", "APPCODE " + appCode);
        headers.put("Content-Type", "application/json; charset=UTF-8");
        Map<String, String> querys = new HashMap<String, String>();
        try {
            HttpResponse response = AliHttpUtils.doPost(domain, path, "POST", headers, querys, bodys);
            return R.ok(EntityUtils.toString(response.getEntity()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.fail();
    }

}
