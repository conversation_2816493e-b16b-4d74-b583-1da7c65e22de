package com.ruoyi.scwt.common.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 全国省市区
 *
 * <AUTHOR>
 * @date 2019-10-17 16:21:21
 */
@Data
@ApiModel
@TableName("sys_base_region")
@EqualsAndHashCode(callSuper = true)
public class SysRegion extends Model<SysRegion> {
private static final long serialVersionUID = 1L;

  /**
   * 省市区编码
   */
  @TableId
  @ApiModelProperty(value = "省市区编码")
  private Long regionId;

  /**
   * 父级id
   */
  @ApiModelProperty(value = "父级id")
  private Long parRegionId;

  /**
   * 省市区名称
   */
  @ApiModelProperty(value = "省市区名称")
  private String regionName;

  /**
   * 层级
   */
  @ApiModelProperty(value = "层级")
  private Long regionLevel;

  /**
   * 类型
   */
  @ApiModelProperty(value = "类型")
  private Integer regionType;

  /**
   * 说明
   */
  @ApiModelProperty(value = "说明")
  private String regionDesc;

  /**
   * 区域排序
   */
  @ApiModelProperty(value = "区域排序")
  private Long regionSort;

  /**
   * 长途区号
   */
  @ApiModelProperty(value = "长途区号")
  private Long regionLong;

  /**
   * 记录首次创建的员工标识
   */
  @ApiModelProperty(value = "记录首次创建的员工标识")
  private Long createStaff;

  /**
   * 记录每次修改的员工标识
   */
  @ApiModelProperty(value = "记录每次修改的员工标识")
  private Long updateStaff;

  /**
   * 记录实例信息创建的时间
   */
  @ApiModelProperty(value = "记录实例信息创建的时间")
  private LocalDateTime createDate;

  /**
   * 记录每次信息变更的时间
   */
  @ApiModelProperty(value = "记录每次信息变更的时间")
  private LocalDateTime statusDate;

  /**
   * 记录任何变动时，修改的时间。
   */
  @ApiModelProperty(value = "记录任何变动时，修改的时间。")
  private LocalDateTime updateDate;

  /**
   * 备注
   */
  @ApiModelProperty(value = "备注")
  private String remark;

}
