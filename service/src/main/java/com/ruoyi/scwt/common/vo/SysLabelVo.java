package com.ruoyi.scwt.common.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 全国省市区
 *
 * <AUTHOR>
 * @date 2019-10-17 16:21:21
 */
@Data
public class SysLabelVo {


  /**
   * 标签类型
   */
  @NotBlank(message = "标签类型不能为空")
  @ApiModelProperty(value = "标签类型")
  private String labelType;

  /**
   * 父级id
   */
  @ApiModelProperty(value = "父级id")
  private Long parLabelId;



}
