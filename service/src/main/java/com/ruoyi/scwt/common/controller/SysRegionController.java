package com.ruoyi.scwt.common.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.scwt.common.entity.SysRegion;
import com.ruoyi.scwt.common.service.SysRegionService;
import com.ruoyi.scwt.common.vo.RegionVO;
import com.ruoyi.scwt.exception.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;


/**
 * 全国省市区
 *
 * <AUTHOR>
 * @date 2019-10-17 16:21:21
 */
@RestController
@AllArgsConstructor
@RequestMapping("/copyright/region")
@Api(value = "region",tags = "全国省市区管理")
public class SysRegionController extends BaseController {

  private final SysRegionService regionService;

  /**
   * 分页查询
   * @param regionVo 全国省市区
   * @return
   */
  @GetMapping("/page")
  @ApiOperation(value="分页查询Region")
  @Anonymous
  public TableDataInfo getRegionPage(RegionVO regionVo) {
    LambdaQueryWrapper<SysRegion> lambda = Wrappers.<SysRegion>query().lambda().orderByAsc(SysRegion::getRegionSort).orderByAsc(SysRegion::getRegionId);
    if (StringUtils.isNotEmpty(regionVo.getRegionName())) {
      lambda.like(SysRegion::getRegionName, regionVo.getRegionName());
    }
    if (regionVo.getParRegionId() != null) {
      lambda.eq(SysRegion::getParRegionId, regionVo.getParRegionId());
    }
    if (regionVo.getRegionLevel() != null) {
      lambda.like(SysRegion::getRegionLevel, regionVo.getRegionLevel());
    }
    if (regionVo.getRegionType() != null) {
      lambda.like(SysRegion::getRegionType, regionVo.getRegionType());
    }
    if (regionVo.getRegionLong() != null) {
      lambda.like(SysRegion::getRegionLong, regionVo.getRegionLong());
    }
    startPage();
    List<SysRegion> list = regionService.list(lambda);
    return getDataTable(list);
  }

  /**
   * 通过id查询全国省市区
   * @param regionId id
   * @return R
   */
  @GetMapping("/{regionId}")
  @ApiOperation(value="查询Region")
  public R<SysRegion> getById(@PathVariable("regionId") Long regionId){
    SysRegion region = regionService.getById(regionId);
    Optional.ofNullable(region).orElseThrow(BusinessException::resourceNotFoundException);
    return R.ok(region);
  }

  /**
   * 新增全国省市区
   * @param regionVo 全国省市区
   * @return R
   */
  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  @ApiOperation(value="新增Region")
  @ApiImplicitParam(name = "regionVo", paramType = "body", value = "全国省市区Vo", required = true, dataType = "RegionVo")
  public R save(@Valid @RequestBody RegionVO regionVo) {
    SysRegion region = new SysRegion();
    region.setCreateDate(LocalDateTime.now());
    region.setStatusDate(LocalDateTime.now());
    BeanUtil.copyProperties(regionVo, region, CopyOptions.create().ignoreNullValue());
    boolean b = regionService.save(region);
    if (b) {
        regionVo.setRegionId(region.getRegionId());
      return R.ok(regionVo, "新增成功");
    } else {
      throw BusinessException.resourceCreateException();
    }
  }

  /**
   * 修改全国省市区
   * @param regionVo 全国省市区
   * @return R
   */
  @PutMapping("/{regionId}")
  @ApiOperation(value="修改Region")
  public R updateById(@PathVariable("regionId") Long regionId, @Valid @RequestBody RegionVO regionVo) {
    regionVo.setRegionId(regionId);
    SysRegion region = new SysRegion();
    region.setUpdateDate(LocalDateTime.now());
    BeanUtil.copyProperties(regionVo, region);
    boolean b = regionService.updateById(region);
    if (b) {
      regionVo.setRegionId(region.getRegionId());
      return R.ok(regionVo, "修改成功");
    } else {
      throw BusinessException.resourceUpdateException();
    }
  }

  /**
   * 通过id删除全国省市区
   * @param regionId id
   * @return R
   */
  @DeleteMapping("/{regionId}")
  @ApiOperation(value="删除Region")
  public R removeById(@PathVariable Long regionId) {
    boolean b = regionService.removeById(regionId);
    if (b) {
      return R.ok(null, "删除成功");
    } else {
      throw BusinessException.resourceDeleteException();
    }
  }

}
