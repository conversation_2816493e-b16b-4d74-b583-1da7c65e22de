package com.ruoyi.scwt.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 短信服务配置属性
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@Component
@ConfigurationProperties(prefix = "ruoyi.sms")
public class SmsConfig {

    /**
     * 验证码相关配置
     */
    private Code code = new Code();

    /**
     * 验证码配置类
     */
    @Data
    public static class Code {
        /**
         * 禁止发送验证码的手机号前缀列表（黑名单）
         */
        private List<String> blockedPrefixes;
    }
}
