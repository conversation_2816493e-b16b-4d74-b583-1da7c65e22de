package com.ruoyi.scwt.common.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.scwt.common.constant.CommonConstants;
import com.ruoyi.scwt.common.dto.AliIdentifyDto;
import com.ruoyi.scwt.common.dto.CompanyIdentifyDto;
import com.ruoyi.scwt.common.dto.OcrDto;
import com.ruoyi.scwt.common.dto.PersonIdentifyDto;
import com.ruoyi.scwt.common.util.AliHttpUtils;
import com.ruoyi.scwt.identify.constant.IdentifyConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * 实名认证
 */
@RestController
@RequestMapping("/ali/identify")
@Api(value = "aliIdentify", tags = "阿里要素认证")
@Slf4j
public class AliIdentifyController extends BaseController {

    @Value("${ali.market.AppCode}")
    public String appCode;

    @Value("${ali.eid.domain}")
    public String didDomain;

    @Value("${ali.mobile.domain}")
    public String mobileDomain;

    @Value("${ali.company.domain}")
    public String companyDomain;
    @ApiOperation("身份证二要素验证")
    @PostMapping("/person")
    public R eidIdentify(@RequestBody PersonIdentifyDto dto) {
        try {
            String url = didDomain + "/eid/check";
            Map<String, String> params = new HashMap<>();
            params.put("idcard", dto.getIdNo());
            params.put("name", dto.getIdName());
            OkHttpClient client = new OkHttpClient.Builder().build();
            FormBody.Builder formbuilder = new FormBody.Builder();
            Iterator<String> it = params.keySet().iterator();
            while (it.hasNext()) {
                String key = it.next();
                formbuilder.add(key, params.get(key));
            }
            FormBody body = formbuilder.build();
            Request request = new Request.Builder().url(url).addHeader("Authorization", "APPCODE " + appCode).post(body).build();
            Response response = client.newCall(request).execute();
//        System.out.println("返回状态码" + response.code() + ",message:" + response.message());
            JSONObject jsonObject = new JSONObject(response.body().string());
            if (StrUtil.equals(jsonObject.getStr("code"), "0")){
                String res = jsonObject.getJSONObject("result").getStr("res");
                switch (res) {
                    case "1":
                        return R.ok(true, "认证通过");
                    case "2":
                        return R.fail(false, "姓名与身份证不一致");
                    default:
                        return R.fail(false, "未知错误");
                }
            }else {
                return R.fail("请求认证接口失败");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    @ApiOperation("企业三要素认证")
    @PostMapping("/company")
    public R companyIdentify(@RequestBody CompanyIdentifyDto dto) {
        String path = "/company-three/check/v2";
        Map<String, String> headers = new HashMap<String, String>();
        //最后在header中的格式(中间是英文空格)为Authorization:APPCODE 83359fd73fe94948385f570e3c139105
        headers.put("Authorization", "APPCODE " + appCode);
        Map<String, String> querys = new HashMap<String, String>();
        querys.put("companyName", dto.getCompanyName());
        querys.put("creditNo", dto.getCreditNo());
        querys.put("legalPerson", dto.getLegalPerson());

        try {
            HttpResponse response = AliHttpUtils.doGet(companyDomain, path, "GET", headers, querys);
            //获取response的body
//            System.out.println(EntityUtils.toString(response.getEntity()));
            String result = EntityUtils.toString(response.getEntity());
            JSONObject jsonObject = new JSONObject(result);
            if (StrUtil.equals(jsonObject.getStr("code"), "200")){
                String res = jsonObject.getJSONObject("data").getStr("result");
                switch (res) {
                    case "0":
                        return R.fail(false, "信息不一致");
                    case "1":
                        return R.ok(true, "认证通过");
                    case "2":
                        return R.fail(false, "输⼊企业名疑似曾⽤名，其他两要素⼀致");
                    case "3":
                        return R.fail(false, "无数据");
                    default:
                        return R.fail(false, "未知错误");
                }
            }else {
                return R.fail("请求认证接口失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public R aliIdentify(AliIdentifyDto dto) {
        if (IdentifyConstants.IDENTIFY_TYPE_PERSON.equals(dto.getIdentifyType())){
            return eidIdentify(new PersonIdentifyDto(dto.getIdName(),dto.getIdNo()));
        }else if (IdentifyConstants.IDENTIFY_TYPE_ENTERPRISE.equals(dto.getIdentifyType())){
            return companyIdentify(new CompanyIdentifyDto(dto.getIdName(),dto.getIdNo(),dto.getLegalName()));
        }else {
            return R.fail("未知实名认证类型");
        }
    }
}
