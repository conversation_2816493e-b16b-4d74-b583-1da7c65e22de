package com.ruoyi.scwt.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 全国省市区
 *
 * <AUTHOR>
 * @date 2019-10-17 16:21:21
 */
@Data
@ApiModel
@TableName("sys_label")
@EqualsAndHashCode(callSuper = true)
public class SysLabel extends Model<SysLabel> {
private static final long serialVersionUID = 1L;

  /**
   * 标签ID
   */
  @TableId(type= IdType.AUTO)
  @ApiModelProperty(value = "标签ID")
  private Long labelId;
  /**
   * 标签类型
   */
  @ApiModelProperty(value = "标签类型")
  private String labelType;

  /**
   * 父级id
   */
  @ApiModelProperty(value = "父级id")
  private Long parLabelId;

  /**
   * 标签名称
   */
  @ApiModelProperty(value = "标签名称")
  private String labelName;


  /**
   * 标签排序
   */
  @ApiModelProperty(value = "标签排序")
  private Long labelSort;


}
