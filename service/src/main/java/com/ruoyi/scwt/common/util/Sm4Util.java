package com.ruoyi.scwt.common.util;

import com.google.api.client.util.Value;
import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.symmetric.SM4;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;


@Slf4j
@Component
public class Sm4Util {

    @Value("${sm4.secret-key}")
    private String secretKey;

    public String encrypt(String plaintext) {
        try {
            SM4 sm4 = (SM4) SmUtil.sm4(HexUtil.decodeHex(secretKey));
            byte[] iv = generateSecureIv();
            sm4.setIv(iv);

            String ciphertext = sm4.encryptBase64(plaintext);
            return HexUtil.encodeHexStr(iv) + ":" + ciphertext;
        } catch (Exception e) {
            throw new RuntimeException("加密失败", e);
        }
    }

    // 解密方法
    public String decrypt(String ciphertext) {
        try {
            String[] parts = ciphertext.split(":");
            if (parts.length != 2) throw new IllegalArgumentException("无效密文格式");

            byte[] iv = HexUtil.decodeHex(parts[0]);
            SM4 sm4 = (SM4) SmUtil.sm4(HexUtil.decodeHex(secretKey));
            sm4.setIv(iv);

            return sm4.decryptStr(parts[1], StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("解密失败", e);
        }
    }

    // 对比方法（安全版）
    public boolean secureCompare(String inputPlaintext, String storedCiphertext) {
        try {
            String decrypted = decrypt(storedCiphertext);
            return slowEquals(decrypted.getBytes(), inputPlaintext.getBytes());
        } catch (Exception e) {
            log.error("安全比对异常", e);
            return false;
        }
    }

    // 安全随机IV生成
    private byte[] generateSecureIv() {
        SecureRandom random = new SecureRandom();
        byte[] iv = new byte[16];
        random.nextBytes(iv);
        return iv;
    }

    // 防时序攻击的比对
    private boolean slowEquals(byte[] a, byte[] b) {
        int diff = a.length ^ b.length;
        for (int i = 0; i < a.length && i < b.length; i++) {
            diff |= a[i] ^ b[i];
        }
        return diff == 0;
    }
}
