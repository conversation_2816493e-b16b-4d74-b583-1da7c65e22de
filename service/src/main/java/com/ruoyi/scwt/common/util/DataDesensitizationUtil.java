package com.ruoyi.scwt.common.util;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * Created by zeb on 2021/4/20.
 */
public class DataDesensitizationUtil {
    /**
     * 保留左边数据指定个数字符脱敏
     *
     * @param str
     * @return
     */
    public static String replaceLeft(String str,int leftIndex) {
        String name = StringUtils.left(str, leftIndex);
        return StringUtils.rightPad(name, StringUtils.length(str), "*");
    }

    /**
     * 保留前端后端各指定长度的字符，其他用星号代替
     *
     * @param str
     * @return
     */
    public static String replaceLeftRight(String str,int leftIndex,int rightIndex) {
        return StringUtils.left(str, leftIndex).concat(
                StringUtils.removeStart(
                        StringUtils.leftPad(
                                StringUtils.right(str, rightIndex), StringUtils.length(str), "*"), "***"));

    }
}
