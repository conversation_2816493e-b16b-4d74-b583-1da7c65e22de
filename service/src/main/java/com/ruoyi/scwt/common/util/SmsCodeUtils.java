package com.ruoyi.scwt.common.util;

import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.stereotype.Component;

@Component
public class SmsCodeUtils {



    @Autowired
    private RedisCache redisCache;


    /**
     * 校验短信验证码
     *
     * @return 结果
     */
    public void validateSmsCaptcha(String phone, String smsCode) {
        String verifyKey = CacheConstants.SMS_CODE_KEY + StringUtils.nvl(phone, "");
        if (!redisCache.hasKey(verifyKey)) {
            throw new BadCredentialsException("验证码失效");
        }
        String captcha = redisCache.getCacheObject(verifyKey).toString();
        redisCache.deleteObject(verifyKey);
        if (!smsCode.equalsIgnoreCase(captcha)) {
            throw new BadCredentialsException("验证码不一致");
        }
    }
}
