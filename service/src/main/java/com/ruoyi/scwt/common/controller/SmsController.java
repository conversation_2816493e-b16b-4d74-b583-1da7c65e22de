package com.ruoyi.scwt.common.controller;

import cn.hutool.core.util.ObjectUtil;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.scwt.common.config.SmsConfig;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Base64;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.util.FastByteArrayOutputStream;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.alibaba.fastjson2.JSON.toJSONString;

/**
 * 验证码操作处理
 * 
 * <AUTHOR>
 */
@RestController
@Slf4j
public class SmsController {

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SmsConfig smsConfig;

    @Value("${ali.sms.accessKey}")
    public String accessKey;

    @Value("${ali.sms.accessId}")
    public String accessId;

    @Value("${ali.sms.accessSecret}")
    public String accessSecret;

    @Value("${ali.sms.servicePoint}")
    public String servicePoint;

    @Value("${ali.sms.signName}")
    public String signName;

    @GetMapping("/sendPhoneCode")
    @Anonymous
    public AjaxResult sendPhoneCode(String phone, String scene){
        // 校验手机号前缀（黑名单检查）
        if (isBlockedPhoneNumber(phone)) {
            log.warn("手机号在排除名单中，禁止发送验证码，手机号: {}", phone);
            return AjaxResult.error("暂时不支持虚拟运营商手机号，请更换后重试");
        }

        SysUser sysUser = userMapper.checkPhoneUnique(phone);
//        String SignName=signName;
        String templateCode="";
        switch (scene){
            case Constants.SMS_SCENE_H5_REGISTER:
                //验证手机号是否注册
                if (StringUtils.isNotNull(sysUser)) {
                    return AjaxResult.error("手机号已注册");
                }
//                SignName=H5signName;
                templateCode="SMS_491420165";
                break;
            case Constants.SMS_SCENE_REGISTER:
                //验证手机号是否注册
                if (StringUtils.isNotNull(sysUser)) {
                    return AjaxResult.error("手机号已注册");
                }
//                templateCode="SMS_474910628";
                templateCode="SMS_485465136";
                break;
            case Constants.SMS_SCENE_H5_LOGIN:
                //验证手机号是否注册
                if (StringUtils.isNull(sysUser)) {
                    return AjaxResult.error("发送失败，请检查您的手机号是否正确");
                }
//                SignName=H5signName;
                templateCode="SMS_491520134";
                break;
            case Constants.SMS_SCENE_LOGIN:
                //验证手机号是否注册
                if (StringUtils.isNull(sysUser)) {
                    return AjaxResult.error("发送失败，请检查您的手机号是否正确");
                }
//                templateCode="SMS_480885190";
                templateCode="SMS_485260127";
                break;
            case Constants.SMS_SCENE_IDENTIFY:
                //验证手机号是否注册
                if (StringUtils.isNull(sysUser)) {
                    return AjaxResult.error("发送失败，请检查您的手机号是否正确");
                }else if (!sysUser.getPhonenumber().equals(phone)){
                    return AjaxResult.error("手机号与注册手机号不一致，请确认！");
                }
                templateCode="SMS_485285158";
                break;
            case Constants.SMS_SCENE_SHOPLOGOUT:
                //验证手机号是否注册
                if (StringUtils.isNull(sysUser)) {
                    return AjaxResult.error("发送失败，请检查您的手机号是否正确");
                }
                templateCode="SMS_478955008";
                break;
            case Constants.SMS_SCENE_USERLOGOUT:
                //验证手机号是否注册
                if (StringUtils.isNull(sysUser)) {
                    return AjaxResult.error("发送失败，请检查您的手机号是否正确");
                }
                templateCode="SMS_479125008";
                break;
            case Constants.SMS_SCENE_H5_RESETPASSWORD:
                //验证手机号是否注册
                if (StringUtils.isNull(sysUser)) {
                    return AjaxResult.error("发送失败，请检查您的手机号是否正确");
                }
//                SignName=H5signName;
                templateCode="SMS_491515159";
                break;
            case Constants.SMS_SCENE_RESETPASSWORD:
                //验证手机号是否注册
                if (StringUtils.isNull(sysUser)) {
                    return AjaxResult.error("发送失败，请检查您的手机号是否正确");
                }
                templateCode="SMS_485320129";
                break;
            default:
                return AjaxResult.error("参数错误");
        }
        String verifyKey = CacheConstants.SMS_CODE_KEY + phone;
        long expire = redisCache.getExpire(verifyKey);
        if (expire > 240){
            return AjaxResult.error("频繁发送验证码,一分钟后再试");
        }
        //发送手机验证码
        try {
            int code=getCode();
            Map<String,String> map=new HashMap<>();
            map.put("code", String.valueOf(code));
            String sendSms = sendSms(phone, signName, templateCode, toJSONString(map));
            if (sendSms.equals("OK")){
                redisCache.setCacheObject(verifyKey, String.valueOf(code), Constants.SMS_EXPIRATION, TimeUnit.MINUTES);
                return AjaxResult.success("短信验证码发送成功，请注意查收！");
            }else {
                return AjaxResult.error("短信验证码发送失败，请稍后重试！");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /***
     * 交易完成短信通知
     * @return
     * @throws Exception
     */
    public void sendTransactionSms(Long userId,String assetName,String money){
        try {
            SysUser sysUser = userMapper.selectUserById(userId);
            Map<String,String> map=new HashMap<>();
            map.put("assetName", assetName);
            map.put("money", money);
            String sendSms = sendSms(sysUser.getPhonenumber(), signName, "SMS_485495162", toJSONString(map));
            if (sendSms.equals("OK")){
                log.info("交易成功短信发送成功，手机号{},参数{}", sysUser.getPhonenumber(), toJSONString(map));
            }else {
                log.error("交易成功短信发送失败，手机号{},参数{},失败信息{}", sysUser.getPhonenumber(), toJSONString(map), sendSms);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public Client createClient() throws Exception {
        Config config = new Config()
//                .setType(accessKey)
                .setAccessKeyId(accessId)
                .setAccessKeySecret(accessSecret);
        // 配置 Endpoint
        config.endpoint = servicePoint;
        return new Client(config);
    }
    public String sendSms(String phone,String signName,String templateCode,String templateParam) throws Exception {
        // 初始化请求客户端
        Client client = createClient();

        // 构造请求对象，请填入请求参数值
        SendSmsRequest sendSmsRequest = new SendSmsRequest()
                .setPhoneNumbers(phone)
                .setSignName(signName)
                .setTemplateCode(templateCode)
                .setTemplateParam(templateParam);

        // 获取响应对象
        SendSmsResponse sendSmsResponse = client.sendSms(sendSmsRequest);
        // 响应包含服务端响应的 body 和 headers
        return sendSmsResponse.body.getMessage();
    }

    public int getCode(){
        int min=100000;
        int max=999999;
        return (int) (Math.random()*(max-min+1)+min);
    }

    /**
     * 检查手机号是否在黑名单中
     *
     * @param phone 手机号
     * @return true-在黑名单中（禁止发送），false-不在黑名单中（允许发送）
     */
    private boolean isBlockedPhoneNumber(String phone) {
        if (StringUtils.isBlank(phone)) {
            return true; // 空手机号视为禁止
        }

        // 获取配置的禁止发送验证码的手机号前缀
        List<String> blockedPrefixes = smsConfig.getCode().getBlockedPrefixes();

        // 如果配置为空，表示没有黑名单，所有手机号都允许发送
        if (blockedPrefixes == null || blockedPrefixes.isEmpty()) {
            log.info("短信验证码黑名单配置为空，允许所有手机号发送验证码");
            return false;
        }

        // 检查手机号是否以配置的黑名单前缀开头
        boolean isBlocked = blockedPrefixes.stream()
            .anyMatch(prefix -> phone.startsWith(prefix));

        if (isBlocked) {
            log.warn("手机号在黑名单中，禁止发送验证码，手机号: {}，黑名单前缀: {}", phone, blockedPrefixes);
        } else {
            log.info("手机号不在黑名单中，允许发送验证码，手机号: {}", phone);
        }

        return isBlocked;
    }

}
