package com.ruoyi.scwt.common.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.scwt.asset.entity.AssetLabelRel;
import com.ruoyi.scwt.asset.service.AssetLabelRelService;
import com.ruoyi.scwt.common.entity.SysLabel;
import com.ruoyi.scwt.common.entity.SysRegion;
import com.ruoyi.scwt.common.mapper.SysLabelMapper;
import com.ruoyi.scwt.common.mapper.SysRegionMapper;
import com.ruoyi.scwt.common.service.SysLabelService;
import com.ruoyi.scwt.common.service.SysRegionService;
import com.ruoyi.scwt.shop.entity.ShopLabelRel;
import com.ruoyi.scwt.shop.service.ShopLabelRelService;
import com.ruoyi.scwt.zone.entity.ZoneLabelRel;
import com.ruoyi.scwt.zone.service.ZoneLabelRelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 全国省市区
 *
 * <AUTHOR>
 * @date 2019-10-17 16:21:21
 */
@Service
public class SysLabelServiceImpl extends ServiceImpl<SysLabelMapper, SysLabel> implements SysLabelService {

    @Autowired
    private ShopLabelRelService shopLabelRelService;
    @Autowired
    private AssetLabelRelService assetLabelRelService;
    @Autowired
    private ZoneLabelRelService zoneLabelRelService;

    @Override
    public List<SysLabel> getListByShopId(Long shopId) {
        List<ShopLabelRel> list = shopLabelRelService.list(Wrappers.<ShopLabelRel>lambdaQuery().eq(ShopLabelRel::getShopId, shopId));
        List<Long> labelIds = list.stream().map(ShopLabelRel::getLabelId).collect(Collectors.toList());
        return baseMapper.selectList(Wrappers.<SysLabel>lambdaQuery().in(SysLabel::getLabelId, labelIds));
    }

    @Override
    public List<SysLabel> getLabelListByAssetId(Long assetId) {
        List<Long> labelIds = assetLabelRelService.list(Wrappers.<AssetLabelRel>lambdaQuery().eq(AssetLabelRel::getAssetId, assetId)).stream().map(AssetLabelRel::getLabelId).collect(Collectors.toList());
        return baseMapper.selectList(Wrappers.<SysLabel>lambdaQuery().in(SysLabel::getLabelId, labelIds));
    }

    @Override
    public List<SysLabel> getLabelListByZoneId(Long zoneId) {
        List<Long> labelIds = zoneLabelRelService.list(Wrappers.<ZoneLabelRel>lambdaQuery().eq(ZoneLabelRel::getZoneId, zoneId)).stream().map(ZoneLabelRel::getLabelId).collect(Collectors.toList());
        return baseMapper.selectList(Wrappers.<SysLabel>lambdaQuery().in(SysLabel::getLabelId, labelIds));
    }
}
