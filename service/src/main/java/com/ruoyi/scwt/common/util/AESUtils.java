package com.ruoyi.scwt.common.util;

import com.google.api.client.util.Value;
import org.springframework.stereotype.Component;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
@Component
public class AESUtils {
    // AES密钥（需改为实际密钥，建议从配置中心获取）
//    @Value("${sm4.secret-key}")
//    private static String AES_KEY;
    private static final String AES_KEY = "cwsfsccdex123456"; // 长度必须16/24/32字节
    
    private static final String ALGORITHM = "AES/ECB/PKCS5Padding";

    // 加密
    public static String encrypt(String data) {
        try {
            SecretKeySpec key = new SecretKeySpec(AES_KEY.getBytes(), "AES");
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, key);
            byte[] encrypted = cipher.doFinal(data.getBytes());
            return Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            throw new RuntimeException("AES加密失败", e);
        }
    }

    // 解密
//    public static String decrypt(String encryptedData) {
//        try {
//            SecretKeySpec key = new SecretKeySpec(AES_KEY.getBytes(), "AES");
//            Cipher cipher = Cipher.getInstance(ALGORITHM);
//            cipher.init(Cipher.DECRYPT_MODE, key);
//            byte[] decoded = Base64.getDecoder().decode(encryptedData);
//            byte[] decrypted = cipher.doFinal(decoded);
//            return new String(decrypted);
//        } catch (Exception e) {
//            throw new RuntimeException("AES解密失败", e);
//        }
//    }

    public static String decrypt(String encryptedData) {
        try {
            // 处理Base64填充问题
            String cleanData = encryptedData
                    .replaceAll("[^a-zA-Z0-9+/=]", "")
                    .replaceAll("=+$", "");

            // 验证密钥长度
            if (AES_KEY.length() != 16) {
                throw new IllegalArgumentException("密钥长度必须为16位");
            }

            SecretKeySpec key = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, key);

            byte[] decoded = Base64.getDecoder().decode(cleanData);
            byte[] decrypted = cipher.doFinal(decoded);

            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (IllegalBlockSizeException e) {
            throw new RuntimeException("密文长度不正确（应为16字节倍数），当前长度：" + encryptedData.length(), e);
        } catch (BadPaddingException e) {
            throw new RuntimeException("密钥不匹配或密文损坏", e);
        } catch (Exception e) {
            throw new RuntimeException("解密失败：" + e.getMessage(), e);
        }
    }
}
