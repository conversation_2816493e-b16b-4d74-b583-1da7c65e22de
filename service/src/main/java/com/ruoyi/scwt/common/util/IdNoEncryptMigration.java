package com.ruoyi.scwt.common.util;

import com.ruoyi.scwt.identify.entity.Identify;
import com.ruoyi.scwt.identify.service.IdentifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

// 创建临时迁移服务类
@Component
public class IdNoEncryptMigration {

    @Autowired
    private IdentifyService identifyService;
    
//    @PostConstruct
    public void migrate() {
        List<Identify> allIdentifies = identifyService.list();
        allIdentifies.forEach(identify -> {
            // 解密判断逻辑（防止重复加密）
//            if (!isEncrypted(identify.getIdNo())) {
                String encrypted = AESUtils.decrypt(identify.getIdNo());
                identify.setIdNo(encrypted);
                identifyService.updateById(identify);
//            }
        });
    }

    private boolean isEncrypted(String value) {
        try {
            AESUtils.decrypt(value);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
