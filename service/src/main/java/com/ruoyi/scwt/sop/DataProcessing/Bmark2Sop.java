package com.ruoyi.scwt.sop.DataProcessing;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.scwt.file.entity.AttachmentInfo;
import com.ruoyi.scwt.file.mapper.AttachmentInfoMapper;
import com.ruoyi.scwt.file.util.OssUtil;
import com.ruoyi.scwt.sop.constant.CommonConstants;
import com.ruoyi.scwt.sop.controller.BlockChainController;
import com.ruoyi.scwt.sop.controller.EvidenceAndRegisterController;
import com.ruoyi.scwt.sop.dto.*;
import com.ruoyi.scwt.works.constant.WorksConstants;
import com.ruoyi.scwt.works.entity.*;
import com.ruoyi.scwt.works.mapper.WorksNTSCMapper;
import com.ruoyi.scwt.works.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.InputStream;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor
public class Bmark2Sop {

    private final EvidenceAndRegisterController evidenceAndRegisterController;

    private final BlockChainController blockChainController;

    private final AttachmentInfoMapper attachmentInfoMapper;
//    private final WorksOwnerRelService worksOwnerRelService;
//    private final RegisterOwnerService registerOwnerService;
    private final WorksAuthorService worksAuthorService;

    private final OssUtil ossUtil;

    @Value("${copyright.temp}")
    private String tempPath;

    /***
     * 用户注册上链
     */
    public JSONObject foreign(RegisterForeignDto foreignDto) {
        return evidenceAndRegisterController.foreign(foreignDto);
    }


    /***
     * 文件上传
     */
    public JSONObject fileUpload(AttachmentUploadDto attachmentUploadDto) {
        return evidenceAndRegisterController.fileUpload(attachmentUploadDto);
    }

    /***
     * 根据文件地址组装文件上传数据
     */
    public String fileUploadDataJoin(String attId, String typeCode, String platformUserId, String fileDesc) {
        AttachmentInfo info = attachmentInfoMapper.selectById(attId);
        String filePath = info.getFilePath();
//        String[] paths = filePath.split("/");
        File file = null;
        try {
            InputStream inputStream = ossUtil.createCosClient().getObject(ossUtil.getBucketName(), filePath).getObjectContent();
            FileUtil.mkdir(tempPath);
            file = FileUtil.writeFromStream(inputStream, tempPath + StrUtil.SLASH + filePath);
//            MultipartFile cMultiFile = new MockMultipartFile(info.getFileName(), file.getName(), null, Files.newInputStream(file.toPath()));
            AttachmentUploadDto dto = new AttachmentUploadDto();
            dto.setFile(file);
            dto.setAttachmentObjectTypeCode(typeCode);
            dto.setPlatformUserId(platformUserId);
            dto.setFileDesc(fileDesc);
            JSONObject jsonObject = fileUpload(dto);
            return String.valueOf(jsonObject.get("attachId"));
        } catch (Exception ioException) {
            ioException.printStackTrace();
            log.error("文件上传接口异常");
            throw new RuntimeException(ioException);
        } finally {
            assert file != null;
            file.delete();
        }
    }


    /***
     * 新增著作权人
     * 著作权人应用场景类型（存证：evidence，登记：registration）
     */
    public JSONObject addOwner(RegisterOwner owners, String ownerSceneType, String userId) {
        OwnerDto dto = new OwnerDto();
        if (ownerSceneType.equals(CommonConstants.OWNER_SCENE_REGISTRATION)) {
            dto.setLegalName(owners.getLegalName());
            String[] split = owners.getIdCard().split(",");
            if (com.ruoyi.scwt.common.constant.CommonConstants.ID_TYPE_ID.equals(owners.getIdType())) {
//                dto.setPapersType("identityCard");
                dto.setFrontAttachId(fileUploadDataJoin(split[0], CommonConstants.FILE_TYPE_SFZMWJ, userId, "身份证人像面"));
                dto.setBackAttachId(fileUploadDataJoin(split[1], CommonConstants.FILE_TYPE_SFZMWJ, userId, "身份证国徽面"));
            } else {
                dto.setBusinessAttachId(fileUploadDataJoin(split[0], CommonConstants.FILE_TYPE_SFZMWJ, userId, "证件照"));
//                dto.setPapersType("busiLicense");
            }
            if (StrUtil.isNotEmpty(owners.getOtherFiles())) {
                List<Long> otherIds = new ArrayList<>();
                for (String s : owners.getOtherFiles().split(",")) {
                    otherIds.add(Long.valueOf(fileUploadDataJoin(s, CommonConstants.FILE_TYPE_ZZQRQTCL, userId, "其他证明材料")));
                }
                dto.setOtherfilesAttachId(otherIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
            }
            dto.setCountry("中国");
            dto.setProvince(owners.getProvince());
            dto.setCity(owners.getCity());
        }
        dto.setPapersType(com.ruoyi.scwt.common.constant.CommonConstants.ID_TYPE_ID.equals(owners.getIdType())?"identityCard":"busiLicense");
        dto.setOwnerSceneType(ownerSceneType);
        dto.setOwnerName(owners.getOwnerName());
        dto.setIdNo(owners.getIdNo());
        dto.setOwnerType(owners.getOwnerType());
        dto.setSignature(owners.getOwnerName());
        dto.setLegalName(owners.getLegalName());
        dto.setPlatformUserId(userId);
        return evidenceAndRegisterController.addOwner(dto);
    }



    /***
     * 获取审核进度
     */
//    public void getWorkProgressByAcceptNo(WorkByAcceptNoDto dto) {
//        WorksRegister one = worksRegisterService.getOne(Wrappers.<WorksRegister>lambdaQuery().eq(WorksRegister::getAcceptanceNumber, dto.getAcceptNo()));
//        if (ObjectUtil.isEmpty(one)) return;
//        JSONObject jsonObject = evidenceAndRegisterController.getWorkProgressByAcceptNo(dto);
//        log.info("SOP进度查询返回数据：{}", jsonObject);
//        String statusCd = String.valueOf(jsonObject.get("statusCd"));
//        if (StrUtil.equalsAny(statusCd, CommonConstants.FIRST_TRIAL, CommonConstants.REEXAMINE)) {
//            one.setStatusCd(WorksConstants.WORKS_REVIEW);
//        } else if (statusCd.equals(CommonConstants.EVIDENCE)) {
//            one.setStatusCd(WorksConstants.WORKS_EVIDENCE);
//        } else if (statusCd.equals(CommonConstants.COPYRIGHT_OFFICE_EXAMINE)) {
//            one.setStatusCd(WorksConstants.WORKS_REVIEW);
//            JSONObject data = evidenceAndRegisterController.WorksRegistrationData(dto);
//            WorksRegisterData dataDto = JSON.parseObject(data.toString(), WorksRegisterData.class);
//            if (StrUtil.isEmpty(dataDto.getCertificateUrl())) {
//                dataDto.setCertificateUrl(depositWorksCertificate(dto));
//            }
//        } else if (statusCd.equals(CommonConstants.ENTRY_SUCCESS)) {
//            one.setStatusCd(WorksConstants.WORKS_DONE);
//            JSONObject data = evidenceAndRegisterController.WorksRegistrationData(dto);
//            WorksRegisterData dataDto = JSON.parseObject(data.toString(), WorksRegisterData.class);
//            if (StrUtil.isEmpty(dataDto.getCertificateUrl())) {
//                dataDto.setCertificateUrl(depositWorksCertificate(dto));
//            }
//            if (StrUtil.isEmpty(dataDto.getRegistrationCertificateUrl())) {
//                dataDto.setRegistrationCertificateUrl(registrationWorksCertificate(dto));
//            }
//        }else if (StrUtil.equalsAny(statusCd, CommonConstants.FIRST_TRIAL_REJECT, CommonConstants.REEXAMINE_REJECT, CommonConstants.COPYRIGHT_OFFICE_EXAMINE_REJECT)) {
//            one.setStatusCd(WorksConstants.WORKS_REJECT);
//            one.setAuditOpinion(String.valueOf(jsonObject.get("auditOpinion")));
//        }
//        if (statusCd.equals(CommonConstants.EVIDENCE_FAIL)) {
//            one.setStatusCd(WorksConstants.WORKS_FAIL);
//        }
//        one.setStatusDate(LocalDateTime.now());
//        one.updateById();
//    }

    /***
     * 获取登记作品存证证书
     */
    public String depositWorksCertificate(WorkByAcceptNoDto workByAcceptNoDto) {
        JSONObject jsonObject = evidenceAndRegisterController.depositWorksCertificate(workByAcceptNoDto);
        return String.valueOf(jsonObject.get("certificateUrl"));
    }

    /***
     * 获取登记作品登记证书
     */
    public String registrationWorksCertificate(WorkByAcceptNoDto workByAcceptNoDto) {
        JSONObject jsonObject = evidenceAndRegisterController.registrationWorksCertificate(workByAcceptNoDto);
        return String.valueOf(jsonObject.get("certificateUrl"));
    }

    /***
     * 文创链数据
     */
    public BlockChinaCountDto blockChinaCount() {
        JSONObject jsonObject = blockChainController.blockChinaCount();
        return JSON.parseObject(String.valueOf(jsonObject), BlockChinaCountDto.class);
    }

    /***
     * 统一证据编号核验
     * @return
     */
    public JSONObject verificationWorkHash(worksHashVerificationDto worksHashVerificationDto) {
        JSONObject jsonObject = evidenceAndRegisterController.verificationWorkHsh(worksHashVerificationDto);
        return jsonObject;
    }

    /**--------------银联--------------*/

}
