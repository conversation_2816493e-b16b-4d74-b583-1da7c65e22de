package com.ruoyi.scwt.sop.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author：weiqian
 * @Date：2022/7/26 15:31
 */
@Data
@ApiModel(value = "查询著作权人")
public class OwnersByIdDto {

    @ApiModelProperty(value="平台用户ID", required = true)
    @NotNull(message = "平台用户ID不能为空")
    private String platformUserId;

    @ApiModelProperty(value="著作权人ID", required = true)
    @NotNull(message = "著作权人id不能为空")
    private Long ownerId;
}