package com.ruoyi.scwt.sop.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * 银联入网上传图片请求参数
 * <AUTHOR>
 * @date 2022/11/18 - 11:11
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "银联入网上传图片请求参数")
public class ChinaumsMerchantPicDto {

    @ApiModelProperty(value = "上传图片类型(参考在线文档中心-银联对接文档-上传图片类型说明)", required = true)
    @NotEmpty(message = "上传图片类型不能为空")
    private String documentType;

    @ApiModelProperty(value = "图片名称(例如:身份证正面)", required = true)
    @NotEmpty(message = "图片名称不能为空")
    private String documentName;

    @ApiModelProperty(value = "图片路径(由图片上传接口返回)", required = true)
    @NotEmpty(message = "图片路径不能为空")
    private String filePath;

    @ApiModelProperty(value = "图片大小(由图片上传接口返回)", required = true)
    @NotEmpty(message = "图片大小不能为空")
    private String fileSize;

    @ApiModelProperty(value = "备注", required = true)
    @Length(max = 200,message = "备注长度不能超过200")
    private String remark;
}
