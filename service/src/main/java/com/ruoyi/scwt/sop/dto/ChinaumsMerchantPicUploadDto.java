package com.ruoyi.scwt.sop.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * 银联图片资料上传请求参数
 * <AUTHOR>
 * @date 2022-11-7 - 15:52
 **/
@Data
@Accessors(chain = true)
@ApiModel(value = "银联图片资料上传请求参数")
public class ChinaumsMerchantPicUploadDto {

    @ApiModelProperty(value = "请求流水号", required = true)
    @NotEmpty(message = "请求流水号不能为空")
    @Length(max = 100,message = "请求流水号长度不能超过30")
    private String requestSeq;

    @ApiModelProperty(value = "图片base64编码(jpg、jpeg、png、bmg/图片大小不超过2M。并且必须带有前缀“data:image/”，且不能有换行符。)", required = true)
    @NotEmpty(message = "图片base64编码不能为空")
    private String base64;
}
