package com.ruoyi.scwt.sop.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ApiModel(value = "查询存证列表参数")
public class FileEvidenceListDto {

    @ApiModelProperty(value="名称")
    private String workName;

    @ApiModelProperty(value="受理号")
    private String acceptanceNumber;

    @ApiModelProperty(value="状态")
    private String statusCd;

    @ApiModelProperty(value = "起始时间")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "截止时间")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty(value="平台用户ID")
    private String platformUserId;
}