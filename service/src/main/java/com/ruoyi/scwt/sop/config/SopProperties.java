package com.ruoyi.scwt.sop.config;


import com.ruoyi.sop.config.YamlPropertySourceFactory;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023-03-21 11:41
 */
@Data
@Component
@ConfigurationProperties(prefix = "sop")
@PropertySource(value = {"classpath:sop.yml"}, factory = YamlPropertySourceFactory.class)
public class SopProperties {
    @ApiModelProperty(value = "访问路径")
    private String accessUrl;

    @ApiModelProperty(value = "平台应用id")
    private String appKey;

    @ApiModelProperty(value = "应用私钥")
    private String privateKey;

    @ApiModelProperty(value = "应用公钥")
    private String publicKey;

    @ApiModelProperty(value = "平台公钥")
    private String platformPublicKey;


    @ApiModelProperty(value = "银联code")
    private String unionCode;

    @ApiModelProperty(value = "商户登记存证注册")
    private String registerForeign;

    @ApiModelProperty(value = "文件上传")
    private String fileEvidenceFileUpload;

    @ApiModelProperty(value = "新增著作权人")
    private String fileEvidenceAddOwner;

    @ApiModelProperty(value = "查询著作权人列表")
    private String ownersGetOwnersList;

    @ApiModelProperty(value = "查询著作权人详情")
    private String ownersGetOwnersById;

    @ApiModelProperty(value = "通过id删除著作权人")
    private String ownersDelete;

    @ApiModelProperty(value = "字典查询")
    private String workDict;

    @ApiModelProperty(value = "登记-新增登记作品信息")
    private String workSave;

    @ApiModelProperty(value = "登记-更新登记作品信息")
    private String workUpdate;

    @ApiModelProperty(value = "登记-查询登记作品列表")
    private String workPage;

    @ApiModelProperty(value = "登记-根据受理号查询登记作品")
    private String workGetWorkByAcceptNo;

    @ApiModelProperty(value = "登记-根据受理号查询审核进度")
    private String workProgress;

    @ApiModelProperty(value = "登记-下载作品存证证书")
    private String workEvidence;

    @ApiModelProperty(value = "登记-下载作品登记证书")
    private String workRegistration;

    @ApiModelProperty(value = "登记-获取作品登记数据")
    private String workRegistrationData;

    @ApiModelProperty(value = "存证-新增存证")
    private String fileEvidenceBaseSave;

    @ApiModelProperty(value = "存证-查询存证作品列表")
    private String fileEvidencePage;

    @ApiModelProperty(value = "存证-查询存证作品详情")
    private String fileEvidenceInfo;

    @ApiModelProperty(value = "存证-下载作品存证证书")
    private String fileEvidenceEvidence;

    @ApiModelProperty(value = "核验-统一证据编号核验")
    private String verificationWorkHash;



    @ApiModelProperty(value = "用户注册，获取私钥地址和公钥地址")
    private String blockchainRegister;

    @ApiModelProperty(value = "文创链数据")
    private String blockchainBlockChinaCount;

    @ApiModelProperty(value = "资产发行")
    private String issueNft;



    @ApiModelProperty(value = "图片资料上传")
    private String chinaumsPicUpload;
    @ApiModelProperty(value = "商户入网1.1-银行四要素实名检查")
    private String picAgreementUpload;
    @ApiModelProperty(value = "个人商户入网v2.0")
    private String chinaumsAutoRegPerson;
    @ApiModelProperty(value = "企业商户入网V2.0")
    private String chinaumsAutoRegEnterprise;
    @ApiModelProperty(value = "商户入网2.0-采集入网用户档案资料信息上传")
    private String complexUpload;
    @ApiModelProperty(value = "商户入网3.0-发起对公账户验证交易接口(对公账户打款接口,非对公账户无需调用)")
    private String requestAccountVerify;
    @ApiModelProperty(value = "对公账户认证")
    private String chinaumsAccountVerify;
    @ApiModelProperty(value = "组装前台签约地址")
    private String chinaumsAgreementSignUrl;
    @ApiModelProperty(value = "订单支付V1")
    private String chinaumsOrderPay;
    @ApiModelProperty(value = "订单查询")
    private String chinaumsOrderQueryOrder;
    @ApiModelProperty(value = "退款")
    private String chinaumsOrderRefund;
    @ApiModelProperty(value = "关闭订单")
    private String chinaumsOrderClose;
    @ApiModelProperty(value = "入网状态查询")
    private String chinaumsApplyQry;




}
