package com.ruoyi.scwt.sop.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * 银联前台签约请求参数
 * <AUTHOR>
 * @date 2022-11-7 - 15:52
 **/
@Data
@ApiModel(value = "银联前台签约请求参数")
public class ChinaumsAgreementSignDto {

    @ApiModelProperty(value = "自助签约平台流水号")
    @NotEmpty(message = "自助签约平台流水号不能为空")
    @Length(max = 100,message = "自助签约平台流水号长度不能超过100")
    private String umsRegId;
}
