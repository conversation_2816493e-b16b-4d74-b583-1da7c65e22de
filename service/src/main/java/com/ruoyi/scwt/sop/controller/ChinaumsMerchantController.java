package com.ruoyi.scwt.sop.controller;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ruoyi.scwt.sop.config.SopProperties;
import com.ruoyi.scwt.sop.dto.*;
import com.ruoyi.scwt.sop.util.HttpUtil;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 银联订单接口
 * <AUTHOR>
 * @date 2022/11/7 - 14:02
 */
@Slf4j
@RestController
@RequestMapping("/chinaumsMerchant")
public class ChinaumsMerchantController {

    @Autowired
    private HttpUtil httpUtil;
    @Autowired
    private SopProperties sopProperties;

    @ApiOperation(value = "商户入网1.0-图片资料上传", tags = "银联入驻接口", position = 1)
    @PostMapping(value = "picUpload")
    public JSONObject merchantUpload(@RequestBody @Validated ChinaumsMerchantPicUploadDto picUploadDto) {
        String response = httpUtil.doPost(picUploadDto,  false,sopProperties.getChinaumsPicUpload(), "1.0", false);
        JSONObject result = JSONUtil.parseObj(response.replace("\"", "").replace("\\",""));
        return result;
    }

    @SneakyThrows
    @ApiOperation(value = "商户入网1.1-银行四要素实名检查", tags = "银联入驻接口", position = 2)
    @PostMapping(value = "picAgreementUpload")
    public JSONObject picAgreementUpload(@RequestBody @Validated ChinaumsMerchantPicAgreementUploadDto dto) {
        String response = httpUtil.doPost(dto,  false,sopProperties.getPicAgreementUpload(), "1.0", false);
        JSONObject result = JSONUtil.parseObj(response);
        return result;
    }
    @ApiOperation(value = "个人商户入网V2.0-进件", tags = "银联入驻接口", position = 3)
    @PostMapping(value = "autoRegPerson")
    public JSONObject autoRegPerson(@RequestBody @Validated ChinaumsMerchantAutoRegPersonDto dto) {
        String response = httpUtil.doPost(dto,  false,sopProperties.getChinaumsAutoRegPerson(), "2.0", false);
        JSONObject result = JSONUtil.parseObj(response);
        return result;
    }

    @ApiOperation(value = "企业商户入网V2.0-进件", tags = "银联入驻接口", position = 3)
    @PostMapping(value = "autoRegEnterprise")
    public JSONObject autoRegEnterprise(@RequestBody @Validated ChinaumsMerchantAutoRegEnterpriseDto dto) {
        String response = httpUtil.doPost(dto,  false,sopProperties.getChinaumsAutoRegEnterprise(), "2.0", false);
        JSONObject result = JSONUtil.parseObj(response);
        return result;
    }
    @SneakyThrows
    @ApiOperation(value = "商户入网2.0-采集入网用户档案资料信息上传", tags = "银联入驻接口", position = 3)
    @PostMapping(value = "complexUpload")
    public JSONObject complexUpload(@RequestBody @Validated ChinaumsMerchantComplexUploadDto dto) {
        String response = httpUtil.doPost(dto,  false,sopProperties.getComplexUpload(), "1.0", false);
        JSONObject result = JSONUtil.parseObj(response.replace("\\", "").replace("\"{", "{").replace("}\"", "}"));
        return result;
    }

    @SneakyThrows
    @ApiOperation(value = "商户入网3.0-发起对公账户验证交易接口(对公账户打款接口,非对公账户无需调用)", tags = "银联入驻接口", position = 4)
    @PostMapping(value = "requestAccountVerify")
    public JSONObject requestAccountVerify(@RequestBody @Validated ChinaumsAgreementSignDto dto) {
        String response = httpUtil.doPost(dto,  false,sopProperties.getRequestAccountVerify(), "1.0", false);
        JSONObject result = JSONUtil.parseObj(response.replace("\\", "").replace("\"{", "{").replace("}\"", "}"));
        return result;
    }
    @ApiOperation(value = "商户入网4.0-对公账户认证接口(对公账户金额验证接口,非对公账户无需调用)", tags = "银联入驻接口", position = 5)
    @PostMapping(value = "companyAccountVerify")
    public JSONObject companyAccountVerify(@RequestBody @Validated CompanyAccountVerifyDto verifyDto) {
        String response = httpUtil.doPost(verifyDto,  false,sopProperties.getChinaumsAccountVerify(), "1.0", false);
        JSONObject result = JSONUtil.parseObj(response.replace("\\", "").replace("\"{", "{").replace("}\"", "}"));
        return result;
    }

    @ApiOperation(value = "商户入网5.0-组装前台签约地址接口", tags = "银联入驻接口", position = 6)
    @PostMapping(value = "complexAgreementSign")
    public String complexAgreementSign(@RequestBody @Validated ChinaumsAgreementSignDto signDto) {
        String response = httpUtil.doPost(signDto,  false,sopProperties.getChinaumsAgreementSignUrl(), "1.0", false);
        return response.replace("\"", "");
    }

    @ApiOperation(value = "订单支付", notes = "订单支付", position = 1, tags = "银联订单接口")
    @PostMapping("/pay")
    public String pay(@RequestBody @Validated ChinaumsOrderDto dto) {
        String response = httpUtil.doPost(dto,  false,sopProperties.getChinaumsOrderPay(), "1.0", true);
        return response;
    }

    @ApiOperation(value = "订单查询", notes = "订单查询", position = 3, tags = "银联订单接口")
    @PostMapping("/queryOrder")
    public JSONObject queryOrder(@Validated QueryDto queryDto) {
        String response = httpUtil.doPost(queryDto,  false,sopProperties.getChinaumsOrderQueryOrder(), "1.0", false);
        JSONObject result = JSONUtil.parseObj(response.replace("\\", "").replace("\"{", "{").replace("}\"", "}"));
        return result;
    }


    @ApiOperation(value = "商户入网6.0-入网状态查询接口", tags = "银联入驻接口", position = 7)
    @PostMapping(value = "complexApplyQry")
    public JSONObject complexApplyQry(@RequestBody @Validated ChinaumsAgreementSignDto signDto) {
        String response = httpUtil.doPost(signDto,  false,sopProperties.getChinaumsApplyQry(), "1.0", false);
        JSONObject result = JSONUtil.parseObj(response.replace("\\", "").replace("\"{", "{").replace("}\"", "}"));
        return result;
    }
    public String getUnionCode(){
        return sopProperties.getUnionCode();
    }

}

