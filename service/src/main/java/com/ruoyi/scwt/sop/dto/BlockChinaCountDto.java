package com.ruoyi.scwt.sop.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author：weiqian
 * @Date：2022/7/26 15:31
 */
@Data
@ApiModel(value = "上链数据")
public class BlockChinaCountDto {

    /**
     * 用户总数
     */
    @ApiModelProperty(value="用户总数")
    private String userTotal;

    /***
     * 区块高度
     */
    @ApiModelProperty(value = "区块高度")
    private String blockTotal;

    /***
     * 上链总数
     */
    @ApiModelProperty(value = "上链总数")
    private String transactionTotal;

    /***
     * 节点总数
     */
    @ApiModelProperty(value="节点总数")
    private String nodeTotal;

}