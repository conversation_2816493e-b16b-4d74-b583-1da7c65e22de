package com.ruoyi.scwt.sop.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author：weiqian
 * @Date：2022/7/26 16:48
 */
@Data
@ApiModel(value = "著作权人")
public class OwnerDto extends Model<OwnerDto> {

    @TableId
    @ApiModelProperty(value = "主键id", hidden = true)
    private Integer id;

    @ApiModelProperty(value = "著作权人应用场景类型（存证：evidence，登记：registration）", required = true)
    @NotBlank(message = "著作权人应用场景类型不能为空")
    private String ownerSceneType;

    @ApiModelProperty(value = "著作权人名称（个人姓名or企业名称）", required = true)
    @NotBlank(message = "著作权人名称不能为空")
    private String ownerName;

    @ApiModelProperty(value = "著作权人证件号码（身份证号or营业执照编码）", required = true)
    @NotBlank(message = "证件号码不能为空")
    private String idNo;

    @ApiModelProperty(value = "著作权人类型（个人: person, 企业: enterprise）", required = true)
    @NotBlank(message = "著作权人类型不能为空")
    private String ownerType;

    @ApiModelProperty(value = "证件类型（身份证：identityCard，营业执照：busiLicense）", required = true)
    @NotBlank(message = "证件类型不能为空")
    private String papersType;

    @ApiModelProperty(value = "法人名称(证件类型为营业执照时必传)")
    private String legalName;

    @ApiModelProperty(value = "署名情况(存证不传，登记著作权人时必传)")
//    @NotBlank(message = "署名情况不能为空")
    private String signature;

    @ApiModelProperty(value = "平台用户主键id", required = true)
    @NotBlank(message = "平台用户主键id能为空")
    private String platformUserId;

    @ApiModelProperty(value = "身份正面附件id(存证不传，登记著作权人时必传)")
    private String frontAttachId;

    @ApiModelProperty(value = "身份背面附件id(存证不传，登记著作权人时必传)")
    private String backAttachId;

    @ApiModelProperty(value = "营业执照附件id(存证不传，登记著作权人时必传)")
    private String businessAttachId;

    @ApiModelProperty(value = "其他文件id")
    private String otherfilesAttachId;

    @ApiModelProperty(value = "国家(存证不传，登记著作权人时必传)")
    private String country;

    @ApiModelProperty(value = "省(存证不传，登记著作权人时必传)")
//    @NotBlank(message = "省不能为空")
    private String province;

    @ApiModelProperty(value = "市(存证不传，登记著作权人时必传)")
//    @NotBlank(message = "市不能为空")
    private String city;
}

