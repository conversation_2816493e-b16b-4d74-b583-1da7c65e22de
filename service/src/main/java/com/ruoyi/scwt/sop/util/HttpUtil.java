package com.ruoyi.scwt.sop.util;



import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.ruoyi.scwt.sop.config.SopProperties;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import org.codehaus.jackson.map.ObjectMapper;
import java.io.File;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.security.PublicKey;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
@Component
@Slf4j
@RequiredArgsConstructor
public class HttpUtil {

    private final SopProperties sopProperties;

    private static final String charset = "utf-8";

    /**
     * 发送请求
     * @param arrayFlag 是否数组JSON标识
     * @return
     */
    public String doPost(Object bean,  Boolean arrayFlag,String method,String version, Boolean stringFlag) {
        try {
            String response=getResponse(method);
            Map<String, Object> body = getRequestMap(bean, method, version,"POST");
            log.info(body.toString());
            String reqBody = HttpRequest.post(sopProperties.getAccessUrl())
                    .form(body)
                    .timeout(10000)
                    .execute()
                    .body();
            // 验签
            signCheck(reqBody, response);
            // 结果json解析
            return resultJsonAnalysis(reqBody, response, arrayFlag,stringFlag);
        } catch (HttpException e) {
            log.error("开放平台请求异常:{}", e.getMessage());
            throw new RuntimeException("请求异常，请联系管理员");
        }
    }
    /**
     * 文件上传
     * @param arrayFlag 是否数组JSON标识
     * @return
     */
    public String doPostUpload(Object bean,  Boolean arrayFlag,String method,String version,File file, Boolean stringFlag) {
        try {
            String response=getResponse(method);
            Map<String, Object> body = getRequestMap(bean, method, version,"POST");
            log.info(body.toString());
            String reqBody = HttpRequest.post(sopProperties.getAccessUrl())
                    .form(body)
                    .form("file",file)
                    .timeout(10000)
                    .execute()
                    .body();
            // 验签
            signCheck(reqBody, response);
            // 结果json解析
            return resultJsonAnalysis(reqBody, response, arrayFlag,stringFlag);
        } catch (HttpException e) {
            log.error("开放平台请求异常:{}", e.getMessage());
            throw new RuntimeException("请求异常，请联系管理员");
        }
    }

    /**
     * 发送请求
     * @param arrayFlag 是否数组JSON标识
     * @return
     */
    public String doGet(Object bean, Boolean arrayFlag,String method,String version, Boolean stringFlag) {
        try {
            String response=getResponse(method);
            Map<String, Object> body = getRequestMap(bean, method, version,"GET");
//            System.out.println(body);
            String reqBody = HttpRequest.get(sopProperties.getAccessUrl())
                    .form(body)
                    .timeout(10000)
                    .execute()
                    .body();
            // 验签
            signCheck(reqBody, response);
            // 结果json解析
            return resultJsonAnalysis(reqBody, response, arrayFlag,stringFlag);
        } catch (HttpException e) {
            log.error("开放平台请求异常:{}", e.getMessage());
            throw new RuntimeException("请求异常，请联系管理员");
        }
    }

    private String getResponse(String method){
        String[] strings = method.split("\\.");
        StringBuilder response = new StringBuilder();
        for (int i = 0; i < strings.length; i++) {
            response.append(strings[i]).append(i == strings.length - 1 ? "_response" : "_");
        }
        return response.toString();
    }

    /**
     * 组装请求参数
     * @param bean 组装请求对象
     * @return
     */
    @SneakyThrows
    public Map<String, Object> getRequestMap(Object bean, String method, String version, String httpMethod) {
//        Map<String, Object> contentMap = BeanUtil.beanToMap(bean);
        ObjectMapper objectMapper=new ObjectMapper();
        String contentStr = objectMapper.writeValueAsString(bean);
        Map<String,Object>params=new HashMap<>();
        // 公共请求参数
        params.put("app_id", sopProperties.getAppKey());
        params.put("version", version);
        params.put("method", method);
        params.put("format", "json");
        params.put("charset", "utf-8");
        params.put("sign_type", "RSA2");
        params.put("httpMethod", httpMethod);
        params.put("timestamp", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        params.put("biz_content",contentStr);
        String content = getSignContent(params);
//        System.out.println("待签名内容"+content);
        String sign = null;
        try {
            sign = SignatureUtil.rsa256Sign(content, sopProperties.getPrivateKey(), charset);
        } catch (Exception e) {
            throw new RuntimeException("构建签名失败", e);
        }
        params.put("sign", sign);
        // 公钥验签
//        boolean b = SignatureUtil.rsa256CheckContent(content, sign, sopProperties.getPublicKey(), charset);
//        System.out.println("验签结果：" + b);
        return params;
    }

    /**
     * 业务字段字母升序排序
     * @param sortedParams 业务字段
     * @return
     */
    private String getSignContent(Map<String, Object> sortedParams) {
        StringBuffer content = new StringBuffer();
        List<String> keys = new ArrayList<String>(sortedParams.keySet());
        Collections.sort(keys);
        int index = 0;
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = sortedParams.get(key).toString();
            if (StringUtil.areNotEmpty(key, value)) {
                content.append((index == 0 ? "" : "&") + key + "=" + value);
                index++;
            }
        }
        return content.toString();
    }

    /**
     * 平台公钥验签
     * @param reqBody 响应body
     * @param response 响应标识
     */
    private void signCheck(String reqBody, String response) {
        try {
            JSONObject result = JSONUtil.parseObj(reqBody);
            System.out.println(reqBody);
            String sign = result.get("sign").toString();
            boolean flag = SignatureUtil.rsa256CheckContent(
                    reqBody.replace("{\"" + response + "\":", "").replace(",\"sign\":\"" + sign + "\"}", ""),
                    sign,
                    sopProperties.getPlatformPublicKey(),
                    charset);
            if (!flag) {
                log.error("开放平台验签失败");
                throw new RuntimeException("请求异常，请联系管理员");
            }
        } catch (Exception e) {
            log.error("开放平台验签异常:{}", e.getMessage());
            throw new RuntimeException("请求异常，请联系管理员");
        }
    }
    /**
     *
     * 结果json解析
     * @param reqBody 返回结果
     * @param response 响应标识
     * @param arrayFlag 是否数组JSON标识
     * @return
     */
    private String resultJsonAnalysis(String reqBody, String response, Boolean arrayFlag, Boolean stringFlag) {
        JSONObject result = JSONUtil.parseObj(reqBody);
        JSONObject responseJson = Optional.ofNullable(result.get(response, JSONObject.class))
                .orElseThrow(() -> new RuntimeException("请求异常，请联系管理员"));
        if (!"10000".equals(responseJson.get("code"))) {
            log.error("开放平台请求异常:{}", reqBody);
            if (responseJson.containsKey("sub_msg")) {
                throw new RuntimeException(responseJson.getStr("sub_msg"));
            } else if (responseJson.containsKey("msg")) {
                throw new RuntimeException(responseJson.getStr("msg"));
            }
            throw new RuntimeException("请求异常，请联系管理员");
        }
        if (arrayFlag) {
            JSONArray data = responseJson.get("data", JSONArray.class);
            return JSON.toJSONString(data);
        }
        if (stringFlag){
            return responseJson.get("data").toString();
        }
        JSONObject dataJson = responseJson.get("data", JSONObject.class);
        if (Objects.isNull(dataJson)) {
            return JSON.toJSONString(responseJson);
        }
        Object dataTwoJson = dataJson.get("data");
        return JSON.toJSONString(Objects.isNull(dataTwoJson) ? dataJson : dataTwoJson);
    }


}
