package com.ruoyi.scwt.sop.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 银联下单请求参数
 * <AUTHOR>
 * @date 2022-09-21
 **/
@Data
@ApiModel(value = "银联下单请求参数")
public class ChinaumsOrderDto {

    @ApiModelProperty(value = "订单号", required = true)
    private String orderNo;

    @ApiModelProperty(value = "订单金额", required = true)
    private BigDecimal orderMoney;

    @ApiModelProperty(value = "支付渠道（云闪付H5：uacH5Pay，云闪付APP：uacAppPay，支付宝h5：aliH5，支付宝APP：aliApp，支付宝二维码：aliPc" +
            "，h5微信：wxH5Pay，微信APP：wxApp，微信小程序：wxAppletPay，微信公众号：wxJSPay，微信二维码：wxPc）", required = true)
    private String payChannel;

    @ApiModelProperty(value = "自行组装上报字段")
    private String srcReserve;

    @ApiModelProperty(value = "返回地址", required = true)
    private String returnUrl;

    @ApiModelProperty(value = "收款商户号(使用场景：用户A向用户B付款)")
    private String merchantNo;

    @ApiModelProperty(value = "过期时间(格式：yyyy-MM-dd HH:mm:ss)，默认过期时间5分钟")
    private String expireTime;

    @ApiModelProperty(value = "订单时间(格式：yyyy-MM-dd)")
    private String billDate;

    @ApiModelProperty(value = "订单备注")
    private String billDesc;
}
