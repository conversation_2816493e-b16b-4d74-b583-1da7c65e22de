package com.ruoyi.web.core.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new HandlerInterceptorAdapter() {
            @Override
            public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
                response.setHeader("Referrer-Policy", "strict-origin-when-cross-origin");
                response.setHeader("X-Content-Type-Options", "nosniff");
                response.setHeader("X-Permitted-Cross-Domain-Policies", "none");
//                response.setHeader("Content-Security-Policy", "default-src 'self'; script-src 'self'; style-src 'self'; object-src 'none'");
                response.setHeader("X-Download-Options", "noopen");
                response.setHeader("X-Frame-Options", "SAMEORIGIN");
                response.setHeader("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload");
            }
        });
    }
}