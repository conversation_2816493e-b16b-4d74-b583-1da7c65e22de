# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.8
  # 版权年份
  copyrightYear: 2024
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/ruoyi/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math
  # 活动相关配置
  activity:
    # 邀请排行榜需要排除的用户名前缀
    invite-rank:
      exclude-prefixes:
        - "1700"
        - "1701"
        - "1702"
        - "1703"
        - "1705"
        - "1706"
        - "1704"
        - "1707"
        - "1708"
        - "1709"
        - "171"
        - "167"
        - "147"
        - "190"
        - "1928"
        - "1927"
        - "1926"
        - "1922"
        - "1301"
        - "1302"
        - "1306"
        - "1300"
        - "1309"
        - "1305"
        - "1307"
        - "1921"
        - "1308"
        - "1304"
        - "1303"
        - "1923"
        - "1929"
        - "1920"
        - "1626"
        - "1925"
        - "1627"
        - "1655"
        - "1625"
        - "1628"
        - "1623"
        - "18280358345"
        - "15379574725"
        - "18166210447"
        - "18211092203"
        - "15363190492"
        - "17722518179"
        #- "162"
        #- "165"
        #- "192"

      # 排行榜显示前多少名（不设置则显示全部）
      display-limit: 100
  # 短信服务配置
  sms:
    # 验证码发送限制配置
    code:
      # 禁止发送验证码的手机号前缀（黑名单）
      blocked-prefixes:
        - "1700"
        - "1701"
        - "1702"
        - "162"
        - "1703"
        - "1705"
        - "1706"
        - "165"
        - "1704"
        - "1707"
        - "1708"
        - "1709"
        - "171"
        - "167"
        - "147"
        - "190"
        - "192"

# 开发环境配置
server:
  workId: 0
  datacenterId: 0
  # 服务器的HTTP端口，默认为8080
  port: 9020
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 1024MB
      # 设置总上传的文件大小
      max-request-size: 1024MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    key-prefix: 'cwsf:'
    # 地址
    host: ************
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password: 'dE2fL8De4i.S!23'
    # password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30
  # 是否允许并多设备登录   true:允许  false:不允许  这个是新增的配置
  singleLogin: false

# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# 阿里云配置
ali:
  #短信服务
  sms:
    accessKey: fff
    accessId: LTAI5tEyQUevQaNrN9Vt9JqQ
    accessSecret: ******************************
    servicePoint: dysmsapi.aliyuncs.com
    signName: 四川省文化数据服务
  market:
    AppKey: *********
    AppSecret: 4ntyILhE21hzo8loa8LW7OynRRSzPmTX
    AppCode: ********************************
  ocr:
    domain: https://cardpack.market.alicloudapi.com
  # 身份证二要素验证
  eid:
    domain: https://eid.shumaidata.com
  # 手机三要素认证
  mobile:
    domain: https://mobile3elements.shumaidata.com
  # 企业三要素认证
  company:
    domain: https://smkjqysys.market.alicloudapi.com
#腾讯云配置
tencent:
  #对象存储
  oss:
    secretId: LTAI5tHNNoa6GWJiw5XZgKEW
    secretKey: ******************************
    region: cn-shenzhen
    bucketName: trade-sccdex
    endpoint: https://oss-cn-shenzhen.aliyuncs.com
    downUrl: https://trade.sccdex.com

copyright:
  id: copyright-cdbq
  secret: 566a569e-1784-4979-9db9-e3629fe6e73b
  code: 519904
  callback: https://dsc.tianfutv.com/api/works/callback
  url_prefix: https://api.sc-copyright.org/
  temp: /opt/bmark/jar/temp/
  save_sc: http://*************:8101/blockchain/copyright/save

# 支付宝支付
alipay:
  # 支付宝网关名、partnerId和appId
  openApiDomain: https://openapi.alipay.com/gateway.do
  mcloudApiDomain: http://mcloudmonitor.com/gateway.do
  signType: RSA2
  #异步通知url(注意拦截器是否拦截)
  notifyUrl: https://pay.sccdex.com/notify/aliPay
  app_id: 2021004199616432
  private_key: MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCXgBZlJh74YhbPWO4GsZyOQL1YeUpxH6RSyeT4NW10upKDppdiyJ1wCtHCOoG8/C8B2WSZa2Zr/IzO6G5GiV+E2eHohu6VlgLUJH/jC0Y3lfMOINXI03msNE9eqBf9TLA+MuxONphOqvUO0Sx6nFZvyV/pq2OCLt2D075whH6arMv/d1BNP9EIl+OPH5eT7PQEZYvRfjWMEmGf8rjSplopgRU+6FVDQrl+NepUMSQNUd6FI28HQqLtTxL0naPBsWFtQ6NXmq5iBtdMLIFD2Iqma7UEEJJFuN5JBTdf4hX7vHLayhtMcOs4ZqCk55nO9fKYKRVgk9sTz2Alk3HgmzWtAgMBAAECggEAYptb+Dn3rnMppYucYX75Bx3cH9nSOg7+RWRjQ6qCs5c8BbRIlGM/Fc30+RZbm0YI0otMjw74exDGFWXzYLXJFg86mr44S+R3styO4JhV+5f9uxRkEXKTnyHtBw8hjhx8Gf/LDp06RPnAxwQejy9ykw/e/ZgwDGu0gjkvmsyiGsmbid8ooj1FTAkB3S7G8pHgm4DAxqdYG0o9OE+4YLLMwfPpG+NXjLoaoIcN3Z5fxxH+ptrZzEk1q/uOolljcMc+hyDSH1LQnG3FUuTDzaBXrIjp/43GL9QMFmI/E8I4sEgktxvFJv0mvU+H17bhE3IWqECYwAIo2o0A3jdYXSJRgQKBgQDcHAwkdGNfWDXN9pHY6ooeF7n0d8C44BiPfPO6HOYeY1lVuNTJKiLBjCifvM5PQlkgSj5tAl6ALqb7XIEj/JNRwr5W0Eoq5wEWCgqxVCsGUaoT8zUG9dazSnpEPtYGQ2m5InKb2SyGFUAYpZkFJ9pRj2BVB+Q9p86mI2JTMkOO3wKBgQCwNBxSDmg1ttOxgHKkJqT6JCn7K8q+6Zv0TlI+XxH3Fp6POZEHrj1/8p4zhH3s9U6/ComW0EB/wsc8et8zd0YbfKAIvBkbruKZgBpg0nYs3zgt+m/2fT3MotmPhCLXJZftxQOYZmLZKuQNPDGf6/AqYfJrdSPAM8ssNbUdBZBo8wKBgQCZMLwSsx5mQVJRIoanXbT69LBrQN1UzkLJusE2it5tz99hFd6ikzzVT+MGKbW83ASDwKhp3fg/Zji5QMxwa11szgkDCrxT16qd2CdQ+69mGngwolgJnBfteKxXyxPSECEIxLzXUvL17txJSxeQywfbM9pr0646awwb/Qx1ssnqKQKBgQCDb3uooeKOHsCuaTgaxlDN3mgKWdI0sqGIjVu/9r6553+DKtfKOOjbJiloGG6ucRW6X6veeZ2DL9HahXU1OMVs/3uIyHaaNcwF4I7MpnFMa33GnkciQK32lYtgCJrMjdhUnl9eMRGPGPVgAgSqXB6cHs/r5Ec0ue3WHoHpEht0jQKBgQC6yJIzDs8wyH9A0l0gVZ28763ZicpLIS6PwHgEkXB2LKQqUJ1yA/EqUuiIJAtN+uPT12ymM9yTnTcq7lf/0Bgu75USWPwC29AdAkyetYlj/rZdsdH82g1Lpgwb333T/I3Rq/1wIcGj4HvLO4ZL+JAdnolnQ7kOp/giBU1JuTOWwA==
  charset: UTF-8
  alipay_public_key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuWsQQUjQUbNstYmERcq10/JCxZWC2PJwARjKTac+sQQVjgEuOHiiigUgQgeVR7qOHaLLdLMiNKkVxi8Rtlx9mCMT7TQWgpsf8rcTnNASiQ+QvWt6H0IzTgenBNDWwXM8fhWh0pHhu+OxZP2rlwFl/XEp9aiUk1o+OypkHHod41w/03vsS07lGKzCCCI14mydOzQmylWgNuG4xhnBSm2WvefOBo00/zMvWh2vitrZvQ8B93EFqYULA3RWZaagAPYjVtFgvFYHVYhtUXFokPQz1KhkjQMewL+t4JSG/xYoOrDd48wVr+ohxy5WPn5JxaMe/iY9Xnl1FO+Yqubbl1tg2wIDAQAB
  h5_return_url: https://lysz.sccdex.com/profile
  h5_notify_url: https://pay.sccdex.com/h5/notify/aliPay

# 微信支付
wechat:
  pay:
    appid: wx5c9fd942580a9977
    merchantId: 1699616255
    privateKeyPath: /path/to/remote/directory/apiclient_key.pem
    merchantSerialNumber: 6E12A6D6E8E38F16688EE34BD66C0B5A92B24DFC
    apiV3Key: sichuanshengwenhuashujufuwugongs
    notifyUrl: https://pay.sccdex.com/notify/wxPay

# 定时任务
scheduling:
  enabled: false

font:
  file: /path/to/remote/directory/simsun.ttc
sm4:
  secret-key: 0123456789abcdef0123456789abcdef