# 短信验证码手机号黑名单功能

## 修改概述

**修改时间：** 2025-01-29
**修改目的：** 在发送短信验证码时添加手机号黑名单校验，基于配置文件控制禁止发送验证码的手机号范围
**影响模块：** 短信服务 - 发送验证码功能
**接口路径：** `GET /sms/sendPhoneCode`

## 需求背景

为了控制短信验证码的发送范围，需要对手机号进行黑名单校验。配置中的手机号前缀将被禁止接收短信验证码，这样可以：

1. **成本控制**：阻止特定号段发送验证码，降低短信费用
2. **安全防护**：防止测试号段或虚拟号段滥用验证码
3. **业务控制**：根据业务需要禁止特定号段的用户
4. **灵活配置**：通过配置文件动态调整禁止的号段

## 技术方案

采用**配置驱动的黑名单校验**方案：
- 在`application.yml`中配置禁止的手机号前缀（黑名单）
- 创建专门的`SmsConfig`配置类
- 在发送验证码前进行黑名单校验
- 支持配置为空时不进行任何限制

## 具体修改内容

### 1. 扩展配置文件

**文件：** `ruoyi-admin/src/main/resources/application.yml`

**新增配置：**
```yaml
ruoyi:
  # 短信服务配置
  sms:
    # 验证码发送限制配置
    code:
      # 禁止发送验证码的手机号前缀（黑名单）
      blocked-prefixes:
        - "1700"
        - "1701"
        - "1702"
        - "162"
        - "1703"
        - "1705"
        - "1706"
        - "165"
        - "1704"
        - "1707"
        - "1708"
        - "1709"
        - "171"
        - "167"
```

### 2. 创建专门的短信配置类

**文件：** `service/src/main/java/com/ruoyi/scwt/common/config/SmsConfig.java`

**新增内容：**
```java
package com.ruoyi.scwt.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 短信服务配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "ruoyi.sms")
public class SmsConfig {

    /**
     * 验证码相关配置
     */
    private Code code = new Code();

    /**
     * 验证码配置类
     */
    @Data
    public static class Code {
        /**
         * 禁止发送验证码的手机号前缀列表（黑名单）
         */
        private List<String> blockedPrefixes;
    }
}
```

### 3. 修改短信控制器

**文件：** `service/src/main/java/com/ruoyi/scwt/common/controller/SmsController.java`

**修改内容：**

1. 添加依赖注入：
```java
@Autowired
private SmsConfig smsConfig;
```

2. 在`sendPhoneCode`方法开始处添加黑名单校验：
```java
@GetMapping("/sendPhoneCode")
@Anonymous
public AjaxResult sendPhoneCode(String phone, String scene){
    // 校验手机号前缀（黑名单检查）
    if (isBlockedPhoneNumber(phone)) {
        log.warn("手机号在黑名单中，禁止发送验证码，手机号: {}", phone);
        return AjaxResult.error("该手机号暂时无法接收验证码，请联系客服");
    }

    // ... 原有逻辑
}
```

3. 添加黑名单校验方法：
```java
/**
 * 检查手机号是否在黑名单中
 *
 * @param phone 手机号
 * @return true-在黑名单中（禁止发送），false-不在黑名单中（允许发送）
 */
private boolean isBlockedPhoneNumber(String phone) {
    if (StringUtils.isBlank(phone)) {
        return true; // 空手机号视为禁止
    }

    // 获取配置的禁止发送验证码的手机号前缀
    List<String> blockedPrefixes = smsConfig.getCode().getBlockedPrefixes();

    // 如果配置为空，表示没有黑名单，所有手机号都允许发送
    if (blockedPrefixes == null || blockedPrefixes.isEmpty()) {
        log.info("短信验证码黑名单配置为空，允许所有手机号发送验证码");
        return false;
    }

    // 检查手机号是否以配置的黑名单前缀开头
    boolean isBlocked = blockedPrefixes.stream()
        .anyMatch(prefix -> phone.startsWith(prefix));

    if (isBlocked) {
        log.warn("手机号在黑名单中，禁止发送验证码，手机号: {}，黑名单前缀: {}", phone, blockedPrefixes);
    } else {
        log.info("手机号不在黑名单中，允许发送验证码，手机号: {}", phone);
    }

    return isBlocked;
}
```

## 功能特性

### 1. 黑名单驱动校验
- **有配置时**：匹配黑名单前缀的手机号禁止发送验证码
- **无配置时**：不进行黑名单校验，所有手机号都可以发送
- **灵活控制**：可以通过配置文件动态调整禁止的号段

### 2. 用户友好的错误提示
- 黑名单校验失败时返回："该手机号暂时无法接收验证码，请联系客服"
- 不暴露具体的黑名单规则，保护业务逻辑
- 统一的错误处理机制

### 3. 完善的日志记录
- 记录允许发送的手机号
- 记录被黑名单拦截的手机号和黑名单前缀
- 记录配置为空时的处理逻辑

## 使用场景

### 场景1：禁止特定号段
```yaml
ruoyi:
  sms:
    code:
      blocked-prefixes:
        - "1700"
        - "1701"
        - "162"
```
以1700、1701、162开头的手机号无法接收验证码。

### 场景2：测试环境保护
```yaml
ruoyi:
  sms:
    code:
      blocked-prefixes:
        - "1700"
        - "1701"
        - "1702"
```
禁止测试号段接收验证码，避免测试数据污染。

### 场景3：不进行限制
```yaml
# 方式1：不配置sms节点
ruoyi:
  name: RuoYi
  version: 3.8.8

# 方式2：配置空数组
ruoyi:
  sms:
    code:
      blocked-prefixes: []
```
所有手机号都可以接收验证码。

## 接口响应

### 校验通过
```json
{
    "code": 200,
    "msg": "短信验证码发送成功，请注意查收！",
    "data": null
}
```

### 黑名单校验失败
```json
{
    "code": 500,
    "msg": "该手机号暂时无法接收验证码，请联系客服",
    "data": null
}
```

## 测试验证

### 1. 有黑名单配置的情况
- 配置特定黑名单前缀
- 使用黑名单前缀的手机号测试，验证被拒绝
- 使用非黑名单前缀的手机号测试，验证能正常发送

### 2. 无黑名单配置的情况
- 删除或注释配置项
- 使用任意手机号测试，验证都能正常发送

### 3. 边界情况测试
- 空手机号（应该被拒绝）
- 非法格式手机号
- 配置为空数组的情况

## 安全考虑

### 1. 信息泄露防护
- 错误提示不暴露具体的黑名单规则
- 日志中记录详细信息，便于排查问题
- 前端用户只看到通用的错误提示

### 2. 性能优化
- 黑名单校验在业务逻辑之前执行
- 避免不必要的数据库查询和短信发送
- 使用Stream API提高匹配效率

### 3. 配置安全
- 配置文件中的黑名单信息需要妥善保管
- 生产环境的配置应该通过安全的方式管理
- 建议定期审查和更新黑名单配置

## 影响范围

### 直接影响
- 短信验证码发送接口
- 用户注册、登录、找回密码等流程

### 间接影响
- 短信费用控制
- 系统安全性提升
- 用户体验（黑名单中的用户无法接收验证码）

## 注意事项

1. **配置管理**：确保配置文件格式正确，注意YAML缩进
2. **用户沟通**：如果设置了黑名单，需要提前告知相关用户
3. **监控告警**：建议监控黑名单拦截的频率，及时发现异常
4. **回滚方案**：如果出现问题，可以快速清空黑名单配置恢复正常
5. **测试覆盖**：确保各种配置情况都有对应的测试用例

## 后续优化建议

1. **配置界面化**：开发管理界面，支持在线修改黑名单配置
2. **统计分析**：统计不同前缀的验证码拦截量和通过率
3. **动态配置**：接入配置中心，支持不重启应用修改配置
4. **白名单机制**：支持特定手机号的白名单，绕过黑名单校验

## 版本记录

| 版本 | 修改时间 | 修改人 | 修改内容 |
|------|----------|--------|----------|
| v1.0 | 2025-01-29 | 系统 | 初始实现短信验证码手机号黑名单功能 |

---

**文档维护：** 请在后续相关修改时及时更新本文档
