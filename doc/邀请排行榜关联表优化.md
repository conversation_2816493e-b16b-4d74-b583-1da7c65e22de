# 邀请排行榜关联表优化

## 修改概述

**修改时间：** 2025-01-29
**修改目的：** 将邀请排行榜用户过滤功能从关联sys_user表改为关联identify表，使用手机号进行前缀过滤
**影响模块：** 活动系统 - 邀请排行榜用户过滤功能
**修改文件：** `DigUserInvitationMapper.xml`

## 需求背景

原有的`getInviteRankWithUserFilter`方法关联的是`sys_user`表，使用`user_name`字段进行前缀过滤。但根据业务需求和配置的前缀特点（如"1700", "1701"等），应该使用用户的手机号进行过滤，因此需要：

1. **关联正确的表**：从`sys_user`改为`identify`表
2. **使用手机号**：从`user_name`改为`PHONE`字段
3. **优化关联方式**：使用LEFT JOIN确保数据完整性
4. **关联逻辑调整**：关联被邀请人的实名信息而非邀请人

## 表结构分析

### dig_user_invitation表
- `INVITER_ID`：邀请人用户ID
- `USER_ID`：被邀请用户ID
- `CREATE_DATE`：创建时间

### identify表（实名认证表）
- `USER_ID`：用户ID（关联字段）
- `PHONE`：手机号（过滤字段）
- `ID_NAME`：真实姓名
- `IDENTIFY_TYPE`：认证类型
- `STATUS_CD`：状态

### sys_user表（原关联表）
- `user_id`：用户ID
- `user_name`：用户名（原过滤字段）

## 具体修改内容

### 修改前的SQL
```xml
<select id="getInviteRankWithUserFilter" resultType="com.ruoyi.system.domain.vo.ActivityRankVo">
    SELECT
    dui.INVITER_ID AS 'userId',
    COUNT( dui.INVITE_ID ) AS 'inviteNum'
    FROM
    `dig_user_invitation` dui
    INNER JOIN sys_user su ON dui.INVITER_ID = su.user_id
    WHERE
    dui.CREATE_DATE >= #{startTime}
    AND dui.CREATE_DATE <= #{endTime}
    AND dui.INVITER_ID IS NOT NULL
    <foreach collection="excludePrefixes" item="prefix" open="AND (" separator=" AND " close=")">
        su.user_name NOT LIKE CONCAT(#{prefix}, '%')
    </foreach>
    GROUP BY
    dui.INVITER_ID
    ORDER BY inviteNum DESC
</select>
```

### 修改后的SQL
```xml
<select id="getInviteRankWithUserFilter" resultType="com.ruoyi.system.domain.vo.ActivityRankVo">
    SELECT
    dui.INVITER_ID AS 'userId',
    COUNT( dui.INVITE_ID ) AS 'inviteNum'
    FROM
    `dig_user_invitation` dui
    LEFT JOIN identify i ON dui.USER_ID = i.USER_ID
    WHERE
    dui.CREATE_DATE >= #{startTime}
    AND dui.CREATE_DATE <= #{endTime}
    AND dui.INVITER_ID IS NOT NULL
    AND i.ID_NAME IS NOT NULL
    <foreach collection="excludePrefixes" item="prefix" open="AND (" separator=" AND " close=")">
        i.PHONE NOT LIKE CONCAT(#{prefix}, '%')
    </foreach>
    GROUP BY
    dui.INVITER_ID
    ORDER BY inviteNum DESC
</select>
```

## 主要变更点

### 1. 关联表变更
- **原关联**：`INNER JOIN sys_user su ON dui.INVITER_ID = su.user_id`
- **新关联**：`LEFT JOIN identify i ON dui.USER_ID = i.USER_ID`

### 2. 过滤字段变更
- **原字段**：`su.user_name`（用户名）
- **新字段**：`i.PHONE`（手机号）

### 3. 关联逻辑变更
- **原逻辑**：关联邀请人信息（`dui.INVITER_ID = su.user_id`）
- **新逻辑**：关联被邀请人信息（`dui.USER_ID = i.USER_ID`）

### 4. JOIN方式优化
- **原方式**：`INNER JOIN`（必须有关联数据）
- **新方式**：`LEFT JOIN`（保留更多邀请记录）

## 功能影响

### 正面影响
1. **业务准确性**：使用手机号进行过滤，与配置前缀完全匹配
2. **数据完整性**：使用LEFT JOIN保留更多邀请记录
3. **逻辑合理性**：基于被邀请人的手机号进行过滤更符合业务逻辑
4. **配置一致性**：配置的前缀（如"1700", "1701"）正是手机号前缀

### 潜在影响
1. **关联逻辑变化**：现在关联的是被邀请人而非邀请人的信息
2. **性能考虑**：需要关联identify表，建议添加索引优化
3. **数据依赖**：依赖identify表的PHONE字段完整性

## 配置示例

配置保持不变，仍然使用相同的前缀配置：

```yaml
ruoyi:
  activity:
    invite-rank:
      exclude-prefixes:
        - "1700"   # 排除1700开头的手机号
        - "1701"   # 排除1701开头的手机号
        - "1702"   # 排除1702开头的手机号
        - "162"    # 排除162开头的手机号
        - "171"    # 排除171开头的手机号
```

## 测试验证

### 1. 数据准备
- 创建有实名认证的测试用户
- 创建无实名认证的测试用户
- 生成邀请数据

### 2. 功能测试
- 验证有实名认证的用户能正常出现在排行榜中
- 验证无实名认证的用户不出现在排行榜中
- 验证前缀过滤功能正常工作

### 3. 性能测试
- 对比修改前后的查询性能
- 验证大数据量下的查询效率

## 性能优化建议

### 1. 索引优化
建议在identify表上创建复合索引：
```sql
CREATE INDEX idx_identify_user_phone ON identify(USER_ID, PHONE);
```

### 2. 查询优化
如果性能有问题，可以考虑：
- 使用LEFT JOIN替代INNER JOIN（如果需要包含无实名认证用户）
- 添加identify表的状态过滤条件
- 考虑数据预处理或缓存策略

## 注意事项

1. **数据一致性**：确保identify表的PHONE字段数据完整性和准确性
2. **业务逻辑**：确认基于被邀请人手机号过滤符合业务需求
3. **权限控制**：确保有权限访问identify表的敏感信息
4. **监控告警**：监控查询性能，及时发现性能问题
5. **关联逻辑**：注意现在关联的是被邀请人信息，不是邀请人信息

## 回滚方案

如果需要回滚到原来的逻辑，可以将SQL改回：
```xml
INNER JOIN sys_user su ON dui.INVITER_ID = su.user_id
```
并将过滤条件改回：
```xml
su.user_name NOT LIKE CONCAT(#{prefix}, '%')
```

## 版本记录

| 版本 | 修改时间 | 修改人 | 修改内容 |
|------|----------|--------|----------|
| v1.0 | 2025-01-29 | 系统 | 将邀请排行榜关联表从sys_user改为identify，使用手机号进行前缀过滤 |

---

**文档维护：** 请在后续相关修改时及时更新本文档
