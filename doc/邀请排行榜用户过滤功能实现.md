# 邀请排行榜用户过滤功能实现

## 修改概述

**修改时间：** 2025-01-29  
**修改目的：** 在邀请排行榜统计中剔除特定前缀用户名的用户  
**影响模块：** 活动系统 - 邀请排行榜功能  

## 需求背景

在邀请排行榜统计时，需要剔除用户昵称中以以下前缀开头的用户：
- 1700、1701、1702、162、1703
- 1705、1706、165、1704、1707
- 1708、1709、171、167

## 技术方案

采用**方案三（混合方案）**：在数据库层面进行过滤，同时保持代码的扩展性和维护性。

### 方案优势
- **性能最优**：在数据库层面就过滤掉不需要的数据
- **扩展性好**：新增方法不影响现有功能
- **维护性强**：排除前缀可以通过配置管理
- **代码清晰**：逻辑分离，职责明确

## 具体修改内容

### 1. 修改Mapper接口

**文件：** `ruoyi-system/src/main/java/com/ruoyi/system/mapper/DigUserInvitationMapper.java`

**修改内容：** 添加新的查询方法

```java
/**
 * 获取邀请排行榜（过滤指定前缀的用户）
 * 
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @param excludePrefixes 需要排除的用户名前缀列表
 * @return 邀请排行榜列表
 */
List<ActivityRankVo> getInviteRankWithUserFilter(@Param("startTime") String startTime, 
                                                 @Param("endTime") String endTime,
                                                 @Param("excludePrefixes") List<String> excludePrefixes);
```

### 2. 添加SQL查询

**文件：** `ruoyi-system/src/main/resources/mapper/system/DigUserInvitationMapper.xml`

**修改内容：** 添加新的SQL查询方法

```xml
<select id="getInviteRankWithUserFilter" resultType="com.ruoyi.system.domain.vo.ActivityRankVo">
    SELECT
    dui.INVITER_ID AS 'userId',
    COUNT( dui.INVITE_ID ) AS 'inviteNum'
    FROM
    `dig_user_invitation` dui
    LEFT JOIN identify i ON dui.USER_ID = i.USER_ID
    WHERE
    dui.CREATE_DATE >= #{startTime}
    AND dui.CREATE_DATE <= #{endTime}
    AND dui.INVITER_ID IS NOT NULL
    AND i.ID_NAME IS NOT NULL
    <foreach collection="excludePrefixes" item="prefix" open="AND (" separator=" AND " close=")">
        i.PHONE NOT LIKE CONCAT(#{prefix}, '%')
    </foreach>
    GROUP BY
    dui.INVITER_ID
    ORDER BY inviteNum DESC
</select>
```

**SQL特点：**
- 使用LEFT JOIN连接实名认证表(identify)和邀请表
- 关联被邀请人的实名信息（`dui.USER_ID = i.USER_ID`）
- 使用MyBatis的`<foreach>`标签动态生成排除条件
- 通过`NOT LIKE CONCAT(#{prefix}, '%')`来排除指定前缀的手机号
- 添加`ID_NAME IS NOT NULL`条件确保有实名认证信息

### 3. 修改Service实现

**文件：** `asset-issuance/src/main/java/com/ruoyi/issue/activity/service/impl/DigActivityServiceImpl.java`

**修改内容：**

1. 添加import语句：
```java
import java.util.Arrays;
```

2. 修改`getInviteRank`方法：
```java
@Override
public List<ActivityRankVo> getInviteRank(Long activityId) {
    // 定义需要排除的用户名前缀
    List<String> excludePrefixes = Arrays.asList(
        "1700", "1701", "1702", "162", "1703", 
        "1705", "1706", "165", "1704", "1707", 
        "1708", "1709", "171", "167"
    );
    
    DigActivity activity = digActivityMapper.selectDigActivityByActivityId(activityId);
    List<ActivityRankVo> inviteRank = digUserInvitationMapper.getInviteRankWithUserFilter(
        activity.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), 
        activity.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
        excludePrefixes
    );
    
    inviteRank.forEach(item -> {
        SysUser sysUser = sysUserMapper.selectUserById(item.getUserId());
        item.setNickName(DataDesensitizationUtil.replaceLeftRight(sysUser.getUserName(), 3, 4));
        if (StringUtils.isNotBlank(sysUser.getAvatar())) {
            item.setAvatar(attachmentInfoService.getObjectUrl(sysUser.getAvatar()));
        }
    });
    return inviteRank;
}
```

## 功能验证

### 验证要点
1. **过滤功能**：确认指定前缀的用户不出现在排行榜中
2. **数据完整性**：确认其他用户的邀请数据统计正确
3. **性能表现**：确认查询性能没有明显下降
4. **兼容性**：确认不影响其他相关功能

### 测试建议
1. 创建测试用户，用户名以指定前缀开头
2. 让这些用户产生邀请数据
3. 调用邀请排行榜接口，验证这些用户是否被正确过滤
4. 验证正常用户的排行榜数据是否正确

## 影响范围

### 直接影响
- 邀请排行榜查询结果
- 相关的排行榜展示页面

### 间接影响
- 基于排行榜的奖励发放逻辑
- 排行榜相关的统计数据

## 注意事项

1. **数据一致性**：确保过滤逻辑在所有相关查询中保持一致
2. **性能监控**：关注新增JOIN查询对数据库性能的影响
3. **配置管理**：考虑将排除前缀列表配置化，便于后续维护
4. **日志记录**：建议在关键节点添加日志，便于问题排查

## 后续优化建议

1. **配置化管理**：将排除前缀列表移至配置文件或数据库配置表
2. **缓存优化**：对排行榜数据进行适当缓存，提升查询性能
3. **监控告警**：添加排行榜数据异常的监控和告警机制
4. **批量优化**：如果用户量大，考虑批量查询用户信息以提升性能

## 版本记录

| 版本 | 修改时间 | 修改人 | 修改内容 |
|------|----------|--------|----------|
| v1.0 | 2025-01-29 | 系统 | 初始实现邀请排行榜用户过滤功能 |

---

**文档维护：** 请在后续相关修改时及时更新本文档
