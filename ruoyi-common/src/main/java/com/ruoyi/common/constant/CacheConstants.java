package com.ruoyi.common.constant;

/**
 * 缓存的key 常量
 *
 * <AUTHOR>
 */
public class CacheConstants {
    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 登录用户id redis key 用于实现灵活控制多设备登录
     */
    public static final String LOGIN_USER_ID_KEY = "login_user_ids:";


    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 短信验证码 redis key
     */
    public static final String SMS_CODE_KEY = "sms_codes:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = "repeat_submit:";

    /**
     * 限流 redis key
     */
    public static final String RATE_LIMIT_KEY = "rate_limit:";

    /**
     * 登录账户密码错误次数 redis key
     */
    public static final String PWD_ERR_CNT_KEY = "pwd_err_cnt:";


    /**-------------------------------------业务key---------------------------------------*/

    /**
     * 库存队列
     */
    public static final String INVENTORY_QUEUE_KEY = "dig_inventory_queue_";

    /**
     * 资产各种类型数量前缀
     */
    public static final String INVENTORY_COUNT_KEY = "dig_inventory_count:";

    /**
     * 库存锁前缀
     */
    public static final String INVENTORY_LOCK_KEY = "dig_inventory_lock:";

    /**
     * 资产浏览量前缀
     */
    public static final String DIG_ASSET_BROWSE_COUNT_KEY = "dig_asset_browse_count:";

    /**
     * 资产详情前缀
     */
    public static final String DIG_ASSET_DETAIL_KEY = "dig_asset_detail:";

    /**
     * 资产规则前缀
     */
    public static final String DIG_ASSET_RULE_KEY = "dig_asset_rule:";

    /**
     * 资产规则优先购名单前缀
     */
    public static final String DIG_ASSET_RULE_PRIORITY_KEY = "dig_asset_rule_priority:";

    /**
     * 资产规则优先购限购数量前缀
     */
    public static final String DIG_ASSET_RULE_PRIORITY_LIMIT_KEY = "dig_asset_rule_priority_limit:";

    /**
     * 资产规则分时购限购数量前缀
     */
    public static final String DIG_ASSET_RULE_TIMESHARE_LIMIT_KEY = "dig_asset_rule_timeshare_limit:";

    /**
     * 创建订单队列Key
     */
    public static final String ORDER_QUEUE = "order_queue";
    /**
     * 创建订单队列Key
     */
    public static final String ORDER_TEMP_QUEUE = "order_temp_queue";

    /**
     * 错误订单队列Key
     */
    public static final String ORDER_QUEUE_ERROR = "order_queue_error";
    /**
     * 上链消息队列
     */
    public static final String CHAIN_QUEUE = "chain:queue";

    /**
     * 上链消息临时队列
     */
    public static final String CHAIN_TEMP_QUEUE = "chain:temp:queue";

    /**
     * 上链消息错误队列
     */
    public static final String CHAIN_QUEUE_ERROR = "chain:queue:error";

    /**
     * 活动排行榜数据Key
     */
    public static final String ACTIVITY_RANK_KEY = "dig_activity_rank:";


}
