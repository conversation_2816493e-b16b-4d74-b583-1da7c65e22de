package com.ruoyi.common.config;


import com.ruoyi.common.utils.SnowflakeIdWorker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @ClassName SnowflakeComponent
 * <AUTHOR> 102306
 * @Date 2022/3/2 10:13
 */

@Slf4j
@Component
public class SnowflakeComponent {

    @Value("${server.datacenterId}")
    private long datacenterId;

    @Value("${server.workId}")
    private long workId;

    private static volatile SnowflakeIdWorker instance;

    public SnowflakeIdWorker getInstance() {
        if (instance == null) {
            synchronized (SnowflakeIdWorker.class) {
                if (instance == null) {
                    log.info("when instance, workId = {}, datacenterId = {}", workId, datacenterId);
                    instance = new SnowflakeIdWorker(workId, datacenterId);
                }
            }
        }
        return instance;
    }

}
