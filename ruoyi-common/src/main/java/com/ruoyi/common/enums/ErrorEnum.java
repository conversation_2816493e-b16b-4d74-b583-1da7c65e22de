package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 统一异常枚举
 * <AUTHOR>
 * @date 2022/9/22 - 17:01
 */
@Getter
@AllArgsConstructor
public enum ErrorEnum {

    SUCCESS("10000", "ok"),
    NOT_OPEN_MERCHANT_NO_ERROR("400001", "企业未开通商户号"),
    INVALID_BINDID_ERROR("400002", "无效bindId"),
    ORDER_NO_REPEAT_ERROR("400003", "订单号重复"),
    ORDER_NO_PREFIX_MISMATCH_ERROR("400004", "订单号前缀不匹配"),
    PAY_TYPE_UNSUPPORTED_ERROR("400005", "不支持的支付类型"),
    ORDER_NON_EXISTENT_ERROR("400006", "订单不存在"),
    ORDER_STATUS_NOT_CREATED_ERROR("400007", "订单不是待支付状态"),
    BANK_BOUND_ERROR("400008", "当前银行卡已绑定"),
    NO_BANK_BIND_RECORD_ERROR("400009", "未找到绑定记录，请先绑卡"),
    BANK_NO_REPEAT_ERROR("400010", "不能重复绑定"),
    NOT_OPEN_CHINAUMS_NO_ERROR("400011", "企业银联商户未开通"),
    NOT_NOTIFY_URL_ERROR("400012", "企业未填写支付通知地址"),
    NOT_COMPANY_KEY_ERROR("400013", "企业公私钥信息不存在"),
    NOT_COMPANY_PUBLIC_KEY_ERROR("400014", "企业公钥不存在"),
    NOT_PLATFORM_PRIVATE_KEY_ERROR("400015", "平台私钥有误"),
    NOT_COMPANY_CODE_ERROR("400016", "企业编码不存在"),
    PAY_CHANNEL_UNSUPPORTED_ERROR("400017", "不支持的支付渠道"),
    PAY_REQUEST_ERROR("400040", "支付请求异常"),
    REFUND_REQUEST_ERROR("400041", "退款请求异常"),
    CHINAUMS_ORDER_ERROR("400042", "银联下单异常"),
    CHINAUMS_REFUND_ERROR("400043", "退款异常"),
    QUERY_ORDER_ERROR("400044", "订单查询异常"),
    ORDER_REPEAT_REFUND_ERROR("400045", "订单不是已支付状态"),
    NOT_NOTIFY_HELI_EMAIL_ERROR("400046", "未填写合利宝企业邮箱"),
    NOT_REFUND_ERROR("400047", "订单没有发起退款请求"),
    NOT_REFUND_NOTIFY_URL_ERROR("400048", "企业未填写退款通知地址"),
    NOT_UAC_ORDER_ERROR("400049", "只能退款云闪付支付的订单"),
    NOT_RATE_ERROR("400050", "没有配置分账费率"),
    NO_BIND_SUCCESS_ERROR("400051", "该卡未绑定成功，请先绑定成功再做操作"),
    BIND_ERROR("400060", "绑定失败，请稍后再试"),
    ISV_NOT_BINDID_ERROR("400061", "银行卡没有绑定，请先进行鉴权绑定"),
    NOT_MERCHANT_NET_ERROR("400062", "请先商户进件"),
    CNCONTRACT_QUERY_ERROR("400063", "签章查询失败，请稍后再试"),
    MERCHANT_NO_NET_REPEAT_ERROR("4000064", "当前商戶已进件，请上传商户资质"),
    MERCHANT_NO_NET_ERROR("4000065", "商戶进件失败，请稍后再试"),
    MERCHANT_NO_REPEAT_SIGN_CONTRACT_ERROR("4000066", "商戶已签约，不要重复签约"),
    MERCHANT_PIC_ERROR("4000067", "请上传身份证正反面或结算卡照片"),
    MERCHANT_NET_CALLBACK_URL_ERROR("400068", "商户进件回调地址不存在"),
    MERCHANT_SIGN_CALLBACK_URL_ERROR("400068", "商户签约回调地址不存在"),
    COMPANY_ORG_NO_NULL_ERROR("400069", "企业商户组织机构代码不能为空"),
    LICENSENO_ORG_NO_NULL_ERROR("400070", "企业商户营业执照号不能为空"),
    MERCHANT_COMPANY_PIC_ERROR("400071", "请上传身份证正反面/结算卡照片/营业执照"),
    NOT_CUSTOMER_RATE_ERROR("400072", "没有配置平台商户分账费率"),
    MERCHANT_NO_ERROR("4000073", "商户号不存在或者还未签约成功"),
    MERCHANT_NO_AUDIT_ERROR("4000074", "商戶进件中，等待审核"),
    MERCHANT_NO_REPEAT_NET_ERROR("4000075", "商戶已签约，不要重复进件"),
    BANK_INFO_MISMATCH_ERROR("4000076", "持卡人身份信息不匹配"),
    DATA_NON_EXISTENT_ERROR("4000077", "数据不存在"),
    SETTLEMENT_NOTIFY_URL_ERROR("4000078", "企业未填写结算通知地址"),
    PIC_BASE64_ERROR("4000079", "图片编码有误"),
    CHINAUMS_UPLOAD_ERROR("4000081", "资料上传失败，请稍后再试"),
    CHINAUMS_AUDIT_REPEAT_ERROR("4000082", "正在审核中，请勿重复提交"),
    PLEASE_COLLECTION_DATA("4000083", "请先采集资料"),
    BANK_ACCT_NO_REPEAT_ERROR("4000084", "开户行帐号已存在"),
    PLEASE_PIC_AGREEMENT_UPLOAD_DATA("4000085", "请先协议文件上传并且银行四要素实名检查"),
    SUB_ORDERS_IS_NOT_NULL("400086", "分账规则不能为空"),
    SUB_ORDERS_QUANTITY_CANNOT_EXCEED_3("400087", "分账规则数量不能超过3条"),
    SUB_ORDERS_TOTAL_AMOUNT_ERROR("400088", "分账规则总金额不等于订单金额"),
    SUBACCOUNTS_RULE_ERROR("400089", "分账规则金额有误"),
    HELI_SUB_ORDERS_IS_NOT_NULL("401086", "分账规则不能为空"),
    HELI_SUB_ORDERS_QUANTITY_CANNOT_EXCEED_3("401087", "分账规则数量不能超过3条"),
    LEDGER_AMOUNT_RULES_NOT_MATCH("401088", "分账金额规则不匹配"),
    REFUND_AMOUNT_AND_ORDER_AMOUNT_ATYPISM("401089", "退账金额和订单金额不一致"),
    EMAIL_CANNOT_REPEAT("401090", "分账邮箱不能重复"),

    SIGN_ERROR("400080", "签名失败"),
    SYSTEM_ERROR("500000", "系统异常"),
    ;

    private final String code;
    private final String msg;

}
