package com.ruoyi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ChinaumsEnum {

    STATUS_SUCCESS("SUCCESS"),

    STATUS_TIMEOUT("TIMEOUT"),

    STATUS_NO_ORDER("NO_ORDER"),

    STATUS_BAD_REQUEST("BAD_REQUEST"),

    STATUS_DUP_ORDER("DUP_ORDER"),

    STATUS_ORDER_PROCESSING("ORDER_PROCESSING"),

    STATUS_INVALID_ORDER("INVALID_ORDER");

    private final String status;

    @Getter
    @AllArgsConstructor
    public enum instMidEnum{
        INST_MID_H5("h5","H5DEFAULT"),

        INST_MID_APP("app","APPDEFAULT"),

        INST_MID_MINI("mini","MINIDEFAULT"),

        INST_MID_QRCODE("qrcode","QRPAYDEFAULT"),

        INST_MID_JS("js","YUEDANDEFAULT");

        private final String instMid;

        private final String type;
    }

    @Getter
    @AllArgsConstructor
    public enum tradeStatus{
        TRADE_SUCCESS,TRADE_CLOSED,WAIT_BUYER_PAY,UNKNOWN,TRADE_REFUND,NEW_ORDER
    }
}
