package com.ruoyi.quartz.task;

import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.issue.activity.service.IDigActivityService;
import com.ruoyi.issue.pay.service.DigAssetOrderService;
import com.ruoyi.issue.rule.domain.DigSaleRule;
import com.ruoyi.issue.rule.mapper.DigPrioritySnapshotMapper;
import com.ruoyi.issue.rule.service.DigSaleRuleService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.common.utils.StringUtils;

/**
 * 定时任务调度测试
 * 
 * <AUTHOR>
 */
@Component("ryTask")
@RequiredArgsConstructor
public class RyTask
{

    private final DigSaleRuleService digSaleRuleService;
    private final IDigActivityService digActivityService;
    private final DigAssetOrderService digAssetOrderService;
    public void ryMultipleParams(String s, <PERSON><PERSON><PERSON> b, <PERSON> l, Double d, Integer i)
    {
        System.out.println(StringUtils.format("执行多参方法： 字符串类型{}，布尔类型{}，长整型{}，浮点型{}，整形{}", s, b, l, d, i));
    }

    public void ryParams(String params)
    {
        System.out.println("执行有参方法：" + params);
    }

    public void ryNoParams()
    {
        System.out.println("执行无参方法");
    }

    /**
     * 优先购快照任务
     */
    public void ryPrioritySnapshot(Integer ruleId) {
        digSaleRuleService.snapshotPriority(Long.valueOf(ruleId));
    }

    /**
     * 活动抽奖任务
     */
    public void ryActivityLottery(Long activityId) {digActivityService.drawActivityWithLock(activityId);}

    /**
     * 检查超时订单任务
     */
    public void ryCheckOrder() {digAssetOrderService.checkOverTimeOrder();}

}
