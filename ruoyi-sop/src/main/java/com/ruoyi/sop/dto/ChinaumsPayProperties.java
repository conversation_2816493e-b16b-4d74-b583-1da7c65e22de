package com.ruoyi.sop.dto;



import com.ruoyi.sop.config.YamlPropertySourceFactory;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020-11-16
 */
@Data
@Component
@ConfigurationProperties(prefix = "chinaums")
@PropertySource(value = {"classpath:chinaums.yml"},factory = YamlPropertySourceFactory.class)
public class ChinaumsPayProperties {


    private String tokenUrl;
    private String appId;
    private String appKey;
    private String mid;
    private String submid;
    private String tid;
    private String baseUrl;
    private String ctbBaseUrl;
    private String gatewayBaseUrl;
    private String queryUrl;
    private String systemId;
    private String qrCodeUrl;
    private String qrQueryUrl;
    private String notifyUrl;
    private String secret;
    private String accesserId;
    private String key;
    private String autoRegUrl;
    private String regWebUrl;
    private String regH5Url;
    private String bgImgUrl;
    private String shopBgUrl;
    private String shopBgBase64;
    private String h5FaceDetectUrl;
    private String desKey;
    private String refundUrl;
    private String closeUrl;
    private String agentSysId;

    private String queryWithdrawBalanceUrl;
    private String agentPayUrl;
    private String queryAgentPayStatusUrl;
    private String sysId;
    private String processWithdrawUrl;
    /**
     * 支付宝支付参数
     */
    private ChinaumsAliPayParam aliPayParam;

    /**
     * 微信支付参数
     */
    private ChinaumsWxPayParam wxPayParam;
    /**
     * 云闪付支付参数
     */
    private ChinaumsUacPayParam uacPayParam;
    /**
     * 银行实名认证
     */
    private ChinaumsBankVerify bankVerify;
}

