package com.ruoyi.sop.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 银联自定义退款请求参数
 */
@Data
@ApiModel(value = "代付请求参数")
public class AgentPay {

    @ApiModelProperty(value = "订单号", required = true)
    @NotEmpty(message = "订单号不能为空")
    private String orderNo;

    @ApiModelProperty(value = "商户号", required = true)
//    @NotEmpty(message = "商户号不能为空")
    private String merchantNo;

    @ApiModelProperty(value = "付款金额,单位分（为付款明细金额之和）", required = true)
//    @NotEmpty(message = "付款金额不能为空")
    private String withdrawAmt;

//    @ApiModelProperty(value = "收款账号", required = true)
//    @NotEmpty(message = "收款账号不能为空")
//    private String revAcctNo;
//
//    @ApiModelProperty(value = "收款账户名", required = true)
//    @NotEmpty(message = "收款账户名不能为空")
//    private String revAcctName;

    @ApiModelProperty(value = "提现附言")
//    @Max(value = 20, message = "提现附言不能超过20个字符")
    private String postscript;

    @ApiModelProperty(value = "交易日期")
    private String transDate;

}
