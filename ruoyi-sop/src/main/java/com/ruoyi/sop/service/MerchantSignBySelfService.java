package com.ruoyi.sop.service;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.digest.HmacAlgorithm;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import com.ruoyi.common.config.SnowflakeComponent;
import com.ruoyi.sop.dto.ChinaumsMerchantPicUploadDto;
import com.ruoyi.sop.dto.ChinaumsPayProperties;
import com.ruoyi.common.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;

/**
 * @ClassName merchantSignBySelfService
 * <AUTHOR> 102306
 * @Date 2022/5/13 17:47
 */

@Component
@Slf4j
@RequiredArgsConstructor
public class MerchantSignBySelfService {

    private final SnowflakeComponent snowflakeComponent;

    private final ChinaumsPayProperties chinaumsPayProperties;


    /**
     * 小微商户文件资料
     */
    private static final HashMap<String, String> map  = Maps.newHashMap();

    /**
     * 企业或者个人商户文件资料
     */
    private static final HashMap<String, String> enterpriseMap = Maps.newHashMap();

    static {
        map.put("0001","法人身份证");
        map.put("0011","身份证反面");
        map.put("0020","其他小微商户证明材料");
        map.put("0025","银行卡正面照");
        map.put("0026","银行卡背面照");

        enterpriseMap.put("0001","法人身份证");
        enterpriseMap.put("0011","身份证反面");
        enterpriseMap.put("0002","商户营业执照");
        enterpriseMap.put("0005","门头照片");
        enterpriseMap.put("0015","室内照片");
        enterpriseMap.put("0006","对公结算材料");
        enterpriseMap.put("0025","银行卡正面照");
        enterpriseMap.put("0099","其他材料");
//        enterpriseMap.put("0034","app店铺截图");
    }

    /**
     * 统一请求
     * @param jsonStr
     * @return
     */
    public String unifiedPost(String jsonStr){
        JSONObject object = new JSONObject();
        // 打印日志时将pic_base64 的内容截取，不影响原数据
        String logjsonstr = jsonStr.replaceAll("pic_base64\":\"(.*?)\"", "pic_base64\":\"数据太长不展示\"");
        log.info("向银联发起-请求参数:{}", logjsonstr);

        object.put("json_data",SecureUtil.desede(chinaumsPayProperties.getKey().getBytes(StandardCharsets.UTF_8)).encryptHex(jsonStr));
        object.put("sign_data", SecureUtil.sha256(jsonStr));
        object.put("accesser_id",chinaumsPayProperties.getAccesserId());
        // 打印日志时将json_data 的内容截取，不影响原数据
        String logobjectstr = object.toString().replaceAll("json_data\":\"(.*?)\"", "json_data\":\"数据太长不展示\"");
        log.info("向银联发起-请求报文:{}", logobjectstr);
        String result;
        try{
            result = HttpUtil.post(chinaumsPayProperties.getAutoRegUrl(), object,8000);
            log.info("银联返回-请求结果：{}",result);
        }catch (Exception e){
            throw new ServiceException("请求超时，请稍后重试");
        }

        if(result == null){
            throw new ServiceException("系统异常");
        }
        final JSONObject o = JSONUtil.parseObj(result);
        if(!(o.getStr("res_code").equals("0000") || o.getStr("res_code").equals("1057"))){
            throw new ServiceException(o.getStr("res_msg"));
        }
        return result;
    }


    /**
     *  文件上传
     */
    public String uploadPic(ChinaumsMerchantPicUploadDto picUploadDto){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("service","pic_upload");
        jsonObject.put("accesser_id",chinaumsPayProperties.getAccesserId());
        jsonObject.put("sign_type","SHA-256");
        jsonObject.put("request_date", DateUtil.format(new Date(),"yyyyMMddhhmmss"));
        jsonObject.put("request_seq", picUploadDto.getRequestSeq());
        jsonObject.put("pic_base64",picUploadDto.getBase64());
        return unifiedPost(JSONUtil.toJsonStr(jsonObject));
    }

    /**
     *  所属支行查询接口
     * @return
     */
    public String getBranchBankList(String areaCode,String key, String requestSeq){
        JSONObject object = this.createSameJson("branch_bank_list", requestSeq);
        object.put("areaCode",areaCode);
        object.put("key",key);
        return this.unifiedPost(JSONUtil.toJsonStr(object));
    }

    /**
     * 入网状态查询
     * @return
     */
    public String applyQry(String reqId){
        JSONObject object = this.createSameJson("apply_qry","apply_" + snowflakeComponent.getInstance().nextId());
        object.put("ums_reg_id",reqId);
        return this.unifiedPost(JSONUtil.toJsonStr(object));
    }

    /**
     * 前台签约接口
     * @return
     */
    public String agreementSign(String umsRegId){
        return createSignJson(umsRegId,"agreement_sign","agreement_" + snowflakeComponent.getInstance().nextId());
    }
    /**
     * 商户变更签约接口
     */
    public String alterSign(String umsRegId){
        return createSignJson(umsRegId,"alter_sign","alterSigncomplex_alter_acctinfo_");
    }

    /**
     * 3.11  变更入网状态查询接口 手动查询
     * @param umsRegId 自助签约平台流水号
     * @return 平台响应
     */
    public String alterQry(String umsRegId){
        return createSignJson(umsRegId, "alter_qry", "alter_qry_");
    }


    /**
     * 自助签约拼接url
     */
    private String createSignJson(String umsRegId,String serviceName,String seqPrefix){
        return this.createSignJson(umsRegId,serviceName,seqPrefix,null);
    }


    /**
     * 自助签约拼接url
     * @param umsRegId umsRegId
     * @param serviceName serviceName
     * @param seqPrefix seqPrefix
     * @param dataType 适配 dataDownload 方法
     * @return
     */
    private String createSignJson(String umsRegId,String serviceName,String seqPrefix,String dataType){
        JSONObject json  = this.createSameJson(serviceName,seqPrefix);
        if (ObjectUtils.isNotEmpty(umsRegId)) {
            json.put("ums_reg_id",umsRegId);
        }
        if (ObjectUtils.isNotEmpty(dataType)) {
            json.put("data_type",dataType);
        }
        String jsonStr = JSONUtil.toJsonStr(json);
        JSONObject object = new JSONObject();
        object.put("json_data",SecureUtil.desede(chinaumsPayProperties.getKey().getBytes(StandardCharsets.UTF_8)).encryptHex(jsonStr));
        object.put("sign_data", SecureUtil.sha256(jsonStr));
        object.put("accesser_id",chinaumsPayProperties.getAccesserId());
        return chinaumsPayProperties.getAutoRegUrl()+"?"+MapUtil.join(object, "&", "=");
    }

    /**
     * 平台类全前台接入接口
     * @param userId
     * @return
     */
    public String selfSignWeb(Integer userId,boolean webPlat){
        JSONObject object  = this.createSameJson("merchant_reg","merchant_");
        object.put("accesser_acct","bmark_"+userId+"_"+snowflakeComponent.getInstance().nextId());
        String url = webPlat ?chinaumsPayProperties.getRegWebUrl():chinaumsPayProperties.getRegH5Url();
        return url+"?"+ MapUtil.join(object, "&", "=");
    }

//
//    /**
//     * 银行卡四要素校验
//     * @return
//     */
//    public R bankNoVerify(String cardNo, String certNo, String phoneNo, String name){
//        HttpServletRequest request = ((ServletRequestAttributes) Objects
//                .requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
//        String clientIp = ServletUtil.getClientIP(request);
//        clientIp = clientIp.replaceAll("(\\d{1,3})", "00$1");
//        clientIp = clientIp.replaceAll("0*(\\d{3})", "$1");
//        Map<String,String> body = new HashMap<>();
//        body.put("cardNo", cardNo);
//        body.put("certType", "01");
//        if(StrUtil.isNotBlank(certNo)){
//            body.put("certNo", certNo);
//        }
//        if(StrUtil.isNotBlank(phoneNo)){
//            body.put("phoneNo", phoneNo);
//        }
//        if(StrUtil.isNotBlank(name)){
//            body.put("name", name);
//        }
//        body.put("personalMandate", "1");
//        body.put("sceneId","25");
//        body.put("appName","04斑马版权");
//        body.put("ipType","04");
//        body.put("sourceIp",clientIp);
//        body.put("protocolVersion","bmark-v1.0.0");
//        body.put("protocolNo","verify_"+ snowflakeComponent.getInstance().nextId());
//        log.info("银联四要素请求参数:{}", body);
//        Map<String,String> data = new HashMap<>();
//        String encData= SM2.sm2Encrypt(JSONUtil.toJsonStr(body), chinaumsPayProperties.getBankVerify().getReqPubKey());
//        data.put("data",encData);
//        log.info("银联四要素请求加密后:{}", data);
//        try{
//            String token =  client.getToken("bankVerify");
//            String authorization = "OPEN-ACCESS-TOKEN AccessToken="+token;
//            String result = HttpRequest.post(chinaumsPayProperties.getBankVerify().getUrl())
//                    .header(HttpHeaders.CONTENT_TYPE, "application/json;charset=UTF-8")
//                    .header(HttpHeaders.ACCEPT,"application/json")
//                    .header("Authorization", authorization)
//                    .body(JSONUtil.toJsonStr(data))
//                    .timeout(8000)
//                    .execute()
//                    .body();
//            JSONObject rspMap = JSONUtil.parseObj(result);
//            log.info("响应报文：{}",rspMap);
//            if(!rspMap.getStr("errCode").equals("********")){
//                throw new ServiceException(rspMap.getStr("errInfo"));
//            }
//            String rawData = SM2.sm2Decrypt(rspMap.getStr("data"), chinaumsPayProperties.getBankVerify().getResPriKey());
//            log.info("解密后的报文：{}",rawData);
//            final JSONObject object = JSONUtil.parseObj(rawData);
//            if(object.getStr("respCode").equals("00")){
//                return R.ok();
//            }else {
//                // 如果返回验证不一致
//                if(object.getStr("detailRespCode").equals("2319")){
//                    throw new ServiceException("银行卡、身份信息、手机号信息不一致，请检查");
//                }else {
//                    throw new ServiceException(object.getStr("detailRespMsg"));
//                }
//            }
//        }catch(Exception e){
//            log.error("发生异常：{}",e.getMessage());
//            throw new ServiceException(e.getMessage());
//        }
//    }

//    /**
//     * h5 人脸活体检测
//     * @return url
//     */
//    public String h5FaceDetect(String returnUrl,String name,String certNo){
//        JSONObject object = new JSONObject();
//        object.put("returnUrl", returnUrl);
//        object.put("type","action");
//        object.put("bizType",0);
//        DESede desede = SecureUtil.desede(chinaumsPayProperties.getDesKey().getBytes(StandardCharsets.UTF_8));
//        object.put("name",desede.encryptHex(name));
//        object.put("certNo",desede.encryptHex(certNo));
//        String content = JSONUtil.toJsonStr(object);
//        JSONObject requestObject = new JSONObject();
//        requestObject.put("authorization","OPEN-FORM-PARAM");
//        requestObject.put("appId",chinaumsPayProperties.getBankVerify().getAppId());
//        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
//        requestObject.put("timestamp",timestamp);
//        String nonce = WxPayKit.generateStr();
//        requestObject.put("nonce",nonce);
//        requestObject.put("content", URLUtil.encodeAll(content));
//        requestObject.put("signature", createSignature(timestamp,nonce,content));
//        return chinaumsPayProperties.getH5FaceDetectUrl()+"?"+ MapUtil.join(requestObject, "&", "=");
//    }


//    /***
//     * 人证比对-验证该身份证号+姓名和上送的图片是否为同一人
//     * @param name
//     * @param certNo
//     * @param pic  base64格式的人脸图片  30kB
//     * @return
//     */
//    public String faceRecognition(String name,String certNo,String pic){
//        Map<String,String> body = new HashMap<>();
//        body.put("name", name);
//        body.put("certNo", certNo);
//        body.put("pic", pic);
//        body.put("queryId",chinaumsPayProperties.getBankVerify().getAppId()+"-"+AES.generateString(32)+"-"+String.format("%014d", System.currentTimeMillis()));
//        log.info("人证比对请求参数:{}", body);
//        try{
//            String token =  client.getToken("bankVerify");
//            String authorization = "OPEN-ACCESS-TOKEN AccessToken="+token;
//            String result = HttpRequest.post(chinaumsPayProperties.getBankVerify().getCompareUrl())
//                    .header(HttpHeaders.CONTENT_TYPE, "application/json;charset=UTF-8")
//                    .header(HttpHeaders.ACCEPT,"application/json")
//                    .header("Authorization", authorization)
//                    .body(JSONUtil.toJsonStr(body))
//                    .timeout(8000)
//                    .execute()
//                    .body();
//            JSONObject rspMap = JSONUtil.parseObj(result);
//            log.info("响应报文：{}",rspMap);
//            if(!rspMap.getStr("errCode").equals("NO_MATCH")){
//                return "人证比对失败";
//            }else if(rspMap.getStr("errCode").equals("0000")){
//                int score = rspMap.getInt("score");
//                if (score >= 80){
//                    return null;
//                }else {
//                    return "人证比对不匹配";
//                }
//            }else {
//                return rspMap.getStr("errInfo");
//            }
//        }catch(Exception e){
//            log.error("发生异常：{}",e.getMessage());
//            return e.getMessage();
//        }
//    }

//    /***
//     * 企业法人信息校验
//     * @param enterpriseName
//     * @param enterpriseNo
//     * @param legalPersonName
//     * @param legalPersonCert
//     * @return
//     */
//    public String legalPersonCheck(String enterpriseName,String enterpriseNo,String legalPersonName,String legalPersonCert){
//        Map<String,String> body = new HashMap<>();
//        body.put("serviceType", "1");
//        body.put("enterpriseName", enterpriseName);
//        body.put("enterpriseNo", enterpriseNo);
//        body.put("legalPersonName", legalPersonName);
//        body.put("legalPersonCert", legalPersonCert);
//        log.info("法人信息验证请求参数:{}", body);
//        Map<String,String> data = new HashMap<>();
//        String encData= SM2.sm2Encrypt(JSONUtil.toJsonStr(body), chinaumsPayProperties.getBankVerify().getLegalPersonCheckUrl());
//        data.put("data",encData);
//        log.info("法人信息验证请求加密后:{}", data);
//        try{
//            String token =  client.getToken("bankVerify");
//            String authorization = "OPEN-ACCESS-TOKEN AccessToken="+token;
//            String result = HttpRequest.post(chinaumsPayProperties.getBankVerify().getUrl())
//                    .header(HttpHeaders.CONTENT_TYPE, "application/json;charset=UTF-8")
//                    .header(HttpHeaders.ACCEPT,"application/json")
//                    .header("Authorization", authorization)
//                    .body(JSONUtil.toJsonStr(data))
//                    .timeout(8000)
//                    .execute()
//                    .body();
//            JSONObject rspMap = JSONUtil.parseObj(result);
//            log.info("响应报文：{}",rspMap);
//            if(!rspMap.getStr("errCode").equals("********")){
//                throw new ServiceException(rspMap.getStr("errInfo"));
//            }
//            String rawData = SM2.sm2Decrypt(rspMap.getStr("data"), chinaumsPayProperties.getBankVerify().getResPriKey());
//            log.info("解密后的报文：{}",rawData);
//            final JSONObject object = JSONUtil.parseObj(rawData);
//            if(object.getStr("respCode").equals("00")){
//                return null;
//            }else {
//                return object.getStr("respMsg");
//            }
//        }catch(Exception e){
//            log.error("发生异常：{}",e.getMessage());
//            throw new ServiceException(e.getMessage());
//        }
//    }

    private String createSignature(String timestamp,String nonce,String content){
        return  URLUtil.encodeAll(Base64.encode(SecureUtil.hmac(HmacAlgorithm.HmacSHA256, chinaumsPayProperties.getBankVerify().getAppKey())
                .digest(chinaumsPayProperties.getBankVerify().getAppId() + timestamp + nonce + DigestUtil.sha256Hex(content))));
    }



    private JSONObject createSameJson(String service,String requestSeq){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("service",service);
        jsonObject.put("accesser_id",chinaumsPayProperties.getAccesserId());
        jsonObject.put("sign_type","SHA-256");
        jsonObject.put("request_date", DateUtil.format(new Date(),"yyyyMMddhhmmss"));
        jsonObject.put("request_seq", requestSeq);
        return jsonObject;
    }

    /**
     *  解密数据
     * @param object
     * @return
     */
    public String decryptData(JSONObject object) {
        String jsonData = SecureUtil.desede(chinaumsPayProperties.getKey().getBytes(StandardCharsets.UTF_8)).decryptStr(object.getStr("json_data"));
        String  signData = object.getStr("sign_data");
        if(!signData.equals(SecureUtil.sha256(jsonData))){
            return null;
        }
        return jsonData;
    }

    /**
     * 发起对公账户验证交易接口
     */
    public String requestAccountVerify(String bankAcctNo,String umsRegId){
        JSONObject json = this.createSameJson("request_account_verify", "verify_" + snowflakeComponent.getInstance().nextId());
        json.put("company_account",bankAcctNo);
        json.put("ums_reg_id",umsRegId);
        // 银行四要素实名检查
        return this.unifiedPost(JSONUtil.toJsonStr(json));
    }
    /**
     *  对公账户认证接口
     *  bankAcctNo 对公账号
     *  transAmt 验证金额 以分为单位
     */
    public String companyAccountVerify(String bankAcctNo,String transAmt,String umsRegId ){
        JSONObject json = this.createSameJson("company_account_verify", "verify_" + snowflakeComponent.getInstance().nextId());
        json.put("company_account",bankAcctNo);
        json.put("ums_reg_id",umsRegId);
        json.put("trans_amt",transAmt);
        // 银行四要素实名检查
        return this.unifiedPost(JSONUtil.toJsonStr(json));
    }

    /**
     *  进件状态查询接口
     */
    public String complexApplyQry(String umsRegId) {
        JSONObject json = this.createSameJson("apply_qry", "verify_" + snowflakeComponent.getInstance().nextId());
        json.put("ums_reg_id",umsRegId);
        return this.unifiedPost(JSONUtil.toJsonStr(json));
    }

    /**
     *  查询终端列表
     */
    public String terminalsQry(String merNo) {
        JSONObject json = this.createSameJson("terminals_qry", "terminals_qry_" + snowflakeComponent.getInstance().nextId());
        json.put("merchantNo",merNo);
        return this.unifiedPost(JSONUtil.toJsonStr(json));
    }
}
