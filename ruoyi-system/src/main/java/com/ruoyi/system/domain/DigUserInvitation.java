package com.ruoyi.system.domain;

import java.time.LocalDateTime;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;

/**
 * 用户邀请码对象 dig_user_invitation
 * 
 * <AUTHOR>
 * @date 2025-01-06
 */
@Data
@ApiModel(value = "用户邀请码对象")
public class DigUserInvitation
{
    private static final long serialVersionUID = 1L;

    /** 邀请ID */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "邀请ID")
    private Long inviteId;

    /** 用户ID */
    @Excel(name = "用户ID")
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /** 唯一邀请码 */
    @Excel(name = "邀请码")
    @ApiModelProperty(value = "唯一邀请码")
    private String inviteCode;

    /** 邀请人ID(可为空) */
    @Excel(name = "邀请人ID")
    @ApiModelProperty(value = "邀请人ID(可为空)")
    private Long inviterId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;


}